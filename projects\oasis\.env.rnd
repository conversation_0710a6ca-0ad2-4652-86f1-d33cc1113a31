# 测试环境走分发配置
VITE_GLOB_API_URL=/api

# ---- 子系统 ----
# 【管理后台】入口
VITE_SUB_SYS_URL_ADMIN=https://forgeon-sys-admin-test.hypergryph.net
# 【管理后台】API
VITE_BASE_API_ORIGIN_FORGEON_ADMIN=https://forgeon-admin-tech-center-efficiency-test.hypergryph.net
# 【AIGC】入口
VITE_SUB_SYS_URL_AIGC=//forgeon-sys-aigc-test.hypergryph.net
# 【AIGC】API
VITE_BASE_API_ORIGIN_AIGC=https://api-aigc-biz-tech-center-efficiency-test.hypergryph.net
# 【云真机平台】API
VITE_BASE_API_ORIGIN_FORGEON_MTL=https://gateway-tech-center-testdev-test.hypergryph.net/sonic
# 【权限管理中心】入口
VITE_SUB_SYS_URL_PERMISSION_CENTER=https://forgeon-sys-permission-center-test.hypergryph.net
# 【权限管理中心】API
VITE_BASE_API_ORIGIN_PERMISSION_CENTER=https://auth-tech-center-efficiency-test.hypergryph.net
# 【分支合并平台】入口
VITE_BASE_API_ORIGIN_CONFLUX=https://forgeon-sys-conflux-test.hypergryph.net
# 【DevGuard】入口
VITE_SUB_SYS_URL_DEVGUARD=https://forgeon-sys-devguard-test.hypergryph.net