import type { ICommonMenuItem } from './types';
import { PlatformEnterPoint, PlatformRoutePath } from '../route-path';
import { DevelopMenuConfig } from './projects/develop';
import { TestMenuConfig } from './projects/test';
import { ManageMenuConfig } from './projects/management';
import { AIMenuConfig } from './projects/ai';
import { ForgeonTitleMap } from '../route-path/titleMap';

import ModuleHome from '../../assets/svg/icons/module-home.svg?component';
import ModuleDev from '../../assets/svg/icons/module-dev.svg?component';
import ModuleTest from '../../assets/svg/icons/module-test.svg?component';
import ModuleAI from '../../assets/svg/icons/module-ai.svg?component';
import ModuleEfficacy from '../../assets/svg/icons/module-efficacy.svg?component';

/**
 * Forgeon侧边栏通用配置
 */
export const ModulesMenuConfig: ICommonMenuItem[] = [
  {
    title: ForgeonTitleMap[PlatformEnterPoint.ModuleHome],
    key: PlatformEnterPoint.ModuleHome,
    name: PlatformEnterPoint.Home,
    svgIcon: () => <ModuleHome />,
    activeIcon: () => <ModuleHome class="c-FO-Content-Icon1" />,
    prefix: [
      PlatformRoutePath.Home,
    ],
    children: [],
  },
  {
    title: ForgeonTitleMap[PlatformEnterPoint.ModuleDevelop],
    key: PlatformEnterPoint.ModuleDevelop,
    svgIcon: () => <ModuleDev />,
    activeIcon: () => <ModuleDev class="c-FO-Content-Icon1" />,
    prefix: [
      '/test/gamePackage',
      '/p4Depots',
      '/devGuard',
      '/toolkit',
      '/p4Trains',
      '/p4Onboarding',
      '/tcp4t',
      '/p4Pass',
      '/navigations',
      '/instructions/combinations',
      '/messageCenter',
    ],
    children: DevelopMenuConfig,
  },
  {
    title: ForgeonTitleMap[PlatformEnterPoint.ModuleTest],
    key: PlatformEnterPoint.ModuleTest,
    svgIcon: () => <ModuleTest />,
    activeIcon: () => <ModuleTest class="c-FO-Content-Icon1" />,
    prefix: [
      '/performance',
      '/perfdeep',
      '/protocol',
      '/automation',
      '/crashCollect',
      '/deptAsset',
    ],
    children: TestMenuConfig,
  },
  {
    title: ForgeonTitleMap[PlatformEnterPoint.SysAigc],
    key: PlatformEnterPoint.SysAigc,
    svgIcon: () => <ModuleAI />,
    prefix: ['/aigc'],
    activeIcon: () => <ModuleAI class="c-FO-Content-Icon1" />,
    children: AIMenuConfig,
  },
  {
    title: ForgeonTitleMap[PlatformEnterPoint.ModuleEfficacy],
    key: PlatformEnterPoint.ModuleEfficacy,
    name: PlatformEnterPoint.EfficacyPreview,
    svgIcon: () => <ModuleEfficacy />,
    activeIcon: () => <ModuleEfficacy class="c-FO-Content-Icon1" />,
    prefix: ['/efficacy'],
    children: [],
  },
  {
    title: ForgeonTitleMap[PlatformEnterPoint.ModuleAccount],
    key: PlatformEnterPoint.ModuleAccount,
    name: PlatformEnterPoint.AccountSettings,
    prefix: [
      PlatformRoutePath.AccountSettings,
    ],
    children: [],
  },
  {
    title: ForgeonTitleMap[PlatformEnterPoint.ModuleManage],
    key: PlatformEnterPoint.ModuleManage,
    prefix: [],
    children: ManageMenuConfig,
  },
  {
    title: ForgeonTitleMap[PlatformEnterPoint.SysAdmin],
    key: PlatformEnterPoint.SysAdmin,
    name: PlatformEnterPoint.SysAdmin,
    prefix: [PlatformRoutePath.SysAdmin],
    children: [],
    permissionDeclare: {
      scope: PlatformEnterPoint.SysAdmin,
      any: [],
    },
    openInNewTab: true,
  },
];
