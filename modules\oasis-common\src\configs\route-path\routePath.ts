export enum PlatformRoutePath {
  UnrealCase = '/unreal/case',
  UnrealCaseDetail = '/unreal/case/detail/:id',
  Services = '/services',
  CrashCollect = '/crashCollect',
  CrashDetail = '/crashCollect/detail/:recordID',
  CrashClassDetail = '/crashCollect/classDetail/:categoryID',
  ProtocolTest = '/protocol',
  ProtocolTestDevices = '/protocol/devices',
  ProtocolTestDetail = '/protocol/devices/:id',
  ProtocolTestHistory = '/protocol/devices/history',
  Automation = '/automation',
  AutomationReport = '/automation/report',
  AutomationTask = '/automation/task',
  AutomationSet = '/automation/set',
  Performance = '/performance',
  PerformanceCase = '/performance/case',
  PerformanceCaseDetail = '/performance/case/detail/:id',
  PerformanceCaseCompare = '/performance/case/compare',
  PerformanceReference = '/performance/reference',
  PerformanceHeatMap = '/performance/heatMap',
  PerformanceHeatMapList = '/performance/heatMap/list',
  PerformanceHeatMapSettings = '/performance/heatMap/settings',
  PerfdeepCase = '/perfdeep/case',
  PerfdeepCaseDetail = '/perfdeep/case/detail/:id',
  PerformanceMap = '/performance/map',
  Secure = '/secure',
  SecureProtections = '/secure/protections',
  BugRobot = '/bugRob',
  BugRobotChats = '/bugRobot',
  PerformanceCard = '/performance/card',
  PerformanceCardTrend = '/performance/card/trend',
  PerformanceCardCompare = '/performance/card/compare',
  PerfdeepCardTrend = '/perfdeep/card/trend',
  PerfdeepCardCompare = '/perfdeep/card/compare',
  DevGuard = '/devGuard',
  P4Training = '/p4Training',
  P4Depots = '/p4Depots',
  P4PermissionManagement = '/p4Depots/:id/streams/:stream_id',
  P4ClLabelManagement = '/p4Depots/:id/streams/:stream_id/clLabel',
  P4CommitParamsConfigs = '/p4Depots/:id/streams/:stream_id/commitParams',
  P4CommitTagConfigs = '/p4Depots/:id/streams/:stream_id/commitTag',
  P4CompleteNoticeManagement = '/p4Depots/:id/streams/:stream_id/completeNotice',
  P4FormDiff = '/p4Depots/:id/streams/:stream_id/formDiff',
  P4FormDiffDetail = '/p4Depots/:id/streams/:stream_id/formDiff/:diff_id',
  DM01GroupManagement = '/p4Depots/:id/group',
  SwarmSettings = '/p4Depots/:id/streams/:stream_id/swarmSettings',
  ResourceCheck = '/p4Depots/:id/streams/:stream_id/resourceCheck',
  ResourceCheckRules = '/p4Depots/:id/streams/:stream_id/resourceCheck/rules',
  ResourceCheckItems = '/p4Depots/:id/streams/:stream_id/resourceCheck/items',
  ResourceCheckReports = '/p4Depots/:id/streams/:stream_id/reports',
  ResourceCheckReportDetail = '/p4Depots/:id/streams/:stream_id/reports/detail/:report_id',
  ResourceCheckReportCompare = '/p4Depots/:id/streams/:stream_id/reports/compare',
  PerforceManagement = '/p4Depots/:id/perforceManagement',
  P4Triggers = '/p4Depots/:id/p4Triggers',
  P4MemberManagement = '/devGuard/members',
  P4GroupManagement = '/devGuard/group',
  P4CustomGroupManagement = '/devGuard/group/custom',
  P4LdapGroupManagement = '/devGuard/group/ldap',
  ResourceCheckOld = '/resourceCheck',
  ResourceCheckSwitchesOld = '/resourceCheck/switches',
  ResourceCheckRulesOld = '/resourceCheck/rules',
  ResourceCheckIndexOld = '/resourceCheck/index',
  Gitlab = '/gitlab',
  ******************** = '/gitlab/:id/branch/:branch_id/gitlabSettings',
  GitlabReviewList = '/gitlab/:id/branch/:branch_id/reviewList',
  GitlabSubmitDescriptionSpecifications = '/gitlab/:id/branch/:branch_id/SubmitDescription',
  Test = '/test',
  GamePackage = '/test/gamePackage',
  GamePackageDoctor = '/test/gamePackage/:id/doctor/:doctorID',
  GamePackageDoctorCompare = '/test/gamePackage/:id/doctor/compare',
  GamePackageSettings = '/test/gamePackage/settings',
  GameArchive = '/test/gameArchive',
  GamePackageCard = '/test/gamePackageCard',
  GamePackageDoctorProportion = '/test/gamePackage/:id/doctor/:doctorID/proportion',
  GamePackageDoctorTrend = '/test/gamePackage/:id/doctors/trend',
  GamePackageVersionSimple = '/test/gamePackage/:id/version/:versionID/simple',
  Tool = '/tool',
  Toolkit = '/toolkit',
  ToolkitDetail = '/toolkit/detail/:id',
  ToolkitPackageSettings = '/toolkit/settings',
  Tracking = '/tracking',
  TrackingAnalysis = '/tracking/analysis',
  TrackingAnalysisSettings = '/tracking/settings',
  Oasis = '/oasis-tools',
  Instructions = '/instructions',
  InstructionCombinations = '/instructions/combinations',
  InstructionComponents = '/instructions/components',
  ToolNavigations = '/navigations',
  GroupChat = '/groupChat',
  ToolGroupChat = '/toolGroupChat',
  ToolWorkPlatform = '/workPlatform',
  JenkinsAutoTask = '/workPlatform/jenkinsAutoTask',
  OriginInstructions = '/toolkit/instructions',
  DeptAsset = '/deptAsset',
  DeptAssetApplyManagement = '/deptAsset/apply',
  CloudDevice = '/deptAsset/cloud',
  CloudDeviceDetail = '/deptAsset/cloud/detail/:id',
  AssetLib = '/assetLib',
  MaterialLib = '/materialLib',
  HDALib = '/HDALib',
  MessageCenter = '/messageCenter',
  MessageTemplate = '/messageCenter/template',
  TCP4T = '/tcp4t',
  P4Trains = '/p4Trains',
  P4Pass = '/p4Pass',
  P4Onboarding = '/p4Onboarding',
  P4TouchProcess = '/p4TouchProcess',
  System = '/system',
  ResourceCheckSwitchesTemplate = '/system/resourceCheck/switches',
  ResourceCheckTemplate = '/system/resourceCheck/template',
  DeptAssetsManagement = '/system/deptAssets',
  DeviceManagement = '/system/deptAssets/device',
  DeviceManagementAdminConfig = '/system/deptAssets/device/adminConfig',
  DeviceManagementFaultList = '/system/deptAssets/device/faultList',
  DeviceManagementLogs = '/system/deptAssets/device/logs',
  OrganizationManagement = '/system/organization',
  DeptManagement = '/system/organization/dept',
  DeptMemberManagement = '/system/organization/member',
  P4TriggersSettings = '/system/p4TriggersSettings',
  P4TriggersOverview = '/system/p4TriggersSettings/overview',
  P4TriggersParamsSettings = '/system/p4TriggersSettings/params',
  PerforceSettings = '/system/perforceSettings',
  SystemPerforceManagement = '/system/perforceSettings/management',
  PerforceServersSettings = '/system/perforceSettings/servers',
  PerforceAccessLevelsSettings = '/system/perforceSettings/accessLevels',
  SecureSettings = '/system/secure',
  SecureChannelsSettings = '/system/secure/channels',
  TCP4TSettings = '/system/tcp4t',
  TCP4TSelectionsSettings = '/system/tcp4t/selections',
  TCP4TOperationsSettings = '/system/tcp4t/operations',
  P4TrainsSettings = '/system/tcp4t/trains',
  P4OnboardingSettings = '/system/tcp4t/p4Onboarding',
  P4OnboardingSettingsChild = '/system/tcp4t/p4Onboarding/child/:id',
  ProjectsManagement = '/system/projects',
  ProjectPermissionManagement = '/system/projects/:id/permission',
  ProjectMember = '/system/projects/:id/member',
  HomeSettings = '/system/homeSettings',
  HomeSettingsChild = '/system/homeSettings/child/:id',
  RoleManage = '/system/roleManage',
  AccountSettings = '/system/account/settings',
  MenuManagement = '/system/menu',
  BannerManagement = '/admin/bannerManagement',
  ProductionNews = '/admin/productionNews',
  ProjectSettings = '/admin/projectSettings',

  // sys admin
  SysAdmin = '/admin',

  // sys permission
  SysPermissionCenter = '/permission-center',
  PermissionCenterDashboard = '/permission-center/dashboard',
  PermissionCenterApp = '/permission-center/:appId',
  PermissionCenterManagement = '/permission-center/:appId/management',

  // sys aigc
  SysAigc = '/aigc',
  AIImage = '/aigc/image',
  // AI生图
  AIImagePage = '/aigc/image/ai-create',
  ExtraImage = '/aigc/image/extra',
  BgRemoval = '/aigc/image/bg-removal',
  // 我的资产(图片)
  MyAssets = '/aigc/image/assets',
  Voice = '/aigc/voice',
  // 语音识别
  VoiceRecognition = '/aigc/voice/recognition',
  // 语音合成
  VoiceSynthesis = '/aigc/voice/synthesis',
  // 语音资产
  VoiceAssets = '/aigc/voice/assets',
  // 语音复刻
  VoiceRemake = '/aigc/voice/remake',
  LLM = '/aigc/llm',
  VersionNews = '/aigc/llm/version-news',
  // 版本要闻-版本报告（二次总结）
  VersionNewsSecondSummaryNoParam = '/aigc/llm/version-news/second-summary',
  VersionNewsSecondSummary = '/aigc/llm/version-news/second-summary/:taskId?',
  // 版本要闻-版本要闻（一次总结）
  VersionNewsFirstSummary = '/aigc/llm/version-news/first-summary/:taskId?/:secondSummaryId?',
  // 版本要闻-原始数据
  VersionNewsOriginData = '/aigc/llm/version-news/origin-data/:taskId?/:secondSummaryId?/:summaryId?',
  AIChat = '/aigc/llm/chat',
  // AI工具
  AITools = '/aigc/tools',
  Cutting = '/aigc/tools/cutting',
  // AI 404
  AINotFound = '/aigc/not-found',
  // AI 403
  AIForbidden = '/aigc/forbidden',

  // merge
  Conflux = '/conflux',
  // history
  ConfluxHistory = '/conflux/history',
  ConfluxTask = '/conflux/task',

  // oauth
  FeishuReturn = '/auth-callback/feishuReturn',
  LinkToApp = '/auth-callback/link/app',
  SSOReturn = '/auth-callback/ssoReturn',
  LinkToOther = '/auth-callback/link/other',

  // Efficacy
  EfficacyPreview = '/efficacy/preview',

  // Commit Center
  SysDevGuard = '/devguard',
  CommitCenter = '/devguard/commit-center',
  CommitList = '/devguard/commit-list/:submitStreamID',
  Example = '/devguard/example/:submitStreamID',
  ExampleConfig = '/devguard/example/config/:submitStreamID',
  // Login
  Login = '/login',

  // Home
  Home = '/home',
  // Unknown
  ErrorUnknown = '/error',
  /**
   * @deprecated 不再使用
   */
  Redirect = '/redirect',
  // forbidden
  Forbidden = '/forbidden',
  // not found
  NotFound = '/not-found',
}
