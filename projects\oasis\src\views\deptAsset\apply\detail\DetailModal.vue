<template>
  <BasicModal
    :wrapClassName="prefixCls" :width="925" :footer="null" :afterClose="handleAfterClose"
    @register="registerModal"
  >
    <template #title>
      <div class="relative flex items-center justify-center">
        <span>{{ deviceInfo?.deviceName }}</span>
        <template v-if="!cloudDevice">
          <ADropdownButton
            v-if="userStore.isSuperAdmin || userStore.isITAssetManagement" class="absolute right-10"
            @click="handleDeviceEdit"
          >
            <span class="c-FO-Content-Text1">编辑详情</span>
            <template #overlay>
              <AMenu>
                <AMenuItem key="log" @click="() => handleLogDetail()">
                  查看操作日志
                </AMenuItem>
              </AMenu>
            </template>
          </ADropdownButton>
          <APopover v-else v-model:open="popoverVisible" trigger="click">
            <template #content>
              <div class="w-266px flex flex-col items-center justify-center gap-2">
                <div>
                  如发现信息错误或在借用过程中遇到问题，请联系人工客服进行处理
                </div>
                <a-button type="primary" size="small" @click="handleEnterService">
                  进入服务台
                </a-button>
              </div>
            </template>
            <a-button class="absolute right-10">
              问题反馈
            </a-button>
          </APopover>
        </template>
      </div>
    </template>
    <div v-track:v="'hkgfnolvay'" class="flex gap-8" :class="{ 'mb-4': !readOnly }">
      <div class="pl-2">
        <div class="mb-4 font-bold">
          设备信息
        </div>
        <ScrollContainer class="mb-40px mr-8px max-h-400px">
          <div class="mb-4 h-130px w-572px flex gap-3">
            <div class="self-center">
              <AImage
                :class="{
                  [`${prefixCls}__img`]: true,
                  'gray-mode': !deviceInfo?.online && !cloudDevice,
                }" :src="holderUrl(deviceInfo?.picURL)" :preview="false"
              />
            </div>
            <div class="min-w-0 flex-1">
              <ARow justify="space-between" :gutter="[16, 16]">
                <ACol v-for="text in cardContentList" :key="text.value" :span="12" :class=" { hidden: text.label === 'UDID：' && !deviceInfo?.deviceUDID }">
                  <ATooltip v-if="text.label === 'UDID：' && deviceInfo?.deviceUDID" placement="top">
                    <template #title>
                      <span>点击复制</span>
                    </template>
                    <div class="block w-full cursor-pointer overflow-hidden text-ellipsis whitespace-nowrap c-FO-Content-Text2" @click="copyText(getCardContent(text, deviceInfo), '复制成功')">
                      {{ device?.assetType === DeviceTypeEnum.ANDROID ? '序列号：' : 'UDID：' }}
                      {{ getCardContent(text, deviceInfo) }}
                    </div>
                  </ATooltip>

                  <EllipsisText v-else-if="text.label !== 'UDID：'" class="block w-full c-FO-Content-Text2">
                    {{ text.label }}
                    {{ getCardContent(text, deviceInfo) }}
                  </EllipsisText>
                </ACol>
              </ARow>
            </div>
          </div>
          <Description class="w-572px" size="small" :column="2" :data="deviceInfo" :schema="showFormSchema">
            <template #accessLevel>
              <div
                class="flex flex-col"
                :class="{ 'justify-center': ![DeviceAccessLevelEnum.DEPT, DeviceAccessLevelEnum.PROJECT].includes(device?.accessLevel || DeviceAccessLevelEnum.PUBLIC) }"
              >
                <div>
                  <div
                    v-if="canEditAccessLevel(device) && !readOnly"
                    class="w-fit flex cursor-pointer items-center gap-1" @click="handleEditAccessLevel"
                  >
                    <span>
                      {{ formatAccessLevel(device?.accessLevel) }}
                    </span>

                    <ATooltip>
                      <template #title>
                        编辑
                      </template>
                      <a-button title="编辑" type="text" size="small" class="!px-3px">
                        <Icon icon="icon-park-outline:edit" />
                      </a-button>
                    </ATooltip>
                  </div>
                  <template v-else>
                    {{ formatAccessLevel(device?.accessLevel) }}
                  </template>
                </div>
                <div v-if="device?.accessLevel === DeviceAccessLevelEnum.DEPT" class="mt-2 flex flex-wrap gap-1">
                  <template v-for="dept in device?.accessDepts" :key="dept.deptID">
                    <div
                      v-if="dept.deptID"
                      v-tippy="formatDept(dept.deptID, deptList, true) !== formatDept(dept.deptID, deptList) ? formatDept(dept.deptID, deptList, true) : undefined"
                      class="b-1 b-FO-Container-Stroke2 rd-2 px-1"
                    >
                      {{ formatDept(dept.deptID, deptList) }}
                    </div>
                  </template>
                </div>
                <div
                  v-else-if="device?.accessLevel === DeviceAccessLevelEnum.PROJECT"
                  class="mt-2 flex flex-wrap gap-1"
                >
                  <template v-for="accessProject in device?.accessProjects" :key="accessProject.projectID">
                    <div class="b-1 b-FO-Container-Stroke2 rd-2 px-1">
                      {{ accessProject?.project?.name }}
                    </div>
                  </template>
                </div>
              </div>
            </template>
            <template #remark>
              <div class="ml-16px flex flex-col">
                <div>
                  <div style="white-space: pre-line">
                    {{ device?.remark || '无' }}
                    <ATooltip>
                      <template #title>
                        编辑
                      </template>
                      <a-button
                        v-if="canEditAccessLevel(device) && !readOnly" title="编辑" type="text" size="small"
                        class="!px-3px" @click="handleEditRemark()"
                      >
                        <Icon icon="icon-park-outline:edit" />
                      </a-button>
                    </ATooltip>
                  </div>
                </div>
              </div>
            </template>
            <template #usage>
              {{ device?.usage?.label }}
            </template>
          </Description>
        </ScrollContainer>
      </div>
      <div class="min-w-0 flex-1">
        <div class="mb-4 font-bold">
          使用记录
        </div>
        <ScrollContainer class="max-h-400px">
          <ATimeline class="px-6 pt-4">
            <!-- 申请中或者领用中, 需要模拟显示个记录 -->
            <ATimelineItem
              v-if="DeviceFsmStateEnum.APPLYING === device?.fsmState || DeviceFsmStateEnum.BORROWING === device?.fsmState"
            >
              <div>
                {{ formatTISOToDate(device?.latestApplyLog?.UpdatedAt) }}
              </div>
              <div>
                <span class="cursor-pointer c-FO-Brand-Primary-Default" @click="() => handleUserClick(device?.currentUserID)">
                  {{ getUserById(device?.currentUserID)?.displayName }}
                </span>
                {{ getShowUserStatus(device, true) }}
              </div>
            </ATimelineItem>
            <ATimelineItem v-for="(usageLog, i) in usageLogList" :key="usageLog.ID">
              <!-- 最早的记录 -->

              <template v-if="i === usageLogList.length - 1 ">
                <div>
                  {{ formatTISOToDate(device?.CreatedAt) }}
                </div>
                <div>
                  设备入库
                </div>
              </template>
              <template v-else>
                <div>
                  {{ formatTISOToDate(device?.fsmState === DeviceFsmStateEnum.RETURNING && i === usageLogList.length - 1
                    ? device?.latestReturnLog?.CreatedAt : usageLog?.UpdatedAt) }}
                </div>
                <div>
                  <span class="cursor-pointer c-FO-Brand-Primary-Default" @click="() => handleUserClick(usageLog?.userID)">
                    {{ formatNickName(usageLog?.user) }}
                  </span>
                  <!-- 最新且正在进行中的记录 -->

                  <template
                    v-if="!usageLog?.usedTime && usageLog?.deviceType !== UsageLogDeviceTypeEnum.CLOUD && usageLog?.isLatest"
                  >
                    {{ getShowUserStatus(device, true) }}
                  </template>
                  <!-- 云真机正在使用 -->

                  <template
                    v-else-if="usageLog?.deviceType === UsageLogDeviceTypeEnum.CLOUD && !usageLog?.usedTime && usageLog?.isLatest"
                  >
                    正在使用（云真机）
                  </template>
                  <!-- 其他记录 -->
                  <template v-else>
                    使用了
                    <span class="text-gray-400">{{ formatUsedTime(usageLog?.usedTime) }}</span>
                    后归还<template v-if="usageLog?.deviceType === UsageLogDeviceTypeEnum.CLOUD">
                      （云真机）
                    </template>
                  </template>
                </div>
              </template>
            </ATimelineItem>
          </ATimeline>
        </ScrollContainer>
      </div>
    </div>
    <div v-if="device && !readOnly" :class="`${prefixCls}__modal-footer`">
      <div class="w-full flex items-center justify-between gap-4">
        <div class="flex items-center gap-1">
          <template v-if="cloudDevice">
            <span v-if="!!cloudDevice?.deviceOccupy" class="c-FO-Content-Text2">
              预计 {{ cloudDevice?.remainTime }} 分后释放
            </span>
            <Icon
              v-if="cloudDevice.deviceOccupy && cloudDevice.deviceOccupy?.username !== userStore.getUserInfo.userName"
              v-tippy="device.isCloudSubscribed ? '取消订阅“空闲提醒”' : '订阅“空闲提醒”'" icon="material-symbols:bookmark-star-rounded"
              class="cursor-pointer"
              :class="device.isCloudSubscribed ? 'c-FO-Brand-Primary-Default hover:c-FO-Brand-Primary-Hover' : 'c-FO-Content-Text2 hover:c-FO-Content-Text3'"
              @click.stop="() => handleSubscribe()"
            />
          </template>
          <template v-else>
            <span
              v-if="![DeviceFsmStateEnum.FREE].includes(device.fsmState || DeviceFsmStateEnum.FREE) && device.latestApplyLog?.returnTime"
            >
              <ATag v-if="isOverdue(device.latestApplyLog?.returnTime)" color="red" :bordered="false">
                逾期
              </ATag>
              <span :class="isOverdue(device.latestApplyLog?.returnTime) ? 'c-FO-Functional-Error1-Default' : 'c-FO-Content-Text2'">
                {{ formatReturnTime(device.latestApplyLog?.returnTime || 0, true) }}
              </span>
            </span>
            <Icon
              v-if="device.fsmState !== DeviceFsmStateEnum.FREE && (hasAllPermission(device) || userStore.isITAssetManagement)"
              v-tippy="'更改预计归还时间'" :icon="EditIcon" class="cursor-pointer c-FO-Content-Text2 hover:c-FO-Content-Text1"
              @click.stop="() => handleApply(ApplyTypeEnum.CHANGE_RETURN_TIME)"
            />
            <Icon
              v-if="device.fsmState !== DeviceFsmStateEnum.FREE && device.currentUserID !== userStore.getUserInfo.ID && !isCurDeviceAdmin(device)"
              v-tippy="device.isSubscribed ? '取消订阅“空闲提醒”' : '订阅“空闲提醒”'" icon="material-symbols:bookmark-star-rounded"
              class="cursor-pointer"
              :class="device.isSubscribed ? 'c-FO-Brand-Primary-Default hover:c-FO-Brand-Primary-Hover' : 'c-FO-Content-Text2 hover:c-FO-Content-Text3'"
              @click.stop="() => handleSubscribe()"
            />
          </template>
        </div>
        <div class="flex items-center gap-4">
          <template v-if="cloudDevice">
            <a-button
              v-if="!cloudDevice?.deviceOccupy" type="primary" class="w-90px"
              @click.stop="() => handleUseDevice()"
            >
              立即使用
            </a-button>
            <a-button
              v-else-if="cloudDevice?.deviceOccupy?.username === userStore.getUserInfo.userName" type="success" class="w-90px"
              @click.stop="() => handleContinueUse()"
            >
              继续使用
            </a-button>
          </template>
          <template v-else>
            <template v-if="canBorrow(device)">
              <a-button
                v-if="isCurDeviceAdmin(device)"
                class="w-90px !b-FO-Brand-Primary-Default !c-FO-Brand-Primary-Default hover:!bg-FO-Container-Fill2"
                @click.stop="() => handleApply(ApplyTypeEnum.DIRECT_BORROW)"
              >
                借出
              </a-button>
              <a-button
                v-else-if="!isUnavailable(device)" class="w-90px" type="primary"
                @click.stop="() => handleApply(ApplyTypeEnum.BORROW_APPLY)"
              >
                借用
              </a-button>
            </template>
            <template v-if="canReturn(device)">
              <a-button
                v-if="isCurDeviceAdmin(device)"
                class="w-90px !b-FO-Functional-Success1-Default !c-FO-Functional-Success1-Default hover:!bg-FO-Container-Fill2"
                @click.stop="() => handleDirectReturn()"
              >
                确认归还
              </a-button>
              <a-button
                v-else class="w-90px" type="success"
                @click.stop="() => handleApply(ApplyTypeEnum.RETURN_APPLY)"
              >
                归还申请
              </a-button>
            </template>
            <APopconfirm
              v-if="canPickUp(device)" title="确认你已借出该设备吗？" placement="topRight" okText="确认"
              @confirm="() => handlePickUp()"
            >
              <a-button class="w-90px" type="success" @click.stop>
                确认借出
              </a-button>
            </APopconfirm>
            <a-button
              v-if="canAuditReturn(device) || canAudit(device)" class="w-90px" type="error"
              @click.stop="() => handleApply(canAuditReturn(device!) ? ApplyTypeEnum.RETURN_AUDIT : ApplyTypeEnum.BORROW_AUDIT)"
            >
              审批
            </a-button>
          </template>
        </div>
      </div>
    </div>
    <ApplyModal @register="registerApplyModal" @success="handleApplySuccess" />
    <AuditModal @register="registerAuditModal" @success="handleSuccess" />
    <AccessLevelModal @register="registerAccessLevelModal" @success="handleDeviceInfoChange" />
    <UseDurationModal @register="registerDurationModal" @success="handleSuccess" />
    <RemarkModalHolder />
  </BasicModal>
</template>

<script lang="ts" setup>
import { type DeviceBrandListItem, type DeviceListItem, type UsageLogListItem, DeviceApplyStateEnum, DeviceCategoryTypeEnum, DeviceFsmStateEnum, UsageLogDeviceTypeEnum } from '/@/api/page/model/deptAssetModel';
import {
  Col as ACol,
  DropdownButton as ADropdownButton,
  Image as AImage,
  Menu as AMenu,
  MenuItem as AMenuItem,
  Popconfirm as APopconfirm,
  Popover as APopover,
  Row as ARow,
  Tag as ATag,
  Timeline as ATimeline,
  TimelineItem as ATimelineItem,
  Tooltip as ATooltip,
  message,
} from 'ant-design-vue';
import { ref } from 'vue';
import { cardContentList } from '../device.data';
import ApplyModal from './ApplyModal.vue';
import AuditModal from './AuditModal.vue';
import {
  confirmBorrowDevice,
  editPartialDevice,
  getDeviceBrandsListByPage,
  getDeviceByID,
  getUsageLogList,
  subscribeDevice,
  unsubscribeDevice,
} from '/@/api/page/deptAsset';
import { Description } from '/@/components/Description';
import type { ModalMethods } from '/@/components/Modal';
import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
import { useDesign } from '/@/hooks/web/useDesign';
import { formatTISOToDate } from '/@/utils/dateUtil';
import { ScrollContainer } from '/@/components/Container';
import { copyText } from '/@/utils/copyTextToClipboard';
import {
  accessLevelList,
  assetTypeOptions,
  mobileTypeOptions,
  showFormSchema,
} from '/@/views/deptAsset/apply/device.data';
import { DeviceAccessLevelEnum, DeviceTypeEnum } from '/@/api/page/model/deptAssetModel';
import { useDeptAssetApply } from '../hook';
import { getAllPaginationList } from '/@/hooks/web/usePagination';
import { ApplyTypeEnum } from '../apply.data';
import { formatNickName } from '/@/hooks/system/useUserList';
import { ForgeonTitleMap, PlatformEnterPoint } from '@hg-tech/oasis-common';
import { Icon } from '/@/components/Icon';

import type { DeptListItem } from '/@/api/page/model/systemModel';
import AccessLevelModal from './AccessLevelModal.vue';
import { useUserStore } from '/@/store/modules/user';
import { useRoute, useRouter } from 'vue-router';
import UseDurationModal from '../../cloud/UseDurationModal.vue';
import { groupBy } from 'lodash';
import { getDeptList } from '/@/api/page/system';
import { useDeviceHolderImage } from '../../getDeviceImg';
import type { MtlDeviceListCombinedItem } from '/@/api/page/mtl/model/deviceModel';
import { getMtlDeviceDetail } from '/@/api/page/mtl/device';
import { useModalShow } from '@hg-tech/utils-vue';
import RemarkModal from './RemarkModal.vue';
import EditIcon from '@iconify-icons/icon-park-outline/edit';
import { sendEvent } from '/@/service/tracker';

const emits = defineEmits<{
  success: [];
  register: [methods: ModalMethods, uuid: number];
  deviceEdit: [record: DeviceListItem];
}>();
const { prefixCls } = useDesign('dept-asset-detail-modal');
const userStore = useUserStore();
const { holderUrl } = useDeviceHolderImage();
const route = useRoute();
const { resolve, replace, push } = useRouter();
const deviceID = ref<number>();
const device = ref<DeviceListItem>();
const cloudDevice = ref<MtlDeviceListCombinedItem>();
const deviceInfo = ref<DeviceListItem>();
const ownerID = ref<number>();
const applyState = ref<number>();
const returnState = ref<number>();
const applyID = ref<number>();
const returnID = ref<number>();
const deviceName = ref<string>();
const usageLogList = ref<UsageLogListItem[]>([]);
const brandList = ref<DeviceBrandListItem[]>([]);
const deptList = ref<DeptListItem[]>([]);
const popoverVisible = ref(false);
const isEditMode = ref(false);
const readOnly = ref(false);
const isSettingsPage = ref(false);

const { canBorrow, isUnavailable, isCurDeviceAdmin, hasAllPermission, getUserById, isRecycled, curDeviceAdmin, canPickUp, canReturn, canAudit, canAuditReturn, formatReturnTime, isOverdue, formatDept, getCardContent, handleUserClick, getShowUserStatus, getDevicePlatform } = useDeptAssetApply();

const [registerApplyModal, { openModal: openApplyModal }] = useModal();
const [registerAuditModal, { openModal: openAuditModal }] = useModal();
const [registerAccessLevelModal, { openModal: openAccessLevelModal }] = useModal();
const [registerDurationModal, { openModal: openDurationModal }] = useModal();
const [RemarkModalHolder, showRemarkModal] = useModalShow(RemarkModal);
/** 获取设备信息 */
async function getDevice() {
  if (!deviceID.value) {
    return;
  }
  const { assetDevice } = await getDeviceByID(deviceID.value);
  device.value = assetDevice;
}

/** 获取设备品牌列表 */
async function getDeviceBrandList() {
  const { list } = await getAllPaginationList(getDeviceBrandsListByPage);
  brandList.value = list || [];
}

/** 获取使用记录 */
async function getUsageLogs() {
  const { list } = await getAllPaginationList((p) => getUsageLogList({
    ...p,

    deviceID: deviceID.value as number,
  }));

  // 分别找出设备借用和云真机最后一个usedTime为空的记录并标记为isLatest

  const groupedList = groupBy(list || [], 'deviceType');
  const deviceList = groupedList?.[UsageLogDeviceTypeEnum.DEVICE];
  const cloudList = groupedList?.[UsageLogDeviceTypeEnum.CLOUD];
  const latestDevice = deviceList?.length ? deviceList[0] : undefined;
  const latestCloud = cloudList?.length ? cloudList[0] : undefined;
  if (latestDevice && !latestDevice.usedTime) {
    latestDevice.isLatest = true;
  }
  if (latestCloud && !latestCloud.usedTime) {
    latestCloud.isLatest = true;
  }
  usageLogList.value = list || [];
}

/**
 * 格式化使用时间
 * @param time 使用时间
 * @returns 格式化后的使用时间
 */
function formatUsedTime(time?: string) {
  if (!time) {
    return '0秒';
  }
  if (time.includes('h')) {
    const hour = Number(time.split('h')[0]);
    return hour > 24 ? `${Math.floor(hour / 24 + 1)}天` : `${hour}小时`;
  } else if (time.includes('m')) {
    const min = Number(time.split('m')[0]);
    return `${min}分钟`;
  } else {
    const sec = Number(time.split('s')[0]);
    return `${sec}秒`;
  }
}

// 资产分类名称获取
function formatAssetType(val?: number) {
  return assetTypeOptions.find((e) => e.value === val)?.label || '';
}

// 手机类型名称获取
function formatMobileType(val?: number) {
  return mobileTypeOptions.find((e) => e.value === val)?.label || '';
}

// 流通级别名称获取
function formatAccessLevel(val?: number) {
  return accessLevelList.find((e) => e.value === val)?.label || '';
}

function formatBrand(val?: number) {
  const brand = brandList.value.find((e) => e.ID === val);
  return brand?.cnName ? `${brand.cnName} (${brand.name})` : brand?.name;
}

/** 是否可以编辑流通级别 */
function canEditAccessLevel(device?: DeviceListItem) {
  if (!device) {
    return false;
  }
  return hasAllPermission(device) || userStore.isITAssetManagement;
}

const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  setModalProps({ confirmLoading: true });
  isEditMode.value = false;
  deviceID.value = data?.deviceID;
  cloudDevice.value = data?.cloudDevice;
  readOnly.value = data?.readOnly;
  isSettingsPage.value = data?.isSettingsPage;
  await getDevice();

  ownerID.value = device.value?.ownerID;
  applyID.value = device.value?.latestApplyLog?.ID;
  returnID.value = device.value?.latestReturnLog?.ID;
  brandList.value = data?.brandList || [];
  if (!data?.brandList) {
    await getDeviceBrandList();
  }
  deviceInfo.value = Object.assign({}, device.value, {
    assetType: formatAssetType(device.value?.assetType),
    mobileType: formatMobileType(device.value?.mobileType),
    ownerID: formatNickName(curDeviceAdmin(device.value)) + (isRecycled(device.value) ? ' (部门管理员)' : ''),
    deptID: device.value?.dept?.orgPath,
    brandID: formatBrand(device.value?.brandID),
    chipsetID: `${device.value?.chipset?.brand?.nameCN || ''} / ${device.value?.chipset?.socName || ''}`,
  });
  applyState.value = data?.record?.latestApplyLog?.state;
  returnState.value = data?.record?.latestReturnLog?.state;
  deviceName.value = device.value?.deviceName;
  await getUsageLogs();
  if (route.query.editId) {
    const { list } = await getDeptList();
    deptList.value = list || [];
  } else {
    deptList.value = data?.deptList || [];
  }
  setModalProps({ confirmLoading: false });
});

/** 处理申请、审核、归还 */
function handleApply(type: ApplyTypeEnum) {
  openApplyModal(true, {
    record: device.value,
    deviceID: device.value?.ID,
    type,
  });
}

/** 确认归还 */
function handleDirectReturn() {
  openAuditModal(true, {
    deviceID: device.value?.ID,
    isDirectReturn: true,
  });
}

/** 确认领用 */
async function handlePickUp() {
  if (!device.value?.latestApplyLog?.ID) {
    return;
  }

  await confirmBorrowDevice({
    deviceID: device.value?.ID,
    isBorrow: true,
    state: DeviceApplyStateEnum.APPROVED,
  }, device.value?.latestApplyLog.ID);
  await handleSuccess();
}

/** 申请、审核、归还成功 */
async function handleSuccess() {
  closeModal();
  emits('success');
}

/** 流通级别修改 */
function handleEditAccessLevel() {
  openAccessLevelModal(true, {
    record: device.value,
    deptList: deptList.value,
  });
}

/** 设备信息变更 */
async function handleDeviceInfoChange() {
  await getDevice();
  emits('success');
}

/** 备注修改 */
async function handleEditRemark() {
  await showRemarkModal({
    remark: device.value?.remark || '',
    async sentReq(remark: string) {
      if (!device.value?.ID) {
        return;
      }
      return editPartialDevice(device.value.ID, {
        remark,
      }).then((res) => res?.data?.data);
    },
  });
  handleDeviceInfoChange();
}

/** 进入服务台 */
function handleEnterService() {
  window.open('https://applink.feishu.cn/T8TIfICu3Rjd', '_blank');
  popoverVisible.value = false;
}

/** 编辑设备 */
function handleDeviceEdit() {
  if (!device.value) {
    return;
  }
  isEditMode.value = true;
  emits('deviceEdit', device.value);
  closeModal();
}

/** 查看操作日志 */
function handleLogDetail() {
  const { fullPath } = resolve({ name: PlatformEnterPoint.DeviceManagementLogs, query: { editId: device.value?.ID } });
  window.open(fullPath, '_blank');
}

/** 处理订阅 */
async function handleSubscribe() {
  if (!device.value?.ID || device.value?.isSubscribing) {
    return;
  }
  device.value.isSubscribing = true;
  const deviceType = cloudDevice.value?.deviceId ? DeviceCategoryTypeEnum.Cloud : DeviceCategoryTypeEnum.Common;
  const isSubscribed = deviceType === DeviceCategoryTypeEnum.Cloud ? device.value?.isCloudSubscribed : device.value?.isSubscribed;
  if (isSubscribed) {
    const res = await unsubscribeDevice(device.value?.ID, deviceType);
    if (res?.code !== 7) {
      message.success('已取消订阅');
    }
  } else {
    const res = await subscribeDevice(device.value?.ID, deviceType);
    if (res.code !== 7) {
      message.success('订阅成功');
    }
  }
  const device_product_type = ForgeonTitleMap[cloudDevice.value?.deviceId ? PlatformEnterPoint.CloudDevice : (isSettingsPage.value ? PlatformEnterPoint.DeptAssetsManagement : PlatformEnterPoint.DeptAssetApplyManagement)];
  sendEvent('device_idle_alert_click', {
    device_product_type,
    device_name: device.value?.deviceName,
    device_code: device.value?.assetNo,
    device_platform: getDevicePlatform(device.value),
  });
  handleDeviceInfoChange();
}

/** 处理申请成功 */
function handleApplySuccess(applyType: ApplyTypeEnum) {
  if (applyType === ApplyTypeEnum.CHANGE_RETURN_TIME) {
    handleDeviceInfoChange();
  } else {
    handleSuccess();
  }
}

/** 关闭模态框 */
async function handleAfterClose() {
  if ((route.query.editId || route.query.deviceId) && !isEditMode.value && !readOnly.value) {
    await replace({ query: { ...route.query, editId: undefined, deviceId: undefined } });
  }
}

function handleUseDevice() {
  openDurationModal(true, {
    device: cloudDevice.value,
  });
}

async function handleContinueUse() {
  if (!cloudDevice.value?.deviceId) {
    return;
  }
  const { data: { data: mtlDeviceInfo } } = await getMtlDeviceDetail({ id: cloudDevice.value?.deviceId }, {});
  cloudDevice.value.deviceOccupy = mtlDeviceInfo.deviceOccupy;
  // 如果设备未被申请，则切换为立即使用流程
  if (!mtlDeviceInfo?.deviceOccupy) {
    handleUseDevice();
    return;
  } else if (mtlDeviceInfo?.deviceOccupy?.username && mtlDeviceInfo.deviceOccupy.username !== userStore.getUserInfo?.userName) {
    // 如果设备正在使用，并且当前用户不是设备使用人，则提示无法使用
    message.error('未申请当前设备，无法使用');
    return;
  }
  push({
    name: PlatformEnterPoint.CloudDeviceDetail,
    params: { id: cloudDevice.value?.ID },
    query: {
      deviceId: cloudDevice.value?.deviceId,
    },
  });
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-dept-asset-detail-modal';
.@{prefix-cls} {
  &__header {
    position: relative;
    margin-bottom: 16px;
    text-align: center;
  }

  &__title {
    font-size: 20px;
    font-weight: bold;
  }

  &__return-btn {
    position: absolute;
    right: 5px;
  }

  &__footer {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
  }

  &__apply-msg {
    width: 180px;
    margin: 0 auto 16px;
    padding: 8px;
    border-width: 1px;
    border-style: dashed;
    border-radius: 8px;
    border-color: @FO-Brand-Primary-Default;
    font-size: 16px;
    text-align: center;
  }

  &__img {
    width: 100px;
    max-width: 100px;
    max-height: 130px;

    &.gray-mode {
      filter: grayscale(100%) !important;
    }
  }

  & .ant-descriptions-row:last-child .ant-descriptions-item-content:last-child {
    padding: 0 !important;
  }
  &__modal-footer {
    @apply flex justify-between items-center pl-6 pr-14px py-2 font-bold;

    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 60px;
    border-radius: 0 0 8px 8px;
    background-color: @FO-Container-Stroke1;
  }
}
</style>
