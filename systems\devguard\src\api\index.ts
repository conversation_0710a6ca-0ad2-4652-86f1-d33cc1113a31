import { requestService } from '../services/req.ts';
import type { PagedQueryParam, PermissionBaseRes } from './_common.ts';

export interface DepotsListItem {
  ID: number;
  prefix: string;
  depth?: number;
  isLocal?: boolean;
  serverID?: number;
  name?: string;
}
export interface instanceItem {
  instanceType: number;
  name: string;
  workState: number;
}
export interface StreamsListItem {
  id: number;
  depotID?: number;
  name?: string;
  path?: string;
  description?: string;
  streamType?: number;
  sort?: number;
  submittingCount?: number;
  // todaySubmittedCount?: number;
  submitStreamID?: number;
  groupedInstanceList?: {
    queueCount: number;
    instanceList: instanceItem[];
  }[];
}
export interface ListSubmitItem {
  description: string;
  isLocal: boolean;
  name: string;
  prefix: string;
  projectID: number;
  repoType: number;
  sort: number;
  streams: StreamsListItem[];
}

export interface RecordListItem {
  ID: number;
  shelveCL: number;
  reviewCL: number;
  submitCL: number;
  serverID: number;
  streamID: number;
  swarmBaseURL: string;
  submitterName: string;
  submitter: Submitter;
  hasResCheck: boolean;
  submitState: number;
  reviewState: number;
  checkState: number;
  workItemURL: string;
  workItemTitle: string;

}
export interface Submitter {

  ID?: number;
  CreatedAt?: string;
  UpdatedAt?: string;
  uuid?: string;
  userName?: string;
  nickName?: string;
  sideMode?: string;
  headerImg?: string;
  baseColor?: string;
  activeColor?: string;
  authorityId?: number;
  authority?: {
    CreatedAt?: string;
    UpdatedAt?: string;
    DeletedAt?: null;
    authorityId?: number;
    authorityName?: string;
    parentId?: number;
    dataAuthorityId?: null;
    children?: null;
    menus?: null;
    defaultRouter?: string;
  };
  authorities?: null;
  phone?: string;
  email?: string;
  enable?: number;
  authType?: number;
  openID?: string;
}
export interface SubmitInfo {
  changelist?: number;
  submitterName?: string;
  submitter?: Submitter;
  date?: string;
  description?: string;
}
export interface QaCheckInfo {
  recordID?: number;
  shelveCL?: number;
  checkQaList: Submitter[];
  submitterName?: string;
  submitter?: Submitter;
  date?: string;
  description?: string;
  workItemID?: number;
  workItemTitle?: string;
  workItemURL?: string;
  canApply?: boolean;
  checkTime?: string;
}

export interface InstanceConfigItem {
  id: number;
  ipAddress?: string;
  instanceType?: number;
  workState?: number;
  pendingState?: number;
  disabled?: boolean;
  name?: string;
  workspace?: string;
  bootTimestamp?: number;
  piplineUrl?: string;
}

export interface OperationsListItem {
  checkInstance: InstanceConfigItem;
  operator: Submitter;
  execTime: string;
  finishTime: string;
  operationType: number;
  modifications: {
    modifyKey: string;
    oldValue: string;
    newValue: string;
  }[];

}
export interface scheduleItem {
  id?: number;
  streamID?: number;
  operationType?: number;
  triggerType?: number;
  triggerTimestamp?: number;
  triggerCron?: string;
  instanceIDs?: number[];
}

export interface DistributeItem {
  distributeType: number;
  ruleList: {
    instanceType: number;
    suffixList: string[];
  }[];
  fallbackType: number;
}
export interface CheckQueues {
  instanceType: number;
  queueUsers: Submitter[];
}

export interface TaskListItem {
  checkQueues: CheckQueues[];
  checkInstances: InstanceConfigItem[];
}
export interface TaskItems {

  instanceID: number;
  title: string;
  distinguishCode: number;
  startTime: number;
  endTime: number;
  status: number;
  username: string;
  user: Submitter;
  clRecordID: number;
}
/**
 * 获取指定分支信息
 */
export const getStreamsInfo = requestService.GET<
  { id: number; stream_id: number },
  Record<string, never>,
  PermissionBaseRes<{ stream: StreamsListItem }>
>('/api/v1/projects/:id/submit/streams/:stream_id/info');

/**
 * 获取主分支列表页信息
 */
export const getListSubmit = requestService.GET<
  { id: number; projectCode: string },
  Record<string, never>,
  PermissionBaseRes<{ list: ListSubmitItem[] }>
>('/api/v1/projects/:id/submit/streams/listSubmit');

/**
 * 查询 CommitServer 上的 stream 记录
 */
export const getRecordListByPage = requestService.GET<
  { id: number; streamID: number; submitState: number; keyword: string } & PagedQueryParam,
  Record<string, never>,
  PermissionBaseRes<{ list: RecordListItem[]; total: number }>
>('/api/v1/projects/:id/submit/changeLists/recordList');

/**
 * 获取提交详情
 */
export const getSubmitInfoApi = requestService.GET<
  { id: number; recordID: number },
  Record<string, never>,
  PermissionBaseRes<SubmitInfo>
>('/api/v1/projects/:id/submit/changeLists/submitInfo');

/**
 * 获取审批详情
 */
export const getQaCheckInfoApi = requestService.GET<
  { id: number; recordID: number },
  Record<string, never>,
  PermissionBaseRes<QaCheckInfo>
>('/api/v1/projects/:id/submit/changeLists/qaCheckInfo');

/**
 *执行审批
 */
export const applyQaCheckApi = requestService.POST<
  { id: number },
  { clRecordID: number; ApproveCode: number },
  PermissionBaseRes<undefined>
>('/api/v1/projects/:id/submit/changeLists/applyQaCheck');

/**
 *实例配置页列表
 */
export const getInstanceConfigListApi = requestService.GET<
  { id: number } & { streamID: number },
  Record<string, never>,
  PermissionBaseRes<InstanceConfigItem[]>
>('/api/v1/projects/:id/submit/checkInstances/configList');

/**
 *实例配置页排序
 */
export const batchSortListApi = requestService.POST<
  { id: number },
  { streamID: number; ids: number[] },
  PermissionBaseRes<undefined>
>('/api/v1/projects/:id/submit/checkInstances/batchSort');

/**
 *实例配置页编辑
 */
export const updateInstanceConfigItemApi = requestService.POST<
  { id: number },
  { streamID: number; instanceID: number; name: string; piplineUrl: string },
  PermissionBaseRes<undefined>
>('/api/v1/projects/:id/submit/checkInstances/update');

/**
 *实例配置更新/重启/禁用实例
 */
export const updateInstanceOperateApi = requestService.POST<
  { id: number },
  { streamID: number; instanceID: number; operation: number },
  PermissionBaseRes<undefined>
>('/api/v1/projects/:id/submit/checkInstances/operate');
/**
 *取消更新/禁用/重启实例
 */
export const cancelInstanceOperateApi = requestService.POST<
  { id: number },
  { streamID: number; instanceID: number; operation: number },
  PermissionBaseRes<{ list: OperationsListItem[]; total: number }>
>('/api/v1/projects/:id/submit/checkInstances/cancel');
/**
 *全部更新/重启实例
 */
export const updateInstancebatchOperateApi = requestService.POST<
  { id: number },
  { streamID: number; operation: number },
  PermissionBaseRes<undefined>
>('/api/v1/projects/:id/submit/checkInstances/batchOperate');

/**
 *获取配置页实例操作列表
 */
export const getOperationsListApi = requestService.GET<
  { id: number; streamID: number; page: number; pageSize: number; operatorNames?: string[]; startTime?: number; endTime?: number; operationTypes?: number[] },
  Record<string, never>,
  PermissionBaseRes<{ list: OperationsListItem[]; total: number }>
>('/api/v1/projects/:id/submit/checkInstances/operations');

/**
 *查询实例分配规则
 */
export const getDistributeApi = requestService.GET<
  { id: number; streamID: number },
  Record<string, never>,
  PermissionBaseRes<DistributeItem>
>('/api/v1/projects/:id/submit/instanceRules/distribute');

/**
 *更新实例分配规则
 */
export const setDistributeApi = requestService.POST<
  { id: number },
  DistributeItem & {
    streamID: number;
    fallbackType: number;
  },
  PermissionBaseRes<null>
>('/api/v1/projects/:id/submit/instanceRules/setDistribute');

/**
 *查询实例定时任务列表
 */
export const getScheduleListApi = requestService.GET<
  { id: number; streamID: number },
  Record<string, never>,
  PermissionBaseRes<scheduleItem[]>
>('/api/v1/projects/:id/submit/instanceRules/scheduleList');

/**
 *查询实例定时任务列表
 */
export const addScheduleApi = requestService.POST<
  { id: number },
  scheduleItem,
  PermissionBaseRes<null>
>('/api/v1/projects/:id/submit/instanceRules/addSchedule');
/**
 *更新实例定时任务列表
 */
export const updateSchedule = requestService.POST<
  { id: number },
  scheduleItem & { recordID: number },
  PermissionBaseRes<null>
>('/api/v1/projects/:id/submit/instanceRules/updateSchedule');
/**
 *删除单条定时任务列表
 */
export const deleteScheduleApi = requestService.POST<
  { id: number },
  {
    recordID: number;
    streamID: number;
  },
  PermissionBaseRes<null>
>('/api/v1/projects/:id/submit/instanceRules/deleteSchedule');
/**
 *删除单条定时任务列表
 */
export const getStreamCheckInstanceTypesApi = requestService.GET<
  { id: number;streamID: number },
  Record<string, never>,
  PermissionBaseRes<number[]>
>('/api/v1/projects/:id/submit/checkInstances/streamCheckInstanceTypes');

/**
 *实例任务页列表
 */
export const getTaskListApi = requestService.GET<
  { id: number;streamID: number ;instanceType: number },
  Record<string, never>,
  PermissionBaseRes<TaskListItem>
>('/api/v1/projects/:id/submit/checkInstances/taskList');

/**
 *单个实例任务列表
 */
export const getTaskItemsApi = requestService.GET<
  { id: number;streamID: number ;instanceID: number;startTime: number;endTime: number },
  Record<string, never>,
  PermissionBaseRes<TaskItems[]>
>('/api/v1/projects/:id/submit/checkInstances/taskItems');

/**
 *查询单条提交记录详情
 */
export const getTaskItemsDetailApi = requestService.GET<
  { id: number;streamID: number ;recordID: number },
  Record<string, never>,
  PermissionBaseRes<RecordListItem>
>('/api/v1/projects/:id/submit/changeLists/record');
