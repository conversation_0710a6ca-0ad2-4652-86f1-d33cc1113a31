import { computed } from 'vue';
import { usePermissionCheckPoint } from '../../../service/permission/usePermission.ts';
import { PermissionPoint } from '@hg-tech/oasis-common';
import { useAdmin } from '../../../hooks/useProjects.ts';

export function useIsPackageAdmin() {
  const { isSuperAdminOrProjectAdmin } = useAdmin();
  const [hasPermissionAutomationTask] = usePermissionCheckPoint(PermissionPoint.GamePackageSettings);

  const isPackageAdmin = computed(() => isSuperAdminOrProjectAdmin.value || hasPermissionAutomationTask.value);

  return {
    isPackageAdmin,
  };
}
