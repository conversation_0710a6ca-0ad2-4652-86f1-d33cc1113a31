import { type CommonBaseRes, PlatformEnterPoint } from '@hg-tech/oasis-common';
import type { SysUserInfo } from './page/permission/users';
import { PermissionCenterApiService } from '../service/apiService/permissionCenter.ts';

/**
 * 获取设备中心权限点列表
 */
export const getDeptAssetPermissionPointList = PermissionCenterApiService.POST<
  Record<string, never>,
  {
    subject: {
      type: 'user';
      id: SysUserInfo['hgAccount'];
    };
  },
  CommonBaseRes<{ permissions: string[] }>
>(`/api/auth/v1/permissions/listByHgAccountWithoutSign`, { headers: { app: PlatformEnterPoint.DeptAsset }, skipErrorHandler: true });
