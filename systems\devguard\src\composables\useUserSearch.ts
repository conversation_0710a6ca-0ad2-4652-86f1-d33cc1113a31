import type { Ref } from 'vue';
import { computed, ref, watch } from 'vue';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { type SysUserInfo, queryUserList } from '../api/user';
import type { ForgeonUserSelectorOptionItem } from '@hg-tech/oasis-common';
import { unionBy } from 'lodash';

export function useUserListOption(fallback: Ref<SysUserInfo[]>) {
  const sessionUserOptions = ref<ForgeonUserSelectorOptionItem[]>([]);
  const { data: userListRes, loading: userListLoading, execute: queryUser, reset: resetUserList } = useLatestPromise(queryUserList);
  const userListOptions = computed<ForgeonUserSelectorOptionItem[]>(() => {
    if (userListRes.value?.data?.data?.length) {
      return userListRes.value.data.data?.map((i) => ({
        id: i.hgAccount ?? '',
        name: i.name ?? '',
        nickName: i.nickname ?? '',
        avatar: i.avatar ?? '',
      }));
    }

    return (fallback.value).filter(Boolean).map((i) => ({
      id: i.hgAccount ?? '',
      name: i.name ?? '',
      nickName: i.nickname ?? '',
      avatar: i.avatar ?? '',
    }));
  });

  watch(() => userListOptions.value, (newData) => {
    if (newData?.length > 0) {
      sessionUserOptions.value = unionBy(sessionUserOptions.value, newData, 'id');
    }
  }, { deep: true, immediate: true });

  const getUserItemFromSession = (item: string) => {
    return sessionUserOptions.value.find((i) => i.id === item);
  };

  return {
    sessionUserOptions,
    userListOptions,
    userListLoading,
    queryUser,
    resetUserList,
    getUserItemFromSession,
  };
}
