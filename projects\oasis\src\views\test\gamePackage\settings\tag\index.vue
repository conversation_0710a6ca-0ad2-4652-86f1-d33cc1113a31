<template>
  <div v-track:v="'gcs7r9nb1x'" :class="prefixCls">
    <div class="text-center text-lg font-bold">
      配置安装包标签
    </div>
    <div
      :class="`${prefixCls}__body`"
      :style="{ maxHeight: `calc(100vh - 100px - ${headerHeightRef}px)` }"
    >
      <div :class="`${prefixCls}__left`">
        <div :class="`${prefixCls}__forms`">
          <div
            v-for="item in newTagList"
            :key="item.ID"
            :class="`${prefixCls}__forms-item`"
            :isNew="!!item.UUID"
          >
            <Icon
              v-if="!hasNewTagEdit"
              icon="ic:round-drag-handle"
              :size="26"
              :class="`${prefixCls}__forms-drag-btn`"
            />
            <ARow :gutter="[16, 16]" class="w-0 flex-1">
              <ACol :span="8" class="items-center !flex">
                <span class="mr-2 w-60px text-right leading-8">标签名</span>
                <div :class="`${prefixCls}__forms-item-name-input`">
                  <a-input
                    v-model:value="item.name"
                    placeholder="请输入标签名"
                    @blur="handleSave(item, 'name')"
                  >
                    <template #suffix>
                      <ColorPopover v-model:value="item.color" @change="handleSave(item, 'color')" />
                    </template>
                  </a-input>
                </div>
              </ACol>
              <ACol :span="8" class="items-center !flex">
                <span class="mr-2 w-80px text-right leading-8">识别名</span>
                <a-input
                  v-model:value="item.identifier"
                  class="flex-1"
                  placeholder="请输入识别名"
                  @blur="handleSave(item, 'identifier')"
                />
              </ACol>
              <ACol :span="8" class="items-center !flex">
                <span class="mr-2 w-80px text-right leading-8">标签类型</span>
                <div :class="`${prefixCls}__forms-item-radio`">
                  <div :active="item.singleChoice" @click="handleTypeChange(item, true)">
                    单选
                  </div>
                  <div :active="!item.singleChoice" @click="handleTypeChange(item, false)">
                    多选
                  </div>
                </div>
              </ACol>
              <ACol :span="23" class="items-center !flex">
                <span class="mr-2 w-60px self-start text-right leading-8">标签项</span>
                <ARow class="w-0 flex-1" :gutter="[16, 8]">
                  <ACol v-for="val in item.values" :key="val.ID" :span="4">
                    <a-input
                      v-model:value="val.value"
                      :maxlength="20"
                      :style="{
                        borderColor: item.color,
                        color: item.color,
                        borderRadius: item.singleChoice ? '100px !important' : undefined,
                      }"
                      :class="`${prefixCls}__forms-item-input`"
                      :isNew="!!val.UUID"
                      @blur="handleValueSave(val, item)"
                    />
                    <APopconfirm
                      title="确定删除该标签项吗?"
                      overlayClassName="!w-190px"
                      :getPopupContainer="(trigger) => trigger.parentNode"
                      @confirm="handleValueDelete(val, item)"
                    >
                      <a-button
                        preIcon="ant-design:minus-outlined"
                        :class="`${prefixCls}__val-del-btn`"
                        shape="circle"
                        size="small"
                        @click.stop
                      />
                    </APopconfirm>
                  </ACol>
                  <ACol :span="4" class="items-center !flex">
                    <a-button
                      v-if="!item.hasNewValueEdit"
                      preIcon="ant-design:plus-outlined"
                      :class="`${prefixCls}__btn !rounded !px-4px`"
                      :isSingle="item.singleChoice"
                      size="small"
                      @click="handleValueAdd(item)"
                    />
                  </ACol>
                </ARow>
              </ACol>
            </ARow>

            <APopconfirm
              title="确定删除该标签吗?"
              overlayClassName="!w-180px"
              :getPopupContainer="(trigger) => trigger.parentNode"
              @confirm="handleDelete(item)"
            >
              <a-button
                preIcon="ant-design:minus-outlined"
                :class="`${prefixCls}__btn`"
                class="!absolute -right-6px -top-6px"
                shape="circle"
                size="small"
                @click.stop
              />
            </APopconfirm>
          </div>
        </div>

        <div
          v-if="!hasNewTagEdit"
          v-track="'wsvtszfqk5'"
          :class="`${prefixCls}__forms-add`"
          @click="handleAdd()"
        >
          <Icon icon="ant-design:plus-outlined" :size="50" />
        </div>
      </div>
      <div :class="`${prefixCls}__right`">
        <div class="mb-3 text-center c-FO-Content-Text1 font-bold">
          自动化上传包体参数
        </div>
        <div :class="`${prefixCls}__right-url`">
          <a-button
            preIcon="ant-design:copy-outlined"
            :class="`${prefixCls}__btn ml-3 !rounded !px-2px !absolute right-6px top-6px`"
            size="small"
            @click="copyText(postUrl)"
          />
          <div class="mr-4 flex flex-col flex-nowrap overflow-auto break-all">
            POST Url: {{ postUrl }}
          </div>
        </div>
        <div :class="`${prefixCls}__right-json`">
          <a-button
            preIcon="ant-design:copy-outlined"
            :class="`${prefixCls}__btn ml-3 !rounded !px-2px !absolute right-6px top-6px z-1`"
            size="small"
            @click="copyText(JSON.stringify(postJson))"
          />
          <!-- eslint-disable vue/attribute-hyphenation -->
          <JsonEditor :value="postJson" mode="preview" :mainMenuBar="false" :statusBar="false" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="tsx" setup name="GamePackageTag">
import { Col as ACol, Popconfirm as APopconfirm, Row as ARow } from 'ant-design-vue';
import { cloneDeep, isEmpty, map, pick, sortBy } from 'lodash-es';
import { computed, nextTick, onMounted, ref, watch } from 'vue';
import type { GameLabelsListItem, GameLabelValuesListItem } from '/@/api/page/model/testModel';
import {
  addGameLabel,
  addGameLabelValue,
  batchGameLabelSort,
  deleteGameLabel,
  deleteGameLabelValue,
  editGameLabel,
  editGameLabelValue,
  getGameLabelByID,
  getGameLabelsListByPage,
} from '/@/api/page/test';
import ColorPopover, { defaultColorList } from '/@/components/ColorPopover';
import Icon from '/@/components/Icon';
import { JsonEditor } from '/@/components/JsonEditor';
import { useTrack } from '/@/hooks/system/useTrack';
import { useDesign } from '/@/hooks/web/useDesign';
import { useMessage } from '/@/hooks/web/useMessage';
import { useSortable } from '/@/hooks/web/useSortable';
import { useLayoutHeight } from '/@/layouts/default/content/useContentViewHeight';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { copyText } from '/@/utils/copyTextToClipboard';
import { isNullOrUnDef } from '/@/utils/is';
import { buildNumberUUID } from '/@/utils/uuid';
import { useGlobSetting } from '/@/hooks/setting';
import { useCurrentProjectInfo } from '../../../../../hooks/useProjects.ts';

const { prefixCls } = useDesign('game-package-tag');
const { headerHeightRef } = useLayoutHeight();
const userStore = useUserStoreWithOut();
const { createMessage, createConfirm } = useMessage();
const { setTrack } = useTrack();

const tagList = ref<GameLabelsListItem[]>([]);
const newTagList = ref<GameLabelsListItem[]>([]);
const hasNewTagEdit = ref(false);
const hasNewTagEditMsg = '有未保存的标签，请先保存再进行其他操作';
const { beOrigin } = useGlobSetting();
const { projectInfo } = useCurrentProjectInfo();
const postUrl = computed(() => {
  return `${beOrigin}/api/v1/projects/${projectInfo.value?.alias || ''}/appstore/game_pkgs/versions/labels`;
});
const postJson = computed(() => ({
  package: '包名示例',
  version: '版本号示例',
  labels: newTagList.value.map((e) => ({
    identifier: e.identifier,
    values: e.singleChoice ? [e.values?.[0]?.value] : map(e.values, 'value'),
  })),
}));

// 通过ID获取标签
async function getTagByID(labelID: number, UUID?: number) {
  if (!userStore.getProjectId) {
    return;
  }
  const { label } = await getGameLabelByID(userStore.getProjectId, labelID);
  if (UUID) {
    tagList.value.push(label!);
  } else {
    const index = tagList.value.findIndex((e) => e.ID === label.ID);
    tagList.value[index] = label;
  }
  newTagList.value = cloneDeep(tagList.value);
}

// 获取标签列表
async function getTagList() {
  tagList.value = [];
  if (!userStore.getProjectId) {
    return;
  }
  const { list } = await getGameLabelsListByPage(userStore.getProjectId, {
    page: 1,
    pageSize: 999,
  });
  tagList.value = list ? sortBy(list, 'sort') : [];
  newTagList.value = cloneDeep(tagList.value);
}

// 添加标签项
async function handleValueAdd(item: GameLabelsListItem) {
  if (isEmpty(item.name?.trim()) || isEmpty(item.identifier?.trim())) {
    createMessage.warning('请先填写 标签名和识别名 后再添加标签项');
    return;
  }
  if (item.values?.some((e) => isEmpty(e.value?.trim()))) {
    return;
  }
  if (!item.values?.length) {
    item.values = [];
  }
  item.hasNewValueEdit = true;
  item.values.push({
    value: '',
    UUID: buildNumberUUID(),
    labelID: item.ID,
  });
}

// 删除标签项
async function handleValueDelete(val: GameLabelValuesListItem, item: GameLabelsListItem) {
  if (val.UUID) {
    item.hasNewValueEdit = false;
  } else {
    await deleteGameLabelValue(userStore.getProjectId, val.ID!);
  }
  getTagByID(val.labelID!);
}

// 保存标签项
async function handleValueSave(val: GameLabelValuesListItem, item: GameLabelsListItem) {
  setTrack('nzdc1sbhsj');
  if (isEmpty(val.value?.trim())) {
    createMessage.warning('标签项 不能为空');
    return;
  }
  // 标签项重复判断
  const hasRepeat = item.values?.some((e) => e.value === val.value && e.ID !== val.ID);
  if (hasRepeat) {
    createMessage.warning(`标签项【${val.value}】重复`);
    return;
  }
  const origin = tagList.value
    .find((e) => e.ID === item.ID)
    ?.values
    ?.find((e) => e.ID === val.ID);
  const noChange = origin?.value === val.value;
  if (noChange) {
    return;
  }
  const submitData = {
    labelID: item.ID!,
    value: val.value,
  };
  const res = val.UUID
    ? await addGameLabelValue(userStore.getProjectId, submitData)
    : await editGameLabelValue(userStore.getProjectId, submitData, val.ID!);
  if (res?.code !== 7) {
    setTrack('oxht7ndgoq');
    if (val.UUID) {
      val.UUID = undefined;
      item.hasNewValueEdit = false;
    }
    getTagByID(item.ID!);
    hasNewTagEdit.value = false;
  }
}

// 初始化拖拽
function initDrag() {
  nextTick(() => {
    const el = document.querySelector(`.${prefixCls}__forms`) as HTMLElement;
    const { initSortable } = useSortable(el, {
      handle: `.${prefixCls}__forms-drag-btn`,
      animation: 150,
      onEnd: async ({ oldIndex, newIndex }) => {
        if (isNullOrUnDef(oldIndex) || isNullOrUnDef(newIndex) || oldIndex === newIndex) {
          return;
        }
        const currentGroup = newTagList.value[oldIndex];
        newTagList.value.splice(oldIndex, 1);
        newTagList.value.splice(newIndex, 0, currentGroup);
        await batchGameLabelSort(userStore.getProjectId, {
          idList: newTagList.value.map((e) => e.ID!),
        });
        await init();
      },
    });
    initSortable();
  });
}

async function init() {
  await getTagList();
  initDrag();
}

// 添加新标签
async function handleAdd() {
  hasNewTagEdit.value = true;
  newTagList.value.push({
    name: '',
    identifier: '',
    singleChoice: false,
    color: defaultColorList[0],
    values: [],
    sort: newTagList.value.length + 1,
    UUID: buildNumberUUID(),
  });
}

// 删除新标签
async function handleDelete(item: GameLabelsListItem) {
  if (item.UUID) {
    newTagList.value = newTagList.value.filter((e) => e.UUID !== item.UUID);
    hasNewTagEdit.value = false;
    return;
  } else if (hasNewTagEdit.value) {
    createMessage.warning(hasNewTagEditMsg);
    return;
  }
  const res = await deleteGameLabel(userStore.getProjectId, item.ID!);
  if (res?.code === 7) {
    return;
  }
  newTagList.value = newTagList.value.filter((e) => e.ID !== item.ID);
  await batchGameLabelSort(
    userStore.getProjectId,
    {
      idList: newTagList.value.map((e) => e.ID!),
    },
    'none',
  );
}

// 保存新标签
async function handleSave(item: GameLabelsListItem, label: string) {
  if (!item.UUID) {
    setTrack('nzdc1sbhsj');
  }
  if (isEmpty(item.name?.trim()) || isEmpty(item.identifier?.trim())) {
    if (!item.UUID && label !== 'color') {
      createMessage.warning('标签名 或 识别名 不能为空');
    }
    return;
  }
  // 判断是否有重复标识名
  const hasRepeat = newTagList.value.some(
    (e) => e.identifier === item.identifier && e.ID !== item.ID,
  );
  if (hasRepeat) {
    createMessage.warning(`识别名【${item.identifier}】重复`);
    return;
  }
  const origin = tagList.value.find((e) => e.ID === item.ID);
  const noChange = origin?.[label] === item[label];
  if (noChange) {
    return;
  }
  const submitData = pick(item, ['name', 'identifier', 'singleChoice', 'color', 'sort']);
  if (item.UUID) {
    const res = await addGameLabel(userStore.getProjectId, submitData);
    if (res?.code !== 7) {
      setTrack('b6oueu89ip');
      getTagByID(res.id, item.UUID);
      item.UUID = undefined;
      hasNewTagEdit.value = false;
    }
  } else {
    const res = await editGameLabel(userStore.getProjectId, submitData, item.ID!);
    if (res?.code !== 7) {
      setTrack('oxht7ndgoq');
      getTagByID(item.ID!);
      hasNewTagEdit.value = false;
    }
  }
}

// 处理标签类型切换
async function handleTypeChange(item: GameLabelsListItem, singleChoice = false) {
  if (item.singleChoice === singleChoice) {
    return;
  }
  if (singleChoice) {
    createConfirm({
      iconType: 'warning',
      title: '切换为单选',
      content: (
        <div>
          将标签类型从多选改为单选，会取消选中已配置该标签包体的多余选项，
          <b>仅保留第一个。</b>
        </div>
      ),
      okText: '确认',
      onOk: async () => {
        item.singleChoice = singleChoice;
        await handleSave(item, 'singleChoice');
      },
    });
  } else {
    item.singleChoice = singleChoice;
    await handleSave(item, 'singleChoice');
  }
}

onMounted(() => {
  init();
});

watch(
  () => userStore.getProjectId,
  () => {
    init();
  },
);
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-game-package-tag';
.@{prefix-cls} {
  position: relative;
  padding: 16px;
  border-radius: 8px;
  background-color: @FO-Container-Fill1;

  &__body {
    display: flex;
    margin-top: 8px;
    padding: 8px 2px;
    overflow: auto;
  }

  &__left {
    flex: 1;
    width: 0;
  }

  &__right {
    @apply lt-md:w-100px lt-lg:w-200px lt-xl:w-300px;

    display: flex;
    position: sticky;
    top: 0;
    flex-direction: column;
    align-items: center;
    width: 400px;
    margin-left: 16px;
    padding: 16px;
    border-radius: 8px;
    background-color: @member-card-background;

    &-json,
    &-url {
      position: relative;
      width: 100%;
      padding: 8px;
      border-radius: 8px;
      background-color: @FO-Container-Fill1;
    }

    &-json {
      flex: 1;
      height: 0;
      margin-top: 16px;

      & .jsoneditor {
        border: none !important;
      }
    }
  }

  &__forms {
    &-item,
    &-add {
      display: flex;
      align-items: center;
      padding: 16px;
      border-radius: 8px;
      background-color: @member-card-background;
    }

    &-item {
      position: relative;
      margin-bottom: 16px;

      &[isNew='true'] {
        border: 1px dashed @FO-Functional-Success1-Default;

        &::before {
          content: '未保存';
          position: absolute;
          top: -8px;
          left: 8px;
          padding: 2px 4px;
          border-radius: 4px;
          background-color: @FO-Functional-Success1-Default;
          color: #fff;
          font-size: 12px;
        }
      }

      &-name-input {
        position: relative;
        box-sizing: border-box;
        flex: 1;
        transition: all 0.2s;
        border-radius: 6px;
      }

      &-input {
        background-color: @forgeon-btn-normal-bg-color;
        font-weight: bold;
        &[isNew='true'] {
          border-style: dashed;
        }
      }

      &-radio {
        display: flex;
        padding: 2px 8px 2px 2px;
        border-radius: 100px;
        background-color: @forgeon-btn-normal-bg-color;
        font-weight: bold;

        & > div {
          padding: 1px 8px;
          cursor: pointer;

          &[active='true'] {
            background-color: @forgeon-btn-selected-bg-color;
            color: #fff;
          }

          &:first-child {
            margin-right: 8px;
            border-radius: 100px;
          }

          &:last-child {
            border-radius: 5px;
          }
        }
      }
    }

    &-add {
      justify-content: center;
      cursor: pointer;
    }

    &-drag-btn {
      cursor: grab !important;
    }
  }

  &__val-del-btn,
  &__btn {
    border-color: @forgeon-btn-normal-bg-color !important;
    background-color: @forgeon-btn-normal-bg-color!important;
    color: @forgeon-btn-normal-text-color !important;
  }

  &__msg {
    margin-left: 8px;
    padding: 0 6px;
    border-radius: 5px;
    background-color: @FO-Functional-Warning1-Default;
    color: @FO-Content-Text1;
    font-size: 14px;
    font-weight: bold;
    line-height: 26px;
    user-select: none;
  }

  &__btn {
    &[isSingle='true'] {
      border-radius: 100px !important;
    }
  }

  &__val-del-btn {
    display: flex;
    position: absolute !important;
    top: -6px;
    right: 4px;
    align-items: center;
    justify-content: center;
    min-width: 8px !important;
    height: 16px !important;
    font-size: 6px !important;
  }
}
</style>
