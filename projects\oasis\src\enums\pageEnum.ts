import { PlatformEnterPoint, PlatformRoutePath } from '@hg-tech/oasis-common';
/**
 * @deprecated Use PlatformRoutePath instead
 */
export enum PageEnum {
  // basic login path
  BASE_LOGIN = '/login',
  // basic home path
  BASE_HOME = '/home',

  // 飞书登录回调
  FEISHU_RETURN = '/feishuReturn',
  // sso登录回调
  SSO_RETURN = '/ssoReturn',
  // link to other
  LINK_TO_OTHER = '/link/other',
  // 部门资产
  DEPT_ASSET_PAGE = '/deptAsset',
  // 埋点
  TRACKING_PAGE = '/tracking',
  // py脚本
  PYTHON_SCRIPT_PAGE = '/instructions',

  PERFORMANCE_REFERENCE = '/performance/reference',
  // 游戏安全
  GAME_SECURE = '/secure',
  // 系统管理
  SYSTEM_PAGE = '/system',
  // 工具商店
  TOOLKIT_PAGE = '/toolkit',
  // 工具商店详情
  TOOLKIT_DETAIL_PAGE = '/toolkit/detail',
  // 首页配置
  HOME_SETTINGS_PAGE = '/homeSettings',
  // Oasis后台
  OASIS_TOOLS = '/oasis-tools',
  // 工具商店配置
  TOOLKIT_SETTINGS_PAGE = '/toolkitSettings',
  // 材质库
  MATERIAL_LIB_PAGE = '/materialLib',

  // 版本控制
  VERSION_CONTROL = '/versionControl',

  // error page path
  ERROR_PAGE = '/exception',
  // error log page path
  ERROR_LOG_PAGE = '/error-log/list',

  P4_TRAIN_LIST_PAGE = '/p4Training',
  P4_EXAM_PAGE = '/tcp4t',
  P4_TRAINS_PAGE = '/p4Trains',
  P4_TOUCH_PROCESS_PAGE = '/p4Onboarding',
  PERFORMANCE_HEAT_MAP_PAGE = '/performance/heatMap',
  PERMISSION_CENTER = '/permission-center',
}
export const noLoginPages = [
  PlatformEnterPoint.AssetLib,
  PlatformEnterPoint.DevGuard,
  PlatformEnterPoint.FeishuReturn,
  PlatformEnterPoint.GamePackageCard,
  PlatformEnterPoint.GamePackageDoctorProportion,
  PlatformEnterPoint.GamePackageDoctorTrend,
  PlatformEnterPoint.GamePackageVersionSimple,
  PlatformEnterPoint.HDALib,
  // PlatformEnterPoint.Home,
  PlatformEnterPoint.LinkToOther,
  PlatformEnterPoint.LoadTest,
  PlatformEnterPoint.Login,
  PlatformEnterPoint.MaterialLib,
  PlatformEnterPoint.PerfdeepCardCompare,
  PlatformEnterPoint.PerfdeepCardTrend,
  PlatformEnterPoint.Performance,
  PlatformEnterPoint.PerformanceCard,
  PlatformEnterPoint.PerformanceCardCompare,
  PlatformEnterPoint.PerformanceCardTrend,
  PlatformEnterPoint.SSOReturn,
  PlatformEnterPoint.Services,
  PlatformEnterPoint.Test,
];

// 无项目下拉页面
export const noProjectPages = [
  PlatformEnterPoint.System,
  PlatformEnterPoint.DeptAsset,
  PlatformEnterPoint.Tracking,
  PlatformEnterPoint.PerformanceReference,
  PlatformEnterPoint.Toolkit,
  PlatformEnterPoint.TCP4T,
  PlatformEnterPoint.P4Trains,
  PlatformEnterPoint.Instructions,
  PlatformEnterPoint.P4Onboarding,
  PlatformEnterPoint.Home,
  PlatformEnterPoint.InstructionCombinations,
  PlatformEnterPoint.InstructionComponents,
  PlatformEnterPoint.TrackingAnalysisSettings,
  PlatformEnterPoint.DeptAssetApplyManagement,
  PlatformEnterPoint.DeviceManagementAdminConfig,
  PlatformEnterPoint.DeviceManagementLogs,
  PlatformEnterPoint.DeviceManagementFaultList,
  PlatformEnterPoint.CloudDevice,
  PlatformEnterPoint.CloudDeviceDetail,
  PlatformEnterPoint.AccountSettings,
  PlatformEnterPoint.MenuManagement,
  PlatformEnterPoint.ProjectsManagement,
  PlatformEnterPoint.RoleManage,
  PlatformEnterPoint.ResourceCheckSwitchesTemplate,
  PlatformEnterPoint.ResourceCheckTemplate,
  PlatformEnterPoint.DeviceManagement,
  PlatformEnterPoint.P4TriggersOverview,
  PlatformEnterPoint.P4TriggersParamsSettings,
  PlatformEnterPoint.SystemPerforceManagement,
  PlatformEnterPoint.PerforceServersSettings,
  PlatformEnterPoint.PerforceAccessLevelsSettings,
  PlatformEnterPoint.SecureChannelsSettings,
  PlatformEnterPoint.TCP4TSelectionsSettings,
  PlatformEnterPoint.TCP4TOperationsSettings,
  PlatformEnterPoint.P4TrainsSettings,
  PlatformEnterPoint.P4OnboardingSettings,
  PlatformEnterPoint.SysAigc,
  PlatformEnterPoint.ProjectMember,
  PlatformEnterPoint.ProjectPermissionManagement,
  PlatformEnterPoint.ToolGroupChat,
];

// 永远深色模式的页面
export const alwaysDarkPages = [
  PlatformRoutePath.LinkToOther,
  PlatformRoutePath.PerformanceHeatMap,
];

// 永远浅色模式的页面
export const alwaysLightPages = [
  PlatformRoutePath.P4Onboarding,
  PlatformRoutePath.SysAdmin,
];

export const PageWrapperFixedHeightKey = 'PageWrapperFixedHeight';
