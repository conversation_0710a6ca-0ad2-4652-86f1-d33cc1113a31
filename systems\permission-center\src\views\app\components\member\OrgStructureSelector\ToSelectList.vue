<template>
  <div class="flex flex-col overflow-hidden">
    <PermissionSolidTabs
      v-model:value="currentTab"
      class="mb-12px mr-16px flex-none"
      :tabList="validTabs"
    />
    <Breadcrumb v-if="selectedTreeSchema && treeLevels.length && !searchKw" separator=">" class="FO-Font-R12 mb-8px py-3px">
      <BreadcrumbItem v-for=" i in treeLevels " :key="i.key">
        <a class="!bg-transparent !c-FO-Content-Text2" @click="() => handleBackNode(i)">
          <RenderMaybeVNode
            v-if="selectedTreeSchema?.breadcrumbRender"
            :nodes="selectedTreeSchema.breadcrumbRender(i)"
          />
        </a>
      </BreadcrumbItem>
    </Breadcrumb>
    <div ref="cellContainerRef" class="flex-auto overflow-auto">
      <Spin :spinning="isLoading">
        <Empty v-if="!currentList.length" class="mt-40px" />
        <div class="flex flex-col gap-4px pr-16px">
          <OrgStructureSelectorCell
            v-for="i in currentList"
            :key="i.key"
            :data="i"
            class="transition-300"
            :class="[
              { '!bg-FO-Brand-Tertiary-Active': isItemSelected(i) },
              isItemDisabled(i) ? 'cursor-not-allowed' : 'cursor-pointer hover:bg-FO-Container-Fill2',
            ]"
            @click="() => handleSelected(i)"
          >
            <template #prefix>
              <Checkbox
                :checked="isItemSelected(i) || (props.showCheckedWhenDisabled && isItemDisabled(i)) || false"
                :disabled="isItemDisabled(i)"
                :class="{
                  'disabled-checked': props.showCheckedWhenDisabled && isItemDisabled(i),
                }"
              />
            </template>
            <template v-if="selectedTreeSchema && !searchKw && i.type && selectedTreeSchema.treeTypeList?.includes(i.type)" #suffix>
              <Button class="!px-8px" :class="isItemSelected(i) ? 'btn-fill-secondary' : 'btn-fill-text'" @click.stop="() => handleEnterNode(i)">
                <template #icon>
                  <FillArrowDown class="text-16px c-FO-Content-Icon3 -rotate-90" />
                </template>
              </Button>
            </template>
          </OrgStructureSelectorCell>
        </div>
      </Spin>
    </div>
  </div>
</template>

<script setup lang="ts" generic="K extends string, T extends OrgStructureCellInfo<K>">
import { computed, ref, shallowRef, watch } from 'vue';
import { computedWithControl, useInfiniteScroll } from '@vueuse/core';
import { RenderMaybeVNode } from '@hg-tech/oasis-common';
import { Breadcrumb, BreadcrumbItem, Button, Checkbox, Empty, Spin } from 'ant-design-vue';
import OrgStructureSelectorCell from './OrgStructureSelectorCell.vue';
import type { OrgStructureCellInfo, OrgStructurePayload, OrgStructureSchema, OrgStructureSchemaTree } from './type.ts';
import PermissionSolidTabs from '../../../../../components/PermissionSolidTabs.vue';
import { isEqual, last } from 'lodash';
import { type UseInfinityLoadReq, useInfinityLoad } from '../../../../../composables/useInfinityLoad.ts';
import FillArrowDown from '../../../../../assets/icons/fill-arrow-down.svg?component';

const props = defineProps<{
  searchKw?: string;
  schemas?: OrgStructureSchema<K, T>[];
  disabledKeys?: OrgStructureCellInfo<K>['key'][];
  /** 禁用时显示为已勾选 */
  showCheckedWhenDisabled?: boolean;
}>();

const selectedPayloads = defineModel<OrgStructurePayload<K, T>[]>('selectedPayloads', { required: true });
const currentTab = ref<string>();
const validTabs = computed(() => {
  const supportedSchema = props.searchKw
    ? props.schemas?.filter((schema) => schema.searchFunc)
    : props.schemas?.filter((schema) => schema.initFunc != null && schema.searchFunc);

  return (supportedSchema || []).map((schema) => ({
    key: schema.type,
    label: schema.title,
  }));
});
watch(validTabs, (tabs, prvTabs) => {
  if (tabs.length > 0 && !isEqual(tabs, prvTabs)) {
    // 当 tab 变化时，重置 currentTab
    currentTab.value = tabs[0].key;
  }
}, { flush: 'sync', immediate: true });
const selectedSchema = computed(() => props.schemas?.find((schema) => schema.type === currentTab.value));
const selectedTreeSchema = computed<OrgStructureSchemaTree<K, T> | undefined>(() => {
  return (selectedSchema.value as unknown as OrgStructureSchemaTree<K, T>)?.treeTypeList?.length ? selectedSchema.value as OrgStructureSchemaTree<K, T> : undefined;
});
const treeLevels = shallowRef<T[]>([
  { title: '鹰角' } as T,
]);

// 记录当前分类，用于检测分类切换
const currentSchemaType = ref<string>();

const { currentList, loadMore, isLoading, isEnd } = useInfinityLoad<T>(
  computedWithControl<UseInfinityLoadReq<T>, unknown>(
    [() => props.searchKw, treeLevels, selectedSchema],
    () => {
      // 检测分类切换，重置树形结构
      if (selectedSchema.value?.type !== currentSchemaType.value) {
        currentSchemaType.value = selectedSchema.value?.type;
        treeLevels.value = [{ title: '鹰角' } as T];
      }

      if (props.searchKw) {
        return async (pageInfo) => selectedSchema.value?.searchFunc(pageInfo, props.searchKw);
      }
      return async (pageInfo) => selectedSchema.value?.initFunc?.(pageInfo, last(treeLevels.value)?.key);
    },
  ),
  {
    getKey: (i) => i.key,
    immediate: true,
  },
);

// 缓存选中状态，避免重复计算
const selectedKeysMap = computed(() => {
  const map = new Map<string, boolean>();
  selectedPayloads.value.forEach((p) => {
    map.set(`${p.data.key}-${p.data.type}`, true);
  });
  return map;
});

// 检查项目是否被选中
function isItemSelected(item: T): boolean {
  return selectedKeysMap.value.has(`${item.key}-${item.type}`);
}

// 检查项目是否被禁用
function isItemDisabled(item: T): boolean {
  return props.disabledKeys?.includes(item.key) || false;
}

const cellContainerRef = shallowRef<HTMLDivElement>();
useInfiniteScroll(cellContainerRef, () => loadMore(), {
  canLoadMore: () => !(isLoading.value || isEnd.value),
});
// 切换 tab 时滚动到顶部
watch(currentTab, () => {
  cellContainerRef.value?.scrollTo(0, 0);
});

function handleSelected(item: T) {
  // 如果项目被禁用，则不允许选择
  if (isItemDisabled(item)) {
    return;
  }

  const selected = selectedPayloads.value.find((i) => i.data.key === item.key && i.data.type === item.type);
  if (selected) {
    // 已选中，取消选中
    selectedPayloads.value = selectedPayloads.value.filter((i) => !(i.data.key === item.key && i.data.type === item.type));
  } else {
    // 未选中，添加到已选列表
    selectedPayloads.value = [
      ...selectedPayloads.value,
      {
        key: item?.type,
        data: item,
      } as OrgStructurePayload<K, T>,
    ];
  }
}

function handleBackNode(i: T) {
  const toIdx = treeLevels.value.findIndex((j) => j.key === i.key);
  if (toIdx >= 0 && toIdx < treeLevels.value.length - 1) {
    treeLevels.value = treeLevels.value.slice(0, toIdx + 1);
  }
}
function handleEnterNode(i: T) {
  treeLevels.value = [...treeLevels.value, i];
}
</script>

<style scoped lang="less">
:deep(.ant-breadcrumb) {
  &-link a {
    height: unset;
    margin: 0 !important;
  }
  &-separator {
    margin: 0 2px;
  }
}

.disabled-checked {
  &:deep(.ant-checkbox-checked .ant-checkbox-inner) {
    background-color: var(--FO-Brand-Primary-Disabled) !important;
    border-color: var(--FO-Brand-Primary-Disabled) !important;

    &::after {
      border-color: var(--FO-Content-Components1) !important;
    }
  }
}
</style>
