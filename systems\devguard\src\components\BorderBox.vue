<template>
  <div class="form-border-box b-1 b-FO-Container-Stroke1" v-bind="$attrs">
    <slot name="title" titleClass="form-border-box__title">
      <div v-if="label" class="form-border-box__title bg-FO-Container-Fill1">
        {{ label }}
      </div>
    </slot>
    <slot name="subTitle" subTitleClass="form-border-box__sub-title c-FO-Content-Text2">
      <div v-if="subLabel" class="form-border-box__sub-title c-FO-Content-Text2">
        {{ subLabel }}
      </div>
    </slot>
    <slot />
  </div>
</template>

<script lang="ts" setup>
defineProps({
  label: {
    type: String,
    default: () => '',
  },
  subLabel: {
    type: String,
    default: () => '',
  },
});
</script>

<style lang="less">
.form-border-box {
  position: relative;
  margin-top: 16px;
  padding: 16px 16px 16px 24px;

  border-radius: 6px;

  &__title {
    position: absolute;
    z-index: 1;
    top: 0;
    left: 16px;
    width: fit-content;
    padding: 0 8px;
    font-weight: bold;
    transform: translateY(-50%);
  }

  &__sub-title {
    margin-bottom: 8px;
    font-size: 12px;
  }
}

[data-theme='dark'] {
  .form-border-box {
    &__title {
      background-color: inherit;
    }
  }
}
</style>
