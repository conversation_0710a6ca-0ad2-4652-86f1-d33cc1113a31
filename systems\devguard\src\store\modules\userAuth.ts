import { defineStore } from 'pinia';
import { useMicroAppInject, useUserAuthInfoCtx, useUserProfileInfoCtx } from '@hg-tech/oasis-common';

/**
 * 从父应用获取用户信息
 */
export const useUserAuthStore = defineStore('userAuth', () => {
  const { data, loading } = useMicroAppInject(useUserAuthInfoCtx);
  const { data: userProfile } = useMicroAppInject(useUserProfileInfoCtx);
  return {
    userAuthInfo: data,
    userProfile,
    loading,
  };
});
