<template>
  <Modal
    width="620px" :open="show" :title="title" :centered="true" :maskClosable="false" :destroyOnClose="true"
    :afterClose="modalDestroy" :bodyStyle="{ padding: '20px 0 0 0', marginBottom: '36px' }"
    @cancel="() => modalCancel()"
  >
    <Form :labelCol="{ style: { width: '96px' } }">
      <FormItem label="权限名称" v-bind="validateInfos.name">
        <Input
          v-model:value="formValue.name" placeholder="请输入权限名称" :maxlength="30"
          :disabled="props.disabledFields?.includes('name')" :showCount="true"
        />
      </FormItem>
      <FormItem label="权限 Code" v-bind="validateInfos.code">
        <Input
          v-model:value="formValue.code" placeholder="请输入权限 Code" :maxlength="30"
          :disabled="props.disabledFields?.includes('code')" :showCount="true"
        />
      </FormItem>
      <FormItem label="权限说明" v-bind="validateInfos.description">
        <Input
          v-model:value="formValue.description" placeholder="请输入权限说明" :maxlength="30"
          :disabled="props.disabledFields?.includes('description')" :showCount="true"
        />
      </FormItem>
      <FormItem label="权限分类" v-bind="validateInfos.categoryId">
        <Select
          v-model:value="formValue.categoryId"
          :options="checkPointCategory?.map((item) => ({ label: item.name, value: item.id }))" placeholder="请选择权限分类"
          :loading="loadingCategory" :disabled="props.disabledFields?.includes('categoryId') "
        />
      </FormItem>
    </Form>
    <template #footer>
      <div class="flex justify-end gap-4px">
        <Button class="btn-fill-default w-100px" @click="() => modalCancel()">
          取消
        </Button>
        <Button class="btn-fill-primary w-100px" :loading="sending" @click="handleConfirm">
          保存
        </Button>
      </div>
    </template>
  </Modal>
</template>

<script setup lang="tsx">
import { type ModalBaseProps, useLatestPromise } from '@hg-tech/utils-vue';
import {
  type PermissionCheckPoint,
  type PermissionCheckPointCreate,
  fetchPermissionCheckPointCategory,
} from '../../../api/manage.ts';
import { Button, Form, FormItem, Input, Modal, Select } from 'ant-design-vue';
import { computed, ref, watch } from 'vue';
import type { RuleObject } from 'ant-design-vue/es/form/interface';

type PointFormValue = Pick<PermissionCheckPoint, 'name' | 'code' | 'description' | 'categoryId'>;

const props = defineProps<ModalBaseProps & {
  appId?: PermissionCheckPoint['appId'];
  title?: string;
  initData?: PointFormValue;
  disabledFields?: (keyof PointFormValue)[];
  sentReq?: (formValue: PermissionCheckPointCreate) => Promise<PermissionCheckPoint | undefined>;
}>();

const formValue = ref<PointFormValue>({
  name: undefined,
  code: undefined,
  description: undefined,
  categoryId: undefined,
  ...props.initData,
});

const formRule = computed<Record<keyof PointFormValue, RuleObject[]>>(() => ({
  name: [
    { required: true, message: '请输入权限名称' },
    { max: 30, message: '长度不能超过30个字符' },
  ],
  code: [
    { required: true, message: '请输入权限 Code' },
    { pattern: /^\w+$/, message: '只能输入字母、数字、下划线' },
    { max: 30, message: '长度不能超过30个字符' },
  ],
  description: [
    { max: 30, message: '长度不能超过30个字符' },
  ],
  categoryId: [],
}));
const { validate, validateInfos } = Form.useForm(formValue, formRule);
const { data: checkPointCategoryRes, loading: loadingCategory, execute: fetchCategory } = useLatestPromise(fetchPermissionCheckPointCategory);
const checkPointCategory = computed(() => checkPointCategoryRes.value?.data?.data ?? []);
const { execute: send, loading: sending } = useLatestPromise(props.sentReq!);

watch(() => props.appId, (v) => {
  if (v) {
    fetchCategory({ appId: props.appId }, {});
  }
}, { immediate: true });

async function handleConfirm() {
  try {
    await validate();
    const result = await send(formValue.value);
    if (result) {
      return props.modalConfirm();
    }
  } catch (error) {
    console.error('保存失败:', error);
  }
}
</script>
