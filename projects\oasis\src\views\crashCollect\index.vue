<template>
  <div @click="pageClick">
    <div :class="prefixCls">
      <div ref="headerRef" :class="`${prefixCls}__header`" class="gap-[10px]">
        <BasicForm :class="`${prefixCls}__filter-form`" class="flex-1" @register="clEngineForm">
          <template #head>
            <div class="flex items-center">
              <div class="mr-5 flex items-center">
                <div :class="`${prefixCls}__choose-tag line-height-100%`">
                  <div
                    :class="`${prefixCls}__choose-tag-item  line-height-100%`" :active="curChoose === 'TriggerTime'"
                    @click="handleChangeFilter('TriggerTime')"
                  >
                    按触发时间
                  </div>
                  <div
                    :class="`${prefixCls}__choose-tag-item  line-height-100%`" :active="curChoose === 'Class'"
                    @click="handleChangeFilter('Class')"
                  >
                    按分类
                  </div>
                </div>
              </div>
            </div>
          </template>
          <template #time>
            <ARangePicker v-model:value="createdAt" class="max-w-[210px]" :getPopupContainer="getPopupContainer" @change="dataChange" />
          </template>
        </BasicForm>

        <div v-if="isSuperAdminOrProjectAdmin">
          <a-button size="small" :class="`${prefixCls}__config-btn`" @click="openSetting()">
            <Icon icon="icon-park-solid:setting" />
            配置
          </a-button>
        </div>
      </div>
      <div :class="`${prefixCls}__body`" :style="{ maxHeight: bodyHeight }">
        <template v-if="crashListByTime.length && curChoose === 'TriggerTime'">
          <div v-for="item in crashListByTime" :key="item.ID" class="b-FO-Container-Fill0 cursor-pointer b-1px b-rd-4px" :class="{ ' !b-FO-Brand-Primary-Default': nowDetail === item.ID }" @click="openDetail(item.ID!)">
            <div class="bg-FO-Container-Fill1 mr-2 min-w-[1200px] flex flex-col items-start justify-between rounded-md p-4">
              <div class="w-full flex cursor-pointer justify-between gap-4">
                <div class="flex flex-1 flex-wrap items-center gap-2 overflow-hidden">
                  <div class="mr-3 font-bold">
                    Crash-{{ item.ID }}
                  </div>
                  <div v-if="item.bugID" :class="`${prefixCls}__has-bug mr-3`">
                    已提单
                  </div>
                  <div>{{ formatTISOToDate(item.time) }}</div>
                  <div class="ml-10">
                    触发人：{{ item.equipment?.user }}
                  </div>
                  <div class="mx-10 flex items-center">
                    <span>引擎版本：</span><EllipsisText class="max-w-300px">
                      {{ item.engineVersion?.engineVersion }}
                    </EllipsisText>
                  </div>
                  <div v-if="item?.streamClVersion" class="flex items-center overflow-hidden">
                    <span> 工程CL号：</span><EllipsisText class="max-w-300px">
                      {{ item?.streamClVersion }}
                    </EllipsisText>
                  </div>
                </div>

                <div
                  class="relative h-[32px] flex cursor-pointer items-center font-bold"
                  :style="`background: ${getBackgroundColor(item.category?.name)};`"
                  @click.stop="openDetail(item.categoryID!, true)"
                >
                  <div :class="`${prefixCls}_rhombus-time`" />
                  <div class="m-2 ml-5 max-w-[500px] flex items-center !color-[#252525]">
                    <EllipsisText>
                      {{ categoryName(item) }}
                    </EllipsisText>
                  </div>
                </div>
              </div>
              <CrashLabel :labelList="labelList" :item="item" :clickLabelId="clickLabelId" @clickAddLabel="clickAddLabel" @addLabel="addLabel" />
              <div class="bg-FO-Container-Fill3 mt-2 w-full b-rd-[8px] p-2">
                <pre
                  v-if="item.coreDmp"
                  :class="`${prefixCls}__pre ${prefixCls}__detail-log`"
                  class="mb-0 w-full"
                >{{ item.coreDmp }}</pre>
                <div v-else-if="!item.coreDmp && !item.categoryID">
                  正在获取堆栈信息...
                </div>
                <div v-else-if="!item.coreDmp && item.categoryID">
                  无法获取堆栈信息
                </div>
              </div>
            </div>
          </div>
        </template>
        <template v-else-if="updateCategoriesName.length && curChoose === 'Class'">
          <div v-for="item in crashListByClass" :key="item.ID">
            <div class="b-FO-Container-Fill0 bg-FO-Container-Fill1 mr-2 min-w-[1200px] b-1px rounded-md p-4" :class="{ ' !b-FO-Brand-Primary-Default': nowDetail === item.ID && item.recordCount < 3 }">
              <div
                class="relative flex cursor-pointer items-center justify-between font-bold"
                :style="`background: ${getBackgroundColor(item.name)};`" @click="openDetail(item.ID!, true)"
              >
                <div :class="`${prefixCls}_rhombus-class`" />
                <div v-if="openValue !== item.ID" class="m-2 ml-5 color-[#252525]">
                  #{{ item.ID }}.{{ item.name }}
                  <Icon
                    v-if="item.ID" icon="icon-park-outline:editor" class="cursor-pointer"
                    @click.stop="(e) => addOpenValue(e, item.ID, item.name || '')"
                  />
                </div>
                <div v-else :class="`flex flex-items-center ml-5  h-[32px] ${prefixCls}_input-class`">
                  <span class="mr-2">#{{ item.ID }}</span>
                  <div class="bg-FO-Container-Fill1 mx-2 my-1 h-[70%] flex items-center rounded-[6px]" @click.stop>
                    <FitInput
                      v-model="inputValue" :minWidth="70" class="h-[100%]" :inputProps="{
                        placeholder: '分类名称',
                        maxlength: 30,
                        size: 'small',
                        bordered: false,
                      }"
                    />
                    <Icon
                      icon="icon-park-outline:check-small" size="13" class="mx-1 cursor-pointer"
                      @click="checkClick(item.ID, inputValue)"
                    />
                    <Icon icon="icon-park-outline:return" size="13" class="mx-1 cursor-pointer" @click="returnClick" />
                  </div>
                </div>

                <div class="flex items-center">
                  <div v-if="item.hasBug" class="c-FO-Brand-Primary-Default mr-3 border-2 b-rounded-6 px-2 py-[2px] font-size-[12px]">
                    已提单
                  </div>
                  <div class="mr-4 font-bold" :style="`color: ${getTotalColor(item.name)};`">
                    {{ item.recordCount }}
                  </div>
                </div>
              </div>
              <div class="ml-4 mt-4 h-[60px] flex gap-2">
                <div v-for="v in item.records?.slice(0, 3)" :key="v.ID" class="h-full flex flex-1">
                  <div
                    :class="[
                      `${prefixCls}_filter-class-item rounded-md flex-1 flex flex-col justify-between cursor-pointer b-1 b-FO-Container-Fill0 `,
                      { '!b-FO-Brand-Primary-Default': nowDetail === v.ID },
                    ]"
                    @click="openDetail(v.ID!)"
                  >
                    <div class="flex items-center">
                      <div class="mr-3 font-bold">
                        Crash-{{ v.ID }}
                      </div>
                      <div v-if="v.bugID" :class="`${prefixCls}__has-bug`">
                        已提单
                      </div>
                    </div>
                    <div class="flex justify-between">
                      <div>{{ formatTISOToDate(v.time) }}</div>
                      <div>触发人：{{ v.equipment?.user }}</div>
                    </div>
                  </div>
                </div>
                <div v-if="item.records!.slice(0, 3).length < 3" class="flex-1" />
                <div v-if="item.records!.slice(0, 3).length < 2" class="flex-1" />
                <div
                  v-if="item.recordCount > 3" class="b-FO-Container-Fill0 h-full w-[16%] cursor-pointer b-1 b-rd-8px"
                  :class="
                    { '!b-FO-Brand-Primary-Default': nowDetail === item.ID && item.recordCount > 3 }
                  "
                  @click="openDetail(item.ID!, true)"
                >
                  <div :class="`${prefixCls}_filter-class-more rounded-md flex justify-center items-center h-full`">
                    <Icon icon="icon-park-outline:more" size="24" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
        <div v-else-if="!isLoading">
          <div :class="`${prefixCls}__no-data bg-FO-Container-Fill1`">
            暂无崩溃记录
          </div>
        </div>
      </div>
      <div ref="footerRef" :class="`${prefixCls}__footer`">
        <div class="flex justify-end p-4">
          <APagination
            v-model:current="page" v-model:pageSize="pageSize" size="small" :total="totalNum"
            showSizeChanger :pageSizeOptions="['10', '20', '50', '100']" :showTotal="(total) => `共 ${total} 条数据`"
            @change="handlePageChange"
          />
        </div>
      </div>
    </div>

    <SettingModal @register="registerModal" @success="handleSuccess" @getLabelList="editLabelList" />
  </div>
</template>

<script lang="ts" setup>
import { useDebounceFn } from '@vueuse/core';
import { Pagination as APagination, RangePicker as ARangePicker } from 'ant-design-vue';
import { onBeforeMount, onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import SettingModal from './CrashSettingModal.vue';
import CrashLabel from './components/CrashLabel.vue';
import { clEngineVersionsFormSchema } from './crashCollect.data';
import { useCrashCollect } from './crashCollectHook';
import { getCrashLabels, updateCategoriesName } from '/@/api/page/crashCollect';
import { FitInput } from '/@/components/FitInput';
import { BasicForm, useForm } from '/@/components/Form/index';
import Icon from '/@/components/Icon';
import { useModal } from '/@/components/Modal';
import { useDesign } from '/@/hooks/web/useDesign';
import { useGo } from '/@/hooks/web/usePage';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { formatTISOToDate } from '/@/utils/dateUtil';
import { getAllPaginationList } from '/@/hooks/web/usePagination';
import { useAdmin } from '../../hooks/useProjects.ts';
import EllipsisText from '/@/components/EllipsisText/src/EllipsisText.vue';

const { prefixCls } = useDesign('crash-collect');
const { prefixCls: layoutPrefixCls } = useDesign('layout-content');
const userStore = useUserStoreWithOut();
const { currentRoute } = useRouter();
const {
  getVersionsList,
  getCrashListByTime,
  crashListByTime,
  getCrashListByClass,
  crashListByClass,
  getBackgroundColor,
  getTotalColor,
  curChoose,
  isLoading,
  openLoading,
  closeLoading,
  getConfigListByPage,
  clVersionOptions,
  engineVersionOptions,
  userOptions,
  allIds,
  page,
  pageSize,
  totalNum,
  saveLastPageInfo,
  transferLastPageInfo,
  lastClVersion,
  lastEngineVersion,
  lastDmpDetail,
  lastCreatedAt,
  lastLabelValues,
  lastUser,
  lastCurChoose,
  lastClickDetail,
  lastScroll,
  getUserList,
} = useCrashCollect();
const go = useGo();

// const typeStatus = currentRoute.value.query.type as string;
const recordID = Number(currentRoute.value.query.recordID) || undefined;
const categoryID = Number(currentRoute.value.query.categoryID) || undefined;
const openValue = ref<number>();
const isInit = ref(true);
const oldName = ref('');
const inputValue = ref('');
const labelList = ref<{ value: string }[]>([]);
const headerRef = ref<HTMLDivElement>();
const footerRef = ref<HTMLDivElement>();
const bodyHeight = ref<string>('');
const createdAt = ref();
const nowDetail = ref();
const scroll = ref(0);

const { isSuperAdminOrProjectAdmin } = useAdmin();

// const { headerHeightRef } = useLayoutHeight()
const [registerModal, { openModal }] = useModal();
const clickLabelId = ref();
const [clEngineForm, { updateSchema, setFieldsValue, getFieldsValue }] = useForm({
  schemas: clEngineVersionsFormSchema,
  showActionButtonGroup: false,

});

function addOpenValue(e: Event, id: number | undefined, name: string) {
  if (!id) {
    return;
  }

  e.stopPropagation();
  // 存储旧值
  inputValue.value = name;
  oldName.value = name;
  openValue.value = id;
}

async function openSetting() {
  await getConfigListByPage();
  openModal(true, {});
}

function clickAddLabel(id) {
  clickLabelId.value = id;
}
async function addLabel() {
  await initForm();
}

function categoryName(item: { category: { name?: string; ID?: number } }) {
  if (item.category && item.category.ID) {
    return `${item.category.ID}.${item.category.name}`;
  } else {
    return '解析中...';
  }
}

async function handleChangeFilter(type: string) {
  if (type === curChoose.value) {
    return;
  }
  nowDetail.value = undefined;
  openValue.value = undefined;
  curChoose.value = type;
  allIds.value = [];
  await openLoading();

  if (!isInit.value) {
    page.value = 1;
    await initData();
    await initForm();
  }

  await closeLoading();
}

function pageClick() {
  clickLabelId.value = -1;
}

function openDetail(ID: number, isCategory = false, isReplace = false) {
  if (!ID) {
    return;
  }
  nowDetail.value = ID;

  const hypergryphLayoutContent = document.querySelector(`.${layoutPrefixCls}`);
  if (hypergryphLayoutContent) {
    scroll.value = hypergryphLayoutContent.scrollTop;
  }
  saveLastPageInfo({
    clVersion: getFieldsValue()?.clVersion === undefined ? '' : getFieldsValue().clVersion,
    engineVersion: getFieldsValue()?.engineVersion,
    dmpDetail: getFieldsValue()?.dmpDetail,
    labelValues: getFieldsValue()?.labelValues,
    user: getFieldsValue()?.user,
    createdAt: createdAt.value,
    curChoose: curChoose.value,
    clickDetail: nowDetail.value,
    scroll: scroll.value,
  });

  if (isCategory) {
    go({
      name: 'CrashClassDetail',
      params: {
        categoryID: ID,
      },
    }, isReplace);
  } else {
    go({
      name: 'CrashDetail',
      params: {
        recordID: ID,
      },
      query: {
        from: 'crashList',
      },
    }, isReplace);
  }
}

async function checkClick(id: number | undefined, name: string) {
  if (!id) {
    return;
  }

  // 删除openValue.value中的id
  const res = await updateCategoriesName(userStore.getProjectId, id, { name });

  if (res?.code !== 7) {
    const editIndex = crashListByClass.value.findIndex((item) => item.ID === id);

    crashListByClass.value[editIndex].name = inputValue.value;
    openValue.value = undefined;
    inputValue.value = '';
    oldName.value = '';
  }
}

function returnClick() {
  openValue.value = undefined;
  inputValue.value = '';
  oldName.value = '';
}

async function initData() {
  transferLastPageInfo();

  const clVersionItem = clVersionOptions.value?.find(
    (v) => v.value === (getFieldsValue().clVersion === undefined ? '' : getFieldsValue().clVersion),
  )?.value;
  const engineVersionItem = engineVersionOptions.value?.find(
    (v) => v.value === getFieldsValue().engineVersion,
  )?.value;
  let startGenerateAt: number = 0;
  let endGenerateAt: number = 0;

  if (createdAt.value) {
    startGenerateAt = new Date(`${createdAt.value[0].format('YYYY-MM-DD')} 00:00:00`).getTime();
    endGenerateAt = new Date(`${createdAt.value[1].format('YYYY-MM-DD')} 23:59:59`).getTime();
  }

  if (curChoose.value === 'TriggerTime') {
    allIds.value = {
      clVersion: clVersionItem === 0 ? undefined : clVersionItem,
      version: engineVersionItem === 0 ? undefined : engineVersionItem,
      dmpDetail: getFieldsValue().dmpDetail,
      label: getFieldsValue().label,
      users: getFieldsValue().user,
      startGenerateAt,
      endGenerateAt,
    };
    crashListByTime.value = [];
    await getCrashListByTime(getFieldsValue().labelValues, allIds.value);
  } else {
    allIds.value = {
      clVersion: clVersionItem === 0 ? undefined : clVersionItem,
      version: engineVersionItem === 0 ? undefined : engineVersionItem,
      users: getFieldsValue().user,
    };
    await getCrashListByClass(getFieldsValue().labelValues, allIds.value as number[]);
  }
}

async function getLabels() {
  const { code, list } = await getAllPaginationList((p) => getCrashLabels(userStore.getProjectId, p));

  if (code !== 7) {
    return list;
  }

  return [];
}
function getPopupContainer() {
  return document.querySelector(`.${prefixCls}__header`) as HTMLElement;
}
async function initForm() {
  await updateSchema([
    {
      field: 'clVersion',
      componentProps: {
        getPopupContainer, // 使用属性简写语法修复 ESLint 错误
        options: clVersionOptions.value,
        onChange: async () => {
          await openLoading();

          if (!isInit.value) {
            page.value = 1;
            await initData();
          }

          await closeLoading();
        },
      },
    },
    {
      field: 'engineVersion',
      componentProps: {
        getPopupContainer,
        options: engineVersionOptions.value,
        onChange: async () => {
          await openLoading();

          if (!isInit.value) {
            page.value = 1;
            await initData();
          }

          await closeLoading();
        },
      },
    },
    {
      field: 'user',
      componentProps: {
        getPopupContainer,
        options: userOptions.value.map((item) => {
          return {
            label: item,
            value: item,
          };
        }),
        onChange: async () => {
          await openLoading();

          if (!isInit.value) {
            page.value = 1;
            await initData();
          }

          await closeLoading();
        },
      },
    },
    {
      field: 'labelValues',
      componentProps: {
        getPopupContainer,
        options: await getLabels(),
        fieldNames: {
          label: 'content',
          value: 'value',
        },
        onChange: async () => {
          await openLoading();

          if (!isInit.value) {
            page.value = 1;
            await initData();
          }

          await closeLoading();
        },
      },
    },
    {
      field: 'dmpDetail',
      show: curChoose.value === 'TriggerTime',
      componentProps: {
        onChange: useDebounceFn(async () => {
          await openLoading();

          if (!isInit.value) {
            page.value = 1;
            await initData();
          }

          await closeLoading();
        }, 200),
      },
    },
    {
      field: 'time',
      show: curChoose.value === 'TriggerTime',
    },

  ]);
}

async function dataChange() {
  await openLoading();

  if (!isInit.value) {
    page.value = 1;
    await initData();
  }

  await closeLoading();
}

async function handleSuccess(init: boolean = false) {
  await openLoading();
  await getVersionsList();
  await getUserList();
  if (init) {
    await setFieldsValue({
      clVersion: clVersionOptions.value.length ? (lastClVersion.value === '' ? '' : lastClVersion.value || 0) : undefined,
      engineVersion: engineVersionOptions.value.length ? lastEngineVersion.value || 0 : undefined,
      dmpDetail: lastDmpDetail.value ? lastDmpDetail.value : '',
      labelValues: lastLabelValues.value ? lastLabelValues.value : undefined,
      user: lastUser.value ? lastUser.value : undefined,
    });
    curChoose.value = lastCurChoose.value ? lastCurChoose.value : 'TriggerTime';
    createdAt.value = lastCreatedAt.value ? lastCreatedAt.value : undefined;
    nowDetail.value = lastClickDetail.value ? lastClickDetail.value : undefined;
    scroll.value = lastScroll.value ? lastScroll.value : 0;
    const element = document.querySelector(`.${layoutPrefixCls}`);
    if (element) {
      element.scrollTo({
        top: scroll.value, // 替换为 scroll.value
        left: 0, // 水平滚动位置
        behavior: 'smooth', // 平滑滚动
      });
    }
    await initForm();
  }

  await initData();
  await closeLoading();
}

async function getLabelList() {
  const { code, list } = await getAllPaginationList((p) => getCrashLabels(userStore.getProjectId, p));

  if (code !== 7) {
    labelList.value = list;
  }
}

async function editLabelList(editLabelList: { value: string }[], type: string) {
  labelList.value = editLabelList;
  initForm();

  if (type === 'delete') {
    // 提取 labelList.value 中的所有 value 值
    const valuesInLabelList = labelList.value.map((item) => item.value);

    // 过滤 getFieldsValue().labelValues，保留那些在 labelList.value 中存在的值
    const filteredLabelValues = getFieldsValue().labelValues?.filter((value) => valuesInLabelList.includes(value));

    await setFieldsValue({
      labelValues: filteredLabelValues,
    });
  }

  // 修改label列表
  if (type !== 'add') {
    await initData();
  }
}

onBeforeMount(() => {
  if (recordID) {
    openDetail(recordID, false, true);
  } else if (categoryID) {
    openDetail(categoryID, true, true);
  }
});

onMounted(async () => {
  if (userStore.getProjectId) {
    await handleSuccess(true);
    isInit.value = false;
    getLabelList();
  }
});

// 分页处理
async function handlePageChange(p: number, size: number) {
  page.value = p;
  pageSize.value = size;
  nowDetail.value = undefined;

  saveLastPageInfo({
    clVersion: getFieldsValue().clVersion === undefined ? '' : getFieldsValue().clVersion,
    engineVersion: getFieldsValue().engineVersion,
    dmpDetail: getFieldsValue().dmpDetail,
    labelValues: getFieldsValue().labelValues,
    user: getFieldsValue().user,
    createdAt: createdAt.value,
    curChoose: curChoose.value,
    clickDetail: 0,
    scroll: 0,
  });
  const element = document.querySelector(`.${layoutPrefixCls}`);
  if (element) {
    element.scrollTo({
      top: 0,
    });
  }
  await handleSuccess();
}

watch(
  () => userStore.getProjectId,
  async (v, oldValue) => {
    await getLabelList();

    if (v && v !== oldValue) {
      isInit.value = true;
      await handleSuccess(true);
      setTimeout(() => {
        isInit.value = false;
      }, 200);
    }
  },
);
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-crash-collect';

.@{prefix-cls} {
  .ant-typography {
    color: #252525;
  }

  &__pre {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans',
      sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  }

  &__overlay {
    font-size: 12px;
    max-width: calc(100vw - 330px);
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  &__detail-log {
    font-size: 14px;
    line-height: 20px;
    display: -webkit-inline-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    padding: 16px;
  }
  &__filter-form {
    .ant-row {
      gap: 10px !important;
      overflow-y: scroll;
    }
  }
  &__body {
    padding: 16px;
    overflow: auto;
    display: flex;
    flex-direction: column;
    row-gap: 16px;
  }

  & .ant-form-item {
    margin-bottom: 0 !important;
  }

  &__choose-tag {
    width: 100%;
    cursor: pointer;
    user-select: none;
    padding: 2px;
    background-color: @report-card-background;
    border-radius: 16px;
    border: 1px solid @FO-Container-Fill6;

    &-item {
      display: inline-block;
      border-radius: 16px;
      padding: 1px 10px;
      font-weight: bold;

      &[active='true'] {
        background-color: @FO-Container-Fill6;
        color: @FO-Content-Components1;
      }
    }
  }

  &__select-component {
    width: 120px;
    border-radius: 16px !important;
  }

  &__config-btn {
    background-color: @FO-Container-Fill6 !important;
    color: @FO-Content-Components1 !important;
  }

  &_rhombus-time {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;

    &::before {
      content: ' ';
      position: absolute;
      top: 0;
      left: 0;
      width: 0;
      height: 0;
      border-top: 16px solid @FO-Container-Fill1;
      border-bottom: 16px solid @FO-Container-Fill1;
      border-right: 16px solid transparent;
      z-index: 2;
    }
  }

  &_input-class {
    input {
      height: 100% !important;
    }
  }

  &_rhombus-class {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;

    &::before {
      content: ' ';
      position: absolute;
      top: 0;
      left: 0;
      width: 0;
      height: 0;
      border-top: 18px solid @FO-Container-Fill1;
      border-bottom: 18px solid @FO-Container-Fill1;
      border-right: 18px solid transparent;
      z-index: 2;
    }
  }

  &_filter-class-item {
    padding: 10px;
    background-color: @report-card-background;
  }

  &_filter-class-more {
    padding: 10px;
    background-color: @report-card-background;
  }

  &__no-data {
    @apply mt-4 p-4 min-w-1200px rounded-md font-bold text-center;
  }

  &__has-bug {
    @apply rounded-6 py-2px px-2 color-white font-size-12px;

    background-color: #0071a1;
  }
}
</style>
