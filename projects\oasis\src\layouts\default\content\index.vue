<template>
  <div
    v-loading="getOpenPageLoading && getPageLoading" :class="[prefixCls, getLayoutContentMode, hideScrollBarClass]"
  >
    <PageLayout />
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useContentViewHeight } from './useContentViewHeight';
import { useRootSetting } from '/@/hooks/setting/useRootSetting';
import { useTransitionSetting } from '/@/hooks/setting/useTransitionSetting';
import { useDesign } from '/@/hooks/web/useDesign';
import PageLayout from '/@/layouts/page/index.vue';

const { prefixCls } = useDesign('layout-content');
const { getOpenPageLoading } = useTransitionSetting();
const { getLayoutContentMode, getPageLoading, getHideContentScrollBar } = useRootSetting();

const hideScrollBarClass = computed(() => {
  return getHideContentScrollBar.value ? 'hide-scroll-bar' : '';
});

useContentViewHeight();
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-layout-content';

.@{prefix-cls} {
  display: flex;
  position: relative;
  flex-direction: column;
  flex-grow: 1;
  width: 100%;
  height: 0;
  min-height: 0;
  overflow: auto;
  justify-content: space-between;

  &.fixed {
    width: 1200px;
    margin: 0 auto;
  }

  &.hide-scroll-bar {
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  &-loading {
    position: absolute;
    top: 200px;
    z-index: 10000;
  }
}
</style>
