import { createRouter, createWebHistory } from 'vue-router';
import type { App } from 'vue';
import { withDocumentTitle } from './modules/withDocumentTitle.ts';
import { withTrack } from './modules/withTrack.ts';
import { PlatformEnterPoint, PlatformRoutePath } from '@hg-tech/oasis-common';

export const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      name: PlatformEnterPoint.SysDevGuard,
      path: PlatformRoutePath.SysDevGuard,
      redirect: {
        name: PlatformEnterPoint.CommitCenter,
      },
    },
    {
      name: PlatformEnterPoint.CommitCenter,
      path: PlatformRoutePath.CommitCenter,
      meta: {
        title: '提交中心',
      },
      component: () => import('../views/commit-center-home/List.vue'),

    },
    {
      name: PlatformEnterPoint.CommitList,
      path: PlatformRoutePath.CommitList,
      meta: {
        title: '提交列表',
      },
      component: () => import('../views/commit-list/index.vue'),
    },
    {
      name: PlatformEnterPoint.Example,
      path: PlatformRoutePath.Example,
      meta: {
        title: '实例配置、操作、状态同步',
      },
      component: () => import('../views/example/ExampleList.vue'),
    },
    {
      name: PlatformEnterPoint.ExampleConfig,
      path: PlatformRoutePath.ExampleConfig,
      meta: {
        title: '检查实例配置',
      },
      component: () => import('../views/example/config/ExampleConfig.vue'),
    },
  ],
  strict: true,
  scrollBehavior: () => ({ left: 0, top: 0 }),
});

export function setupRouter(app: App<Element>) {
  app.use(router);

  // with plugins
  withDocumentTitle(router);
  withTrack(router);
}
