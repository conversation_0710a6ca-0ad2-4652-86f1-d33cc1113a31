import type { CommonBaseRes } from '@hg-tech/oasis-common';

export enum DevguardErrorCode {
  Success = 0, // 成功
  NoAuth = 1, // 未认证
  BadRequest = 3, // 错误请求
  Forbidden = 7, // 权限不足
}
export type PermissionBaseRes<T = never> = CommonBaseRes<T, DevguardErrorCode>;

export interface PagedQueryParam {
  page: number;
  pageSize: number;
}

export interface PagedRes<T> {
  list?: T[];
  page?: number;
  pageSize?: number;
  total?: number;
}

export type PermissionPagedRes<T> = CommonBaseRes<PagedRes<T>, DevguardErrorCode>;
