<template>
  <div class="gitlab">
    <div v-if="depotList?.length" class="gitlab__body">
      <transition-group name="flip-list" tag="p">
        <div v-for="(depot, index) in depotList" :key="depot.ID" class="gitlab__card">
          <Branch :depotID="depot.ID!" :gitlabProjectID="depot.gitlabProjectID">
            <template #depotTitle>
              <EllipsisText class="gitlab__title">
                {{ depot.description }}
              </EllipsisText>
              <div class="ml">
                <BasicButton
                  type="text" size="small" :disabled="!index " class="border-0 bg-FO-Container-Fill0!"
                  @click.stop="depotsSort(depot.ID)"
                >
                  <Icon icon="icon-park-outline:arrow-circle-up" />
                </BasicButton>
                <BasicButton
                  type="text" size="small" :disabled="index === depotList.length - 1 "
                  class="border-0 bg-FO-Container-Fill0!" @click.stop="depotsSort(depot.ID, true)"
                >
                  <Icon icon="icon-park-outline:arrow-circle-down" />
                </BasicButton>
              </div>
              <div v-if="userStore.isSuperAdmin">
                <BasicButton type="text" size="small" @click.stop="handleEdit(depot)">
                  <Icon icon="icon-park-outline:edit" />
                </BasicButton>
                <Popconfirm
                  :title="`确认删除仓库『${depot.description}』吗？`"
                  @confirm.stop="handleDelete(depot)"
                >
                  <BasicButton type="text" danger size="small" @click.stop>
                    <Icon icon="icon-park-outline:delete" />
                  </BasicButton>
                </Popconfirm>
              </div>
            </template>
          </Branch>
        </div>
      </transition-group>
    </div>
    <div v-else class="m-4 rounded-lg bg-FO-Container-Fill1 p-4">
      <Empty :image="Empty.PRESENTED_IMAGE_SIMPLE" description="该项目未配置仓库">
        <BasicButton type="primary" @click="handleCreate">
          新增仓库
        </BasicButton>
      </Empty>
    </div>
    <DepotsDrawer @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" setup name="gitlab">
import { Empty, Popconfirm } from 'ant-design-vue';
import { ref, watch } from 'vue';
import DepotsDrawer from './DepotsDrawer.vue';
import Branch from './branches/index.vue';
import { deleteRepositories, getRepositoriesList, updateDepotsSort } from '../../../api/page/gitlab';
import { useDrawer } from '../../../components/Drawer';
import { Icon } from '../../../components/Icon';
import { useUserStoreWithOut } from '../../../store/modules/user';
import { usePerforceServer } from '../../../views/versionControl/perforceManagement/hook';
import type { DepotsListItem } from '../../../api/page/model/gitlabModel';
import { getAllPaginationList } from '../../../hooks/web/usePagination';
import { EllipsisText } from '../../../components/EllipsisText';
import { BasicButton } from '../../../components/Button';

const [registerDrawer, { openDrawer }] = useDrawer();
const userStore = useUserStoreWithOut();
const depotList = ref<DepotsListItem[]>([]);
const { getPerforceServerList } = usePerforceServer();

function handleCreate() {
  openDrawer(true, { isUpdate: false });
}
async function getDepotsList() {
  if (!userStore.getProjectId) {
    return;
  }
  const { list } = await getAllPaginationList((p) => getRepositoriesList(userStore.getProjectId, p));

  if (list?.length > 0) {
    depotList.value = list;
  } else {
    depotList.value = [];
  }
}
function handleSuccess() {
  getDepotsList();
}
async function init() {
  return Promise.all([
    getPerforceServerList(userStore.projectId),
    getDepotsList(),
  ]);
}
watch(
  () => userStore.getProjectId,
  (v, oldValue) => {
    if (v && v !== oldValue) {
      init();
    }
  },
);
function handleEdit(record: DepotsListItem) {
  openDrawer(true, { record, isUpdate: true });
}
async function depotsSort(id?: number, isDown = false) {
  if (!id) {
    return;
  }
  // 排序，获取id
  const depotIdList = depotList.value.map((item) => item.ID!);
  const index = depotIdList.indexOf(id);
  if (isDown) {
    // 下移
    // 剔除最后一个
    if (index === depotIdList.length - 1) {
      return;
    }
    const temp = depotIdList[index + 1];
    depotIdList[index + 1] = id;
    depotIdList[index] = temp;
  } else {
    // 剔除第一个
    if (index === 0) {
      return;
    }
    const temp = depotIdList[index - 1];
    depotIdList[index - 1] = id;
    depotIdList[index] = temp;
  }
  await updateDepotsSort(userStore.getProjectId, { idList: depotIdList });
  init();
}

async function handleDelete(record: DepotsListItem) {
  await deleteRepositories(userStore.getProjectId, record.ID);
  init();
}
init();
</script>

<style lang="less" scoped>
.gitlab {
  &__header {
    padding: 16px;
    background-color: @FO-Container-Fill1;
  }

  &__body {
    padding: 16px;
  }

  &__card {
    margin-bottom: 16px;
    padding: 16px;
    border-radius: 8px;
    background-color: @FO-Container-Fill1;
  }

  &__manage-btn {
    margin-left: 12px;
    border: none;
    border-radius: 6px;
    background: #565656 !important;
    color: #fff !important;
  }

  &__title {
    max-width: 300px !important;
    font-size: 16px;
    font-weight: bold;
  }
}
.flip-list-move {
  transition: transform 0.5s;
}
</style>
