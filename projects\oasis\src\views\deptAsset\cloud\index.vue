<template>
  <div class="h-full w-full flex flex-col">
    <Layout class="flex-none">
      <LayoutHeader>
        <template #title="{ currentTitle }">
          <div class="FO-Font-B18">
            {{ currentTitle }}
          </div>
          <div
            class="FO-Font-R12 ml-2 rd-r-full rd-t-full rd-bl-0 rd-br-full bg-FO-Brand-Secondary-Default px-2 py-1 c-FO-Brand-Primary-Default"
          >
            Beta
          </div>
        </template>
      </LayoutHeader>
    </Layout>
    <div class="min-h-0 flex-1">
      <div class="bg-FO-Container-Fill1">
        <div class="flex justify-between b-b-1 b-b-FO-Container-Stroke1 px-8 py-4">
          <LineTab
            v-model:activeTab="curActiveTab" :tabList="cloudDeviceTabList" tabMargin="36px"
            :tabAttrs="{ class: '[&[active=true]::after]:(!-bottom-16px !rd-0) pl-1' }"
            @change="() => handleTabChange()"
          >
            <template #item="{ item }">
              <span class="flex items-center gap-1 text-14px">
                <span :class="{ 'c-FO-Brand-Primary-Default font-bold': item.name === curActiveTab }">
                  {{ item.title }}
                </span>
                <span class="c-FO-Content-Text2">({{ counts?.[item.name as keyof DeviceCounts] || 0 }})</span>
              </span>
            </template>
          </Linetab>
          <div class="flex items-center gap-2">
            <BasicButton class="!px-6px" @click="() => handleOpenHistoryDrawer()">
              <div class="flex items-center gap-1">
                <Icon :icon="HistoryOutlinedIcon" class="c-FO-Brand-Primary-Default" />
                使用历史
              </div>
            </BasicButton>
            <a-button class="!px-6px" @click="() => handleSocClick(0)">
              <div class="flex items-center gap-1">
                <Icon icon="mtl-equipment-ladder|svg" :size="24" />
                设备天梯
              </div>
            </a-button>
          </div>
        </div>
        <div v-if="curActiveTab !== 'logs'" class="px-8 py-4">
          <SearchForm
            ref="searchFormRef" v-model:selectedTags="state.selectedTags" :tags="state.tags"
            :deptList="state.deptList" :chipsetList="chipsetList" :deviceBrandList="deviceBrandList" isCloud
            @search="handleSearchInfoChange" @tagClose="handleTagClose" @clearTags="handleClearTags"
            @keywordChange="handleKeywordChange" @filterChange="handleFilterChange"
          />
        </div>
      </div>
      <template v-if="curActiveTab !== 'logs'">
        <div class="flex items-center justify-between px-8 py-4">
          <div class="flex items-center gap-2">
            <div class="c-FO-Content-Text2">
              共 {{ totalNum }} 台设备
            </div>
          </div>
          <div class="flex items-center justify-end gap-2">
            <div class="flex items-center">
              <AInputGroup compact>
                <a-select
                  v-model:value="sortTypeName" :options="sortCloudTypeOptions" class="w-100px"
                  @change="() => handleSortChange()"
                />
                <a-button class="px-2 [&:not(:hover)]:b-l-transparent" @click="handleSortDirection">
                  <ArrowDown v-if="sortTypeDirection === 'desc'" v-tippy="'降序'" />
                  <ArrowUp v-else v-tippy="'升序'" />
                </a-button>
              </AInputGroup>
            </div>
          </div>
        </div>
        <div class="grid grid-cols-1 gap-4 px-8 2xl:grid-cols-4 lg:grid-cols-2 xl:grid-cols-3">
          <ACard
            v-for="item in deviceList" :key="item.ID"
            class="cursor-pointer rd-4px bg-FO-Container-Fill1 [&>.ant-card-body]:(h-full p-4)" hoverable
            @click="() => handleCardClick(item)"
          >
            <div class="h-full flex flex-col justify-between gap-2">
              <div class="w-full flex items-center justify-between gap-1">
                <EllipsisText
                  class="min-w-0 flex-1 font-bold" :class="{
                    'c-FO-Functional-Error1-Default': item.mobileType === 2,
                  }"
                >
                  {{ item.deviceName + (item.mobileType === 2 ? '(开发机)' : '') }}
                </EllipsisText>
                <div
                  v-if="item.deviceOccupy"
                  v-tippy="getShowUserNickName(item) ? `点击联系：${getShowUserNickName(item)}` : undefined"
                  class="w-fit flex cursor-pointer items-center gap-1 rd-full bg-FO-Container-Fill2 px-3 py-1"
                  @click.stop="() => handleUserClick(getUserByUserName(item?.user)?.ID)"
                >
                  <img :src="getShowUserAvatar(item)" class="h-24px w-24px rounded-full">
                  <span class="whitespace-nowrap c-FO-Brand-Primary-Default">正在使用</span>
                </div>
              </div>
              <div class="flex items-center">
                <div class="mr-3 h-160px w-120px flex items-center justify-center">
                  <AImage
                    class="max-h-160px max-w-120px object-contain" :src="holderUrl(item?.picURL)"
                    :preview="false"
                  />
                </div>
                <div class="min-w-0 flex-1">
                  <div class="flex flex-col gap-1">
                    <div v-if="item.chipset?.socPK">
                      跑分: <span
                        v-tippy="'点击前往查看天梯图'" class="w-fit cursor-pointer c-FO-Brand-Primary-Default"
                        @click.stop="() => handleSocClick(item.chipsetID!)"
                      >{{ item.chipset?.socPK }}</span>
                    </div>
                    <div v-for="text in cardContentList" :key="text.value" class="flex items-center">
                      <span class="whitespace-nowrap">
                        {{ text.label }}
                      </span>
                      <EllipsisText class="c-FO-Content-Text2">
                        {{ getCardContent(text, item) }}
                      </EllipsisText>
                    </div>
                  </div>
                </div>
              </div>
              <div class="w-full flex items-center justify-between gap-4">
                <div class="flex items-center gap-1">
                  <span v-if="item.deviceOccupy" class="c-FO-Content-Text2">
                    预计 {{ item.remainTime }} 分后释放
                  </span>
                  <Icon
                    v-if="item.deviceOccupy?.username && item.deviceOccupy?.username !== userStore.getUserInfo.userName"
                    v-tippy="item.isCloudSubscribed ? '取消订阅“空闲提醒”' : '订阅“空闲提醒”'"
                    icon="material-symbols:bookmark-star-rounded" class="cursor-pointer"
                    :class="item.isCloudSubscribed ? 'c-FO-Brand-Primary-Default hover:c-FO-Brand-Primary-Hover' : 'c-FO-Content-Text2 hover:c-FO-Content-Text3'"
                    @click.stop="() => handleSubscribe(item)"
                  />
                </div>
                <div class="flex items-center gap-2">
                  <a-button
                    v-if="!item.deviceOccupy" type="primary" class="w-90px"
                    @click.stop="() => handleUseDevice(item)"
                  >
                    立即使用
                  </a-button>
                  <a-button
                    v-else-if="item.deviceOccupy?.username && item.deviceOccupy?.username === userStore.getUserInfo.userName"
                    type="success" class="w-90px" @click.stop="() => handleContinueUse(item)"
                  >
                    继续使用
                  </a-button>
                </div>
              </div>
            </div>
          </ACard>
        </div>
        <template v-if="!isInit">
          <div v-if="totalNum === 0" class="h-500px flex items-center justify-center">
            <AEmpty>
              <template #description>
                <div class="text-14px text-gray-400">
                  {{ state.urlQuery?.assetID ? '无法查看该设备' : '暂无待处理设备' }}
                </div>
                <BasicButton type="primary" class="mt-4" @click="() => handleAllDevice()">
                  查看全部设备
                </BasicButton>
              </template>
            </AEmpty>
          </div>
          <div v-else class="w-full flex justify-end px-8 py-4">
            <APagination
              v-model:current="page" v-model:pageSize="pageSize" size="small" :total="totalNum"
              showSizeChanger :pageSizeOptions="['12', '24', '36', '48']" @change="handlePageChange"
            />
          </div>
        </template>
        <DetailModal @register="registerDetailModal" @success="handleSuccess" />
        <UseDurationModal @register="registerDurationModal" @success="handleSuccess" />
        <EquipmentRankingsModal @register="registerEquipmentRankingsModal" @success="handleSuccess" />
      </template>
      <HistoryDrawerHolder />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { cardContentList } from '../apply/device.data';
import { cloudDeviceTabList } from './device.data';
import { UseHistoryTypeEnum } from '../uesHistory.data';
import { sortCloudTypeOptions } from '../cloud/device.data';
import type { DeptAssetStateType } from './types';
import type { DeviceCounts, DeviceListItem, DeviceTagItem } from '/@/api/page/model/deptAssetModel';
import { DeviceCategoryTypeEnum, DeviceSortTypeEnum } from '/@/api/page/model/deptAssetModel';
import {
  Card as ACard,
  Empty as AEmpty,
  Image as AImage,
  InputGroup as AInputGroup,
  Pagination as APagination,
  Layout,
  message,
  Modal,
} from 'ant-design-vue';
import { BasicButton } from '/@/components/Button';
import { onMounted, reactive, ref, toRefs, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import DetailModal from '../apply/detail/DetailModal.vue';
import UseDurationModal from './UseDurationModal.vue';
import {
  getChipsetsListByPage,
  getDeviceBrandsListByPage,
  getDeviceTagList,
  subscribeDevice,
  unsubscribeDevice,
} from '/@/api/page/deptAsset';
import { getDeptList } from '/@/api/page/system';
import { Icon } from '/@/components/Icon';
import { useModal } from '/@/components/Modal';
import { getAllPaginationList } from '/@/hooks/web/usePagination';
import { useDeptAssetApply } from '../apply/hook';
import LineTab from '/@/components/LineTab';
import { ArrowDown, ArrowUp } from '@icon-park/vue-next';
import SearchForm from '../apply/components/SearchForm.vue';
import { ForgeonTitleMap, PlatformEnterPoint, preprocessFilePath } from '@hg-tech/oasis-common';
import { EllipsisText } from '/@/components/EllipsisText';
import { useUserStore } from '/@/store/modules/user';
import { getMtlDeviceDetail, getMtlDevices } from '/@/api/page/mtl/device';
import LogoImg from '/resource/img/logo.png';
import EquipmentRankingsModal from '../apply/EquipmentRankingsModal.vue';
import { useRequest } from 'vue-request';
import { getGamePackageByID, getGamePackagesVersionByID } from '/@/api/page/test';
import type { GamePackagesListItem, GamePackagesVersionsListItem } from '/@/api/page/model/testModel';
import type { MtlDeviceListCombinedItem } from '/@/api/page/mtl/model/deviceModel';
import { useRouteQuery } from '@vueuse/router';
import { isNullOrUnDef } from '/@/utils/is';
import { useDeviceHolderImage } from '../getDeviceImg';
import { sendEvent } from '../../../service/tracker';
import HistoryOutlinedIcon from '@iconify-icons/ant-design/history-outlined';
import { useModalShow } from '@hg-tech/utils-vue';
import HistoryDrawer from '../components/HistoryDrawer.vue';
import LayoutHeader from '/@/layouts/default/header/index.vue';

const route = useRoute();
const { push, replace } = useRouter();
const userStore = useUserStore();
const device_product_type = ForgeonTitleMap[PlatformEnterPoint.CloudDevice];
const { handleUserClick, getCardContent, getUserByUserName, getDevicePlatform } = useDeptAssetApply();
const curActiveTab = ref<string>(route.query.tab as string || 'allDevice');
const urlPkgID = useRouteQuery('pkgId', undefined, { transform: (v) => (!isNullOrUnDef(v) ? Number(v) : undefined) });
const urlVersionID = useRouteQuery('pkgVersionId', undefined, { transform: (v) => (!isNullOrUnDef(v) ? Number(v) : undefined) });
const urlDeviceID = useRouteQuery('deviceId', undefined, { transform: (v) => (!isNullOrUnDef(v) ? Number(v) : undefined) });
const pkgVersionInfo = ref<GamePackagesVersionsListItem>();
const pkgInfo = ref<GamePackagesListItem>();
const { holderUrl } = useDeviceHolderImage();
const state = reactive<DeptAssetStateType>({
  isInit: true,
  tags: {},
  counts: {} as DeviceCounts,
  sortTypeName: 'inTime',
  sortTypeDirection: 'desc',
  selectedTags: {},
  deptList: [],
  chipsetList: [],
  deviceBrandList: [],
  urlQuery: {
    assetID: Number(route.query.editId) || undefined,
    chipsetID: Number(route.query.chipsetID) || undefined,
  },
  page: 1,
  pageSize: 12,
  totalNum: 0,
  todoTotal: 0,
  deviceList: [],
});

const {
  isInit,
  page,
  pageSize,
  counts,
  sortTypeName,
  sortTypeDirection,
  chipsetList,
  deviceBrandList,
  totalNum,
  deviceList,
} = toRefs(state);

const searchFormRef = ref();
const [registerDetailModal, { openModal: openDetailModal }] = useModal();
const [registerDurationModal, { openModal: openDurationModal }] = useModal();
const [registerEquipmentRankingsModal, { openModal: openEquipmentRankingsModal }] = useModal();
const [HistoryDrawerHolder, showHistoryDrawer] = useModalShow(HistoryDrawer);
/** 获取显示用户 */
function getShowUserNickName(item: DeviceListItem) {
  return getUserByUserName(item?.user)?.displayName;
}
/** 获取显示用户头像 */
function getShowUserAvatar(item: DeviceListItem) {
  return preprocessFilePath(getUserByUserName(item?.user)?.headerImg || LogoImg);
}

/** 获取部门列表 */
async function getDepts() {
  const { list } = await getDeptList();
  state.deptList = list || [];
}

/** 获取包信息 */
async function getPkgInfo() {
  if (!userStore.getProjectId || !urlPkgID.value) {
    return;
  }
  const { regamePkg } = await getGamePackageByID(
    userStore.getProjectId,
    urlPkgID.value,
    'none',
  );
  pkgInfo.value = regamePkg;
}

function handleAllDevice() {
  if (route.query.editId) {
    replace({ query: { ...route.query, editId: undefined } });
  } else {
    state.urlQuery = {};
    handleReload();
  }
}
/** 获取包版本信息 */
async function getPkgVersionInfo() {
  if (!userStore.getProjectId || !urlPkgID.value || !urlVersionID.value) {
    return;
  }
  const { repkgVersion } = await getGamePackagesVersionByID(
    userStore.getProjectId,
    urlPkgID.value,
    urlVersionID.value,
    'none',
  );
  pkgVersionInfo.value = repkgVersion;
}

async function getChipsetList() {
  const { list } = await getAllPaginationList((p) => getChipsetsListByPage({ ...p, isCloud: true }));
  state.chipsetList = list || [];
}

async function getDeviceBrandList() {
  const { list } = await getAllPaginationList(getDeviceBrandsListByPage);
  state.deviceBrandList = list || [];
}

/** 获取筛选标签列表 */
async function getTags() {
  const { tags, counts } = await getDeviceTagList({ isCloud: true });
  state.tags = {
    ...tags,
  };
  state.counts = counts;
}

function getSortType() {
  if (state.sortTypeName === 'inTime') {
    return state.sortTypeDirection === 'desc' ? DeviceSortTypeEnum.IN_TIME_DESC : DeviceSortTypeEnum.IN_TIME_ASC;
  }

  return state.sortTypeDirection === 'desc' ? DeviceSortTypeEnum.SCORE_DESC : DeviceSortTypeEnum.SCORE_ASC;
}

const { runAsync: getDevices } = useRequest(
  () => getMtlDevices({
    page: state.page,
    pageSize: state.pageSize,
    ...state.selectedTags,
    assetID: state.urlQuery.assetID,
    keyword: searchFormRef.value?.getFieldsValue().keyword,
    online: true,
    myOccupy: curActiveTab.value === 'myOccupy',
    sortType: getSortType(),
  }, {}),
  {
    refreshOnWindowFocus: true, // 窗口聚焦时自动刷新
    refocusTimespan: 5 * 1000, // 5秒内不重复触发
    onSuccess: (result) => {
      const { code, message: msg, data: res } = result.data;
      state.totalNum = res?.totalElements || 0;
      state.deviceList = res?.content || [];
      state.isInit = false;
      if (code !== 2000) {
        message.error(msg);
      }
    },
    manual: true, // 手动触发
  },
);

function handleSortChange() {
  sendEvent('device_sort_switch', {
    device_product_type,
    sort_option: sortCloudTypeOptions.find((item) => item.value === state.sortTypeName)?.label || state.sortTypeName,
  });
  handleReload();
}

function handleSortDirection() {
  state.sortTypeDirection = state.sortTypeDirection === 'desc' ? 'asc' : 'desc';
  handleSortChange();
}

async function handleReload() {
  state.page = 1;
  await getDevices();
}

// 分页处理
function handlePageChange(p: number, size: number) {
  state.page = p;
  state.pageSize = size;
  handleSuccess();
}

function handleDetail(record: DeviceListItem) {
  openDetailModal(true, {
    cloudDevice: record,
    deviceID: record.ID,
    brandList: state.deviceBrandList,
    deptList: state.deptList,
  });
}

/** 处理卡片点击 */
function handleCardClick(record: DeviceListItem) {
  sendEvent('device_card_click', {
    device_name: record.deviceName,
    device_code: record.assetNo,
    device_product_type,
    device_platform: getDevicePlatform(record),
  });
  handleDetail(record);
}

async function handleSuccess() {
  await getTags();
  await getDevices();
}

async function init() {
  // 初始加载数据
  await getTags();
  getDepts();
  getChipsetList();
  getDeviceBrandList();
}

onMounted(async () => {
  state.isInit = true;
  await init();

  if (state.urlQuery?.chipsetID) {
    searchFormRef.value?.setFieldsValue({
      chipsetIdList: [state.urlQuery.chipsetID],
    });
  } else {
    await getDevices();
    await handleAfterFetch(state.deviceList);
  }
});

// 监听相关依赖变化，刷新设备列表
watch([curActiveTab, () => state.urlQuery.assetID], () => {
  handleReload();
});

/** 判断url内设备id打开编辑抽屉 */
async function handleAfterFetch(list: MtlDeviceListCombinedItem[]) {
  if (state.urlQuery?.assetID || urlDeviceID.value) {
    const findItem = list.find((e) => e.ID === state.urlQuery?.assetID || e.deviceId === urlDeviceID.value);
    if (findItem) {
      handleCardClick(findItem);
    }
  } else if (urlPkgID.value && urlVersionID.value) {
    await getPkgInfo();
    await getPkgVersionInfo();
    if (pkgInfo.value && pkgVersionInfo.value) {
      const filterItems = list.filter((e) => {
        return !e.deviceOccupy && e.assetType === pkgVersionInfo.value?.platform;
      });
      if (filterItems.length > 0) {
        openDurationModal(true, {
          pkgInfo: pkgInfo.value,
          pkgVersionInfo: pkgVersionInfo.value,
          deviceList: filterItems,
        });
      } else {
        Modal.warning({
          title: '当前暂无可用设备',
          okText: '知道了',
        });
      }
    } else {
      Modal.warning({
        title: '获取包信息失败，请重新选择',
        okText: '知道了',
      });
    }
  }
}

function handleOpenHistoryDrawer() {
  sendEvent('device_borrow_history_click', {
    device_product_type,
  });
  showHistoryDrawer({
    type: UseHistoryTypeEnum.cloud,
  });
}
function handleSocClick(chipsetID: number) {
  sendEvent('device_ranking_panel_click', {
    device_product_type,
  });
  openEquipmentRankingsModal(true, {
    socId: chipsetID,
    isCloud: true,
  });
}

/** 处理订阅 */
async function handleSubscribe(item: DeviceListItem) {
  if (!item.ID || item.isSubscribing) {
    return;
  }
  item.isSubscribing = true;
  if (item.isCloudSubscribed) {
    const res = await unsubscribeDevice(item.ID, DeviceCategoryTypeEnum.Cloud);
    if (res?.code !== 7) {
      message.success('已取消订阅');
    }
  } else {
    const res = await subscribeDevice(item.ID, DeviceCategoryTypeEnum.Cloud);
    if (res.code !== 7) {
      message.success('订阅成功');
    }
  }
  sendEvent('device_idle_alert_click', {
    device_product_type,
    device_name: item.deviceName,
    device_code: item.assetNo,
    device_platform: getDevicePlatform(item),
  });
  await handleSuccess();
}

/** 处理筛选 */
async function handleSearchInfoChange() {
  // 获取表单值并解构出关键词
  const { keyword, ...filterValues } = searchFormRef.value?.getFieldsValue();
  // 处理筛选标签
  state.selectedTags = Object.entries(filterValues).reduce((tags, [key, value]) => {
    // 如果值存在且不为空数组则添加到标签中
    const hasValue = Array.isArray(value) ? value.length > 0 : Boolean(value);
    if (hasValue) {
      tags[key] = Array.isArray(value) ? value : [value];
    }
    return tags;
  }, {} as Record<string, any[]>);

  // 重置 URL 查询参数
  state.urlQuery = {};

  // 重新获取设备列表
  await handleReload();
}

function handleKeywordChange(value: string) {
  if (!value) {
    return;
  }
  sendEvent('device_search', {
    device_product_type,
    search_keyword: value ?? '',
  });
}

function handleFilterChange(key: string, value: any[]) {
  if (!value?.length) {
    return;
  }
  sendEvent('device_filter', {
    device_product_type,
    filter_type: key,
    filter_option: value ?? [],
  });
}

/** 处理标签关闭 */
async function handleTagClose(key: keyof DeviceTagItem, value: any) {
  const newTags = { ...state.selectedTags } as Record<string, any[]>;
  const values = newTags[key];
  if (Array.isArray(values)) {
    newTags[key] = values.filter((v) => v !== value);
    if (newTags[key]?.length === 0) {
      delete newTags[key];
    }
  } else {
    delete newTags[key];
  }
  state.selectedTags = newTags;
  searchFormRef.value?.setFieldsValue({ [key]: newTags[key] });
  await handleReload();
}

/** 处理清除所有标签 */
async function handleClearTags() {
  if (route.query?.chipsetID) {
    replace({ query: { ...route.query, chipsetID: undefined } });
    return;
  }
  state.selectedTags = {};
  await searchFormRef.value?.resetFields();
}

async function handleTabChange() {
  replace({ query: { ...route.query, tab: curActiveTab.value, chipsetID: undefined, editId: undefined } });
}
/** 处理立即使用设备 */
function handleUseDevice(item: DeviceListItem) {
  openDurationModal(true, {
    device: item,
  });
}

/** 处理继续使用设备 */
async function handleContinueUse(item: DeviceListItem) {
  if (!item.deviceId) {
    return;
  }
  const { data: { data: mtlDeviceInfo } } = await getMtlDeviceDetail({ id: item.deviceId }, {});
  // 如果设备未被申请，则切换为立即使用流程
  if (!mtlDeviceInfo?.deviceOccupy) {
    handleUseDevice(item);
    return;
  } else if (mtlDeviceInfo?.deviceOccupy?.username && mtlDeviceInfo.deviceOccupy.username !== userStore.getUserInfo?.userName) {
    // 如果设备正在使用，并且当前用户不是设备使用人，则提示无法使用
    message.error('未申请当前设备，无法使用');
    await getDevices();
    return;
  }
  push({
    name: PlatformEnterPoint.CloudDeviceDetail,
    params: { id: item.ID },
    query: {
      deviceId: item.deviceId,
    },
  });
}
</script>
