import dayjs from 'dayjs';
import type { DeviceListItem } from '/@/api/page/model/deptAssetModel';
import { DeviceAccessLevelEnum, DeviceFsmStateEnum, DeviceTypeEnum } from '/@/api/page/model/deptAssetModel';
import { useUserStore } from '/@/store/modules/user';
import { HYPERGRYPH_LARK_URL } from '/@/settings/siteSetting';
import { preprocessFilePath } from '@hg-tech/oasis-common';
import { useMessage } from '/@/hooks/web/useMessage';
import { findNode } from '/@/utils/helper/treeHelper';
import type { DeptListItem } from '/@/api/page/model/systemModel';
import { get } from 'lodash-es';
import type { cardContentListItem } from './device.data';
import LogoImg from '/resource/img/logo.png';
import { useSysUserList } from '/@/hooks/useUserList';
import { match, P } from 'ts-pattern';

export function useDeptAssetApply() {
  const userStore = useUserStore();
  const { userList } = useSysUserList({ containResigned: true });

  function getUserById(ID?: number) {
    return userList.value?.find((e) => e.ID === ID);
  }

  function getUserByUserName(userName?: string) {
    return userList.value?.find((e) => e.userName === userName);
  }

  /** 是否是已回收设备 */
  function isRecycled(item?: DeviceListItem) {
    return item?.deptAdminDevice?.isRecycled;
  }

  /** 获取最大借用天数限制 */
  function getMaxDayLimit(item?: DeviceListItem) {
    // 已回收设备 且 不是当前设备管理员 且 有最大借用天数限制
    if (isRecycled(item) && !isCurDeviceAdmin(item) && item?.deptAdminDevice?.deptAdmin?.maxDay && item?.deptAdminDevice?.deptAdmin?.maxDay !== -1) {
      return item?.deptAdminDevice?.deptAdmin?.maxDay;
    }
    return undefined;
  }

  /** 当前设备管理员 (已回收设备 是 部门管理员, 未回收设备 是 设备所有者) */
  function curDeviceAdmin(item?: DeviceListItem) {
    return isRecycled(item) ? item?.deptAdminDevice?.user : item?.owner;
  }

  /** 是否是当前设备管理员 (已回收设备 是 部门管理员, 未回收设备 是 设备所有者) */
  function isCurDeviceAdmin(item?: DeviceListItem) {
    return userStore.getUserInfo?.ID === curDeviceAdmin(item)?.ID;
  }

  /** 是否有所有权限(管理员，当前设备管理员) */
  function hasAllPermission(item: DeviceListItem) {
    return userStore.isSuperAdmin || isCurDeviceAdmin(item);
  }

  /** 是否有借用审批、确认借出、归还审批审批权限(管理员，当前设备管理员，候补审批人) */
  function hasAuditPermission(item: DeviceListItem) {
    return hasAllPermission(item) || item.canApprove;
  }

  /** 可借用 -- 在库中 */
  function canBorrow(item: DeviceListItem) {
    return item?.fsmState === DeviceFsmStateEnum.FREE;
  }

  /** 是否不可借用状态 */
  function isUnavailable(item: DeviceListItem) {
    return item.accessLevel === DeviceAccessLevelEnum.UNAVAILABLE;
  }

  /** 可确认借出 -- 领用中且有审核权限 */
  function canPickUp(item: DeviceListItem) {
    return item?.fsmState === DeviceFsmStateEnum.BORROWING && hasAuditPermission(item);
  }

  /** 可归还 -- 在使用中, 且有审核权限或是当前使用者 */
  function canReturn(item: DeviceListItem) {
    return item?.fsmState === DeviceFsmStateEnum.USING && (hasAllPermission(item) || userStore.getUserInfo?.ID === item?.currentUserID);
  }

  /** 可审核申请 -- 审核中且有申请审核权限 */
  function canAudit(item: DeviceListItem) {
    return item?.fsmState === DeviceFsmStateEnum.APPLYING && hasAuditPermission(item);
  }

  /** 可审核归还 -- 归还中且有归还审核权限 */
  function canAuditReturn(item: DeviceListItem) {
    return item?.fsmState === DeviceFsmStateEnum.RETURNING && hasAuditPermission(item);
  }

  /** 格式化归还时间 */
  function formatReturnTime(returnTime: number, showFullMsg = false) {
    if (returnTime === -1) {
      return '长期借用中';
    } else if (!returnTime) {
      return '';
    }
    const returnTimeStr = dayjs.unix(returnTime).format('YYYY-MM-DD');
    return showFullMsg ? `预计 ${returnTimeStr} 归还` : returnTimeStr;
  }

  /** 是否逾期 */
  function isOverdue(returnTime: number) {
    if (returnTime === -1 || !returnTime) {
      return false;
    }
    return returnTime < dayjs().endOf('day').unix();
  }

  /** 获取显示用户 */
  function getShowUserNickName(item: DeviceListItem) {
    return getUserById(item?.currentUserID)?.displayName;
  }
  /** 获取显示用户头像 */
  function getShowUserAvatar(item: DeviceListItem) {
    return preprocessFilePath(getUserById(item?.currentUserID)?.headerImg || LogoImg);
  }

  /** 获取显示用户状态 */
  function getShowUserStatus(item?: DeviceListItem, isLog = false) {
    if (!item) {
      return '';
    }
    switch (item?.fsmState) {
      case DeviceFsmStateEnum.RETURNING:
        return isLog ? '正在归还' : '归还中';
      case DeviceFsmStateEnum.USING:
        return isLog ? '正在使用' : '使用中';
      case DeviceFsmStateEnum.APPLYING:
        return isLog ? '正在申请' : '申请中';
      case DeviceFsmStateEnum.BORROWING:
        return isLog ? '正在领用' : '领用中';
      default:
        return '';
    }
  }

  /**
   * 获取部门名称
   * @param ID 部门ID
   * @param deptList 部门列表
   * @param isShowAll 是否显示全部
   * @returns 部门名称
   */
  function formatDept(ID: number, deptList: DeptListItem[], isShowAll = false) {
    const findNodeItem = findNode(deptList, (n) => n.ID === ID);
    const replacePath = findNodeItem?.orgPath?.replace('鹰角>', '') || '';
    return isShowAll ? replacePath : replacePath?.split('>').slice(-2).join('>') || '';
  }

  function handleUserClick(ID?: number) {
    const { createMessage } = useMessage();
    if (!ID) {
      createMessage.warning('人员ID不存在');
      return;
    }
    const user = getUserById(ID);
    if (!user) {
      createMessage.warning('人员不存在');
    } else if (user?.inactive) {
      createMessage.warning('人员已离职');
    } else if (user?.openID) {
      window.open(`${HYPERGRYPH_LARK_URL}?openId=${user.openID}`, '_blank');
    }
  }

  function getCardContent(text: cardContentListItem, item?: DeviceListItem) {
    if (!item) {
      return '';
    }
    const value = get(item, text.value) || '';
    const extraValue = text.extraValue ? (get(item, text.extraValue) || '') : '';
    return (
      (value || '')
      + (value && extraValue ? ` / ` : '')
      + (extraValue || '')
      + (text.unit ? ` ${text.unit}` : '')
    ) || '-';
  }

  /** 获取设备平台 */
  function getDevicePlatform(record?: DeviceListItem) {
    if (!record) {
      return '';
    }
    return match(record.assetType)
      .with(DeviceTypeEnum.IOS, () => 'iOS')
      .with(DeviceTypeEnum.ANDROID, () => 'Android')
      .with(P._, () => '未知')
      .exhaustive();
  }
  return {
    isRecycled,
    getUserById,
    getUserByUserName,
    isCurDeviceAdmin,
    curDeviceAdmin,
    hasAllPermission,
    hasAuditPermission,
    canBorrow,
    isUnavailable,
    canPickUp,
    canReturn,
    canAudit,
    canAuditReturn,
    getMaxDayLimit,
    formatReturnTime,
    isOverdue,
    getShowUserNickName,
    getShowUserAvatar,
    getShowUserStatus,
    handleUserClick,
    formatDept,
    getCardContent,
    getDevicePlatform,
  };
}
