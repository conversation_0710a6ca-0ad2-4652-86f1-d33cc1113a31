<template>
  <BasicModal
    :title="getTitle" :width="1100" destroyOnClose :afterClose="handleAfterClose" @register="registerModal"
    @ok="handleSubmit"
  >
    <template #title>
      <div class="relative flex items-center justify-center">
        <span>{{ getTitle }}</span>
        <div v-if="isUpdate" class="button-group absolute right-10">
          <!-- 查看详情按钮 -->
          <AButton
            v-if="device?.online" class="b-r-0 rounded-bl-8px rounded-br-0 rounded-tl-8px rounded-tr-0"
            @click="handleDeviceEdit"
          >
            查看详情
          </AButton>
          <ATooltip v-else>
            <template #title>
              设备未上线
            </template>
            <AButton :disabled="true" class="b-r-0 rounded-bl-8px rounded-br-0 rounded-tl-8px rounded-tr-0">
              查看详情
            </AButton>
          </ATooltip>

          <!-- 查看操作日志按钮 -->
          <APopover placement="bottom" overlayClassName="show-operate-button">
            <template #content>
              <AMenu class="b-rd-8px">
                <AMenuItem key="log" @click="() => handleLogDetail()">
                  查看操作日志
                </AMenuItem>
              </AMenu>
            </template>
            <AButton class="rounded-bl-0 rounded-br-8px rounded-tl-0 rounded-tr-8px">
              ...
            </AButton>
          </APopover>
        </div>
      </div>
    </template>
    <div :class="prefixCls" class="flex">
      <div class="w-160px text-center xl:w-200px">
        <BasicUpload
          :singleValue="picURL" valueFormat="string" :maxSize="20" :maxNumber="1" :multiple="false" isPicture
          :accept="['.jpg', '.jpeg', '.gif', '.png', '.webp']" @change="handlePicChange"
        />
      </div>
      <BasicForm
        class="device-from max-h-600px min-w-0 flex-1 overflow-auto" :schemas="showSchemas"
        @register="registerForm"
      >
        <template #remark="{ model }">
          <ATextarea v-model:value="model.remark" :rows="3" placeholder="请输入备注" :maxlength="200" />
        </template>
        <template #resolution="{ model }">
          <AInputGroup>
            <ARow :gutter="8">
              <ACol :span="11">
                <AFormItemRest>
                  <AInputNumber v-model:value="model.resolutionX" :precision="0" :min="0" :disabled="isUpdate" />
                </AFormItemRest>
              </ACol>
              <span class="self-center">x</span>
              <ACol :span="11">
                <AInputNumber v-model:value="model.resolutionY" :precision="0" :min="0" :disabled="isUpdate" />
              </ACol>
            </ARow>
          </AInputGroup>
        </template>
        <template #brandID="{ model, field }">
          <div class="flex gap-4px">
            <div>
              <span :class="`${basicFormPrefixCls}__border-box-required-mark`">
                *
              </span>
              <span>
                设备品牌
              </span>
            </div>
            <ATooltip>
              <template #title>
                {{ device?.brand }}
              </template>
              <div class="cursor-pointer c-FO-Content-Text2 hover:!c-FO-Brand-Primary-Default" @click="copyText(device?.brand || '')">
                读取值
              </div>
            </ATooltip>
          </div>
          <ASelect
            v-model:value="model[field]" placeholder="请选择品牌" showSearch allowClear :options="getBrandIdOptions()"
            class="mt-8px"
          />
        </template>
        <template #chipsetID="{ model, field }">
          <div class="flex gap-4px">
            <div>
              <span :class="`${basicFormPrefixCls}__border-box-required-mark`">
                *
              </span>
              <span>
                CPU
              </span>
            </div>
            <ATooltip>
              <template #title>
                {{ device?.cpuType || '无' }}
              </template>
              <div class="cursor-pointer c-FO-Content-Text2 hover:!c-FO-Brand-Primary-Default" @click="copyText(device?.cpuType || '')">
                读取值
              </div>
            </ATooltip>
          </div>
          <ACascader
            v-model:value="model[field]" class="mt-8px" :options="getChipsetOptions(props.chipsetList) || []"
            placeholder="请选择CPU" showSearch allowClear
          />
        </template>
        <template #addBrandID>
          <a-button class="ml-3 mt-30px w-52px" @click="handleAddBrand">
            +
          </a-button>
        </template>
        <template #addChipsetID>
          <a-button class="ml-3 mt-30px w-52px" @click="handleAddChipset">
            +
          </a-button>
        </template>
      </BasicForm>
    </div>
    <AddChipsetModal @register="registerAddChipsetModal" @success="handleAddChipsetSuccess" />
    <AddBrandModal @register="registerAddBrandModal" @success="handleAddBrandSuccess" />
  </BasicModal>
</template>

<script lang="ts" setup>
import {
  Cascader as ACascader,
  Col as ACol,
  Form as AForm,
  InputNumber as AInputNumber,
  Menu as AMenu,
  MenuItem as AMenuItem,
  Popover as APopover,
  Row as ARow,
  Textarea as ATextarea,
  Tooltip as ATooltip,
  message,
} from 'ant-design-vue';
import { groupBy, isEqual, map, omit } from 'lodash-es';
import { computed, ref, unref } from 'vue';
import AddChipsetModal from './AddChipsetModal.vue';
import AddBrandModal from './AddBrandModal.vue';
import { formSchema, mobileTypeOptions } from './device.data';
import { addDevice, editDevice, getDeptDeviceAccessLevel, getDeviceByID, getUdidIsExist, getUsageList } from '/@/api/page/deptAsset';
import { type AccessDeptListItem, type AccessProjectListItem, type ChipsetsListItem, type DeviceBrandListItem, type DeviceListItem, DeviceAccessLevelEnum, DeviceTypeEnum } from '/@/api/page/model/deptAssetModel';
import type { DeptListItem, ProjectListItem } from '/@/api/page/model/systemModel';
import { type Rule, BasicForm, useForm } from '/@/components/Form';
import type { ModalMethods } from '/@/components/Modal';
import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
import { BasicUpload } from '/@/components/Upload';
import { useDesign } from '/@/hooks/web/useDesign';
import { isNullOrUnDef } from '/@/utils/is';
import { useRoute, useRouter } from 'vue-router';
import { getDeptList, getSimpleProjectListByPage } from '/@/api/page/system';
import { getAllPaginationList } from '/@/hooks/web/usePagination';
import { copyText } from '/@/utils/copyTextToClipboard';
import { useSysUserList } from '/@/hooks/useUserList';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';
import { ResultEnum } from '/@/enums/httpEnum';

import { findInTrees } from '@hg-tech/utils';

const props = withDefaults(defineProps<{
  chipsetList?: ChipsetsListItem[];
  brandList?: DeviceBrandListItem[];

}>(), {
  chipsetList: () => [],
  brandList: () => [],

});
const emit = defineEmits<{
  success: [];
  register: [methods: ModalMethods, uuid: number];
  chipsetChange: [];
  brandChange: [];
  deviceDetail: [device: DeviceListItem | undefined];
}>();

const { userList } = useSysUserList();
const route = useRoute();
const { resolve, replace } = useRouter();
const AFormItemRest = AForm.ItemRest;
const { prefixCls } = useDesign('dept-device-modal');
const { prefixCls: basicFormPrefixCls } = useDesign('basic-form');
const isUpdate = ref(false);
const isCopy = ref(false);
const deptList = ref<DeptListItem[]>([]);
const editId = ref();
const device = ref<DeviceListItem>();
const picURL = ref<string>('');
const isInit = ref(false);
const allProjectList = ref<ProjectListItem[]>([]);
const allAssetUsageList = ref<{ label: string; value: number }[]>([]);
const isShowDeviceDetail = ref(false);
const oldDeviceUDID = ref<string>('');
function handlePicChange(val: string) {
  picURL.value = val;
}

/** 获取设备信息 */
async function getDevice() {
  if (!editId.value) {
    return;
  }
  const { assetDevice } = await getDeviceByID(editId.value);
  device.value = assetDevice;
}
/** 查看操作日志 */
function handleLogDetail() {
  const { fullPath } = resolve({ name: PlatformEnterPoint.DeviceManagementLogs, query: { editId: device.value?.ID } });
  window.open(fullPath, '_blank');
}
/** 获取所有项目列表 */
async function getAllProjectList() {
  const { list } = await getAllPaginationList(getSimpleProjectListByPage);
  allProjectList.value = list || [];
}
/** 获取所有资产用途 */
async function getAllAssetUsageList() {
  const { options } = await getUsageList();
  allAssetUsageList.value = options?.map((item) => ({
    label: item.label,
    value: item.id,
  })) || [];
}
function getBrandIdOptions() {
  return props.brandList?.map((item) => ({
    label: item.cnName ? `${item.cnName} (${item.name})` : item.name,
    value: item.ID,
  }));
}
const [registerForm, { resetFields, setFieldsValue, updateSchema, validate, clearValidate, getFieldsValue, scrollToField }] = useForm({
  labelWidth: 100,
  showActionButtonGroup: false,
  layout: 'vertical',
});

const showSchemas = computed(() => {
  const updatedSchemas = formSchema.map((formSchemaItem) => {
    formSchemaItem.children = (formSchemaItem.children ?? []).map((e) => {
      switch (e.field) {
        case 'accessLevel':
        {
          e.componentProps = {
            ...e.componentProps,
            defaultValue: DeviceAccessLevelEnum.PUBLIC,
            onChange: (e: { target: { value: DeviceAccessLevelEnum } }) => {
              if (e?.target?.value === DeviceAccessLevelEnum.DEPT && !isInit.value) {
                setFieldsValue({
                  deptIds: getFieldsValue()?.deptID ? [getFieldsValue()?.deptID] : [],
                });
              }
            },
          };
          break;
        }
        case 'brandID':
        {
          e.componentProps = {
            ...e.componentProps,
            options: props.brandList?.map((item) => ({
              label: item.cnName ? `${item.cnName} (${item.name})` : item.name,
              value: item.ID,
            })),
          };
          break;
        }
        case 'deptID':
        {
          e.componentProps = {
            ...e.componentProps,
            treeData: (deptList.value || []) as DeptListItem[],
            onChange: async (deptID: number) => {
              const { deptAccessLevel } = await getDeptDeviceAccessLevel(deptID);
              if (!deptAccessLevel) {
                return;
              }
              let hasUpdates = false;
              const fieldsValue = await getFieldsValue();
              if (fieldsValue?.accessLevel !== deptAccessLevel.accessLevel) {
                await setFieldsValue({
                  accessLevel: deptAccessLevel.accessLevel,
                });
                hasUpdates = true;
              }
              if (deptAccessLevel.accessLevel === DeviceAccessLevelEnum.PROJECT && !isEqual(fieldsValue?.projectIds, deptAccessLevel.projectIds)) {
                await setFieldsValue({
                  projectIds: deptAccessLevel.projectIds,
                });
                hasUpdates = true;
              }
              if (deptAccessLevel.accessLevel === DeviceAccessLevelEnum.DEPT && !isEqual(fieldsValue?.deptIds, deptAccessLevel.deptIds)) {
                await setFieldsValue({
                  deptIds: deptAccessLevel.deptIds,
                });
                hasUpdates = true;
              }
              if (hasUpdates) {
                message.success('已同步更新流通级别');
              }
            },
          };
          break;
        }
        case 'deptIds':
        {
          e.componentProps = {
            ...e.componentProps,
            treeData: (deptList.value || []) as DeptListItem[],
          };
          break;
        }
        case 'brand':
        case 'cpuType':
        {
          e.ifShow = isUpdate.value;
          break;
        }
        case 'chipsetID':
        {
          e.componentProps = {
            options: getChipsetOptions(props.chipsetList) || [],
          };
          break;
        }
        case 'mobileType':
        {
          e.componentProps = ({ formModel }) => {
            return {
              ...e.componentProps,
              options: mobileTypeOptions.map((e) => {
                e.disabled = !isNullOrUnDef(e.assetType) && e.assetType !== formModel.assetType;
                return e;
              }),
            };
          };
          break;
        }
        case 'projectIds':
        {
          e.componentProps = {
            ...e.componentProps,
            options: allProjectList.value,
          };
          break;
        }
        case 'usageID':
        {
          e.componentProps = {
            options: allAssetUsageList.value,
          };
          break;
        }

        default:
          break;
      }
      return e;
    });
    return formSchemaItem;
  });
  return updatedSchemas;
});

// 将后端接口列表格式改为cascader options格式
function getChipsetOptions(chipsetsList: ChipsetsListItem[]) {
  const grouped = groupBy(chipsetsList, 'brandID');
  return map(grouped, (socs, brandID) => ({
    label: socs[0].brand?.nameCN,
    value: Number(brandID),
    children: map(socs, (soc) => ({
      value: soc.ID,
      label: soc.socName,
    })),
  }));
}
async function validatePass2(_rule: Rule, value: string) {
  if (!value) {
    return Promise.resolve();
  }
  const res = await getUdidIsExist(value);
  if (res?.code === 7) {
    return Promise.resolve();
  } else {
    if (oldDeviceUDID.value === value && isUpdate.value) {
      return Promise.resolve();
    }
    return Promise.reject(new Error(device?.value?.assetType === DeviceTypeEnum.ANDROID ? '序列号已存在，不能重复' : 'UDID已存在，不能重复'));
  }
}
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  setModalProps({ confirmLoading: true });
  try {
    await resetFields();
    await getAllProjectList();
    await getAllAssetUsageList();
    isUpdate.value = !!data?.isUpdate;
    isCopy.value = !!data?.isCopy;
    if (data.deptList?.length) {
      deptList.value = data?.deptList || [];
    } else {
      const { list } = await getDeptList();
      deptList.value = list || [];
    }
    isInit.value = true;

    editId.value = data?.deviceID;
    await getDevice();
    oldDeviceUDID.value = device?.value?.deviceUDID || '' as string;

    updateSchema({
      field: 'deviceUDID',
      label: device?.value?.assetType === DeviceTypeEnum.ANDROID ? '序列号' : 'UDID',
      componentProps: {
        placeholder: device?.value?.assetType === DeviceTypeEnum.ANDROID ? '请输入序列号' : '请输入UDID',
      },
      rules: [
        { validator: validatePass2, trigger: 'blur' },
      ],
    });
    updateSchema({
      field: 'assetType',
      componentProps: {
        onChange: (value) => {
          updateSchema({
            field: 'deviceUDID',
            label: value === DeviceTypeEnum.ANDROID ? '序列号' : 'UDID',
            componentProps: {
              placeholder: value === DeviceTypeEnum.ANDROID ? '请输入序列号' : '请输入UDID',
            },
          });
        },
      },
    });

    if (unref(isUpdate) || unref(isCopy)) {
      picURL.value = device?.value?.picURL || '';
      await setFieldsValue({
        ...device.value,
        ownerID: userList.value?.map((item) => item.ID)?.includes(device.value?.ownerID) ? device.value?.ownerID || undefined : undefined,
        deptID: findInTrees(deptList.value, (item) => item.ID === device.value?.deptID, { keyField: 'ID', childrenField: 'children' })?.target?.ID,
        deptIds: device.value?.accessDepts?.length ? device.value?.accessDepts?.map((e: AccessDeptListItem) => e.deptID) : (device.value?.deptID ? [device.value?.deptID] : []),
        projectIds: device.value?.accessProjects?.length ? device.value?.accessProjects?.map((e: AccessProjectListItem) => e.projectID) : [],
        usageID: device.value?.usageID || undefined,
        mobileType: device.value?.mobileType || undefined,
        deviceUID: unref(isCopy) ? undefined : device.value?.deviceUID,
        resolutionX: device.value?.resolution?.split(' x ')?.[0] || 0,
        resolutionY: device.value?.resolution?.split(' x ')?.[1] || 0,
        chipsetID: device.value?.chipset
          ? [device.value?.chipset?.brand?.ID, device.value?.chipsetID]
          : undefined,
        romVersion: device.value?.romVersion,
      });
      clearValidate(['resolutionX', 'brandID', 'chipsetID']);
    } else {
      ['deviceUID', 'romVersion', 'osVersion', 'deviceType', 'deviceUDID'].forEach((e) => {
        updateSchema({
          field: e,
          componentProps: {
            disabled: false,
          },
        });
      });
      setFieldsValue({
        online: false,
      });
    }
  } finally {
    isInit.value = false;
    setModalProps({ confirmLoading: false });
  }
});

const getTitle = computed(() =>
  (!unref(isUpdate) ? (!unref(isCopy) ? '新增设备' : '复制设备') : '编辑设备'),
);
function handleDeviceEdit() {
  // 返回详情
  isShowDeviceDetail.value = true;
  if (!device.value) {
    return;
  }
  emit('deviceDetail', device.value);
  closeModal();
}
async function handleSubmit() {
  try {
    const values = await validate();
    const formatValues = Object.assign({}, values, {
      resolution: `${values.resolutionX} x ${values.resolutionY}`,
      picURL: picURL.value,
      chipsetID: values.chipsetID[1],
    });
    setModalProps({ confirmLoading: true });
    if (!unref(isUpdate) || unref(isCopy)) {
      const res = await addDevice(formatValues);

      if (res?.code !== ResultEnum.API_ERROR) {
        emit('success');
        closeModal();
      }
    } else if (unref(editId)) {
      const submitData = Object.assign(
        {},
        omit(unref(device), [
          'CreatedAt',
          'UpdatedAt',
          'ID',
          'latestApplyLog',
          'latestUsageLog',
          'dept',
          'owner',
        ]),
        formatValues,
      );
      const res = await editDevice(submitData, unref(editId));

      if (res?.code !== ResultEnum.API_ERROR) {
        emit('success');
        closeModal();
      }
    }
  } catch (error: any) {
    if (error?.errorFields?.length) {
      scrollToField(error?.errorFields[0]?.name[0], {
        behavior: 'smooth',
        block: 'center',
      });
    }
  } finally {
    setModalProps({ confirmLoading: false });
  }
}

const [registerAddChipsetModal, { openModal: openAddChipsetModal }] = useModal();
const [registerAddBrandModal, { openModal: openAddBrandModal }] = useModal();

function handleAddChipset() {
  openAddChipsetModal(true, {
    chipsetList: props.chipsetList,
  });
}

function handleAddBrand() {
  openAddBrandModal(true, {
    brandList: props.brandList,
  });
}

async function handleAddChipsetSuccess(brandID: number, chipsetID: number) {
  if (!brandID || !chipsetID) {
    return;
  }
  emit('chipsetChange');

  await setFieldsValue({
    chipsetID: [brandID, chipsetID],
  });
}

async function handleAddBrandSuccess(brandID: number) {
  if (!brandID) {
    return;
  }
  emit('brandChange');
}

/** 关闭模态框 */
async function handleAfterClose() {
  if ((route.query.editId || route.query.deviceId) && !isShowDeviceDetail.value) {
    await replace({ query: { ...route.query, editId: undefined, deviceId: undefined } });
  } else {
    isShowDeviceDetail.value = false;
  }
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-dept-device-modal';

.@{prefix-cls} {
  & .ant-upload-select-picture-card {
    width: 150px !important;
    height: 280px !important;

    & img {
      max-width: 146px !important;
      max-height: 276px !important;
    }
  }

  & .device-from {
    & .hypergryph-form-border-box {
      border: none !important;
    }
  }

  .ant-form-item:not(.ant-form-item-with-help) {
    margin-bottom: 10px !important;
  }
}

.show-operate-button.ant-popover .ant-popover-inner {
  padding: 0 !important;
}
</style>
