<template>
  <Modal
    :width="600"
    :open="show"
    :maskClosable="false"
    destroyOnClose
    centered
    :afterClose="modalDestroy"
    @cancel="() => modalCancel()"
  >
    <template #title>
      <div class="text-center">
        <span class="font-size-[16px] font-bold">
          <span>审批</span>
        </span>
      </div>
    </template>
    <div class="flex flex-col gap-10px p-20px">
      <Row>
        <Col :span="4" class="flex justify-end">
          <span class="FO-Font-B16">
            审批人：
          </span>
        </Col>
        <Col :span="16">
          <span>
            {{ checkQaList?.map(item => item.nickName).join('、') }}
          </span>
        </Col>
      </Row>
      <Row>
        <Col :span="4" class="flex justify-end">
          <span class="FO-Font-B16">本地cl：</span>
        </Col>
        <Col :span="16">
          <span>
            {{ shelveCL }}
          </span>
          <Tooltip placement="bottom" @openChange="getSubmitInfo">
            <template #title>
              <div>提交人:{{ promptText?.submitterName }}</div>
              <div>日期:{{ promptText?.date }}</div>
              <div>描述:{{ promptText?.description }}</div>
            </template>
            <IIcon class="ml-5px" />
          </Tooltip>
        </Col>
      </Row>
      <Row>
        <Col :span="4" class="flex justify-end">
          <span class="FO-Font-B16">
            提交人：
          </span>
        </Col>
        <Col :span="16">
          <span>
            {{ submitter?.nickName }}
          </span>
        </Col>
      </Row>
      <Row>
        <Col :span="4" class="flex justify-end">
          <span class="FO-Font-B16">
            提审时间：
          </span>
        </Col>
        <Col :span="16">
          <span>
            {{ checkTime }}
          </span>
        </Col>
      </Row>
      <Row>
        <Col :span="4" class="flex justify-end">
          <span class="FO-Font-B16">
            提交描述：
          </span>
        </Col>
        <Col :span="16">
          <div>
            {{ description }}
          </div>
        </Col>
      </Row>
      <Row>
        <Col :span="4" class="flex justify-end">
          <span class="FO-Font-B16">
            单号：
          </span>
        </Col>
        <Col :span="16">
          <a :href="workItemURL" target="_blank">
            {{ workItemTitle }}
          </a>
        </Col>
      </Row>
    </div>
    <template #footer>
      <div v-if="canApply" class="mt flex justify-center">
        <Button type="primary" class="bg-FO-Functional-Success1-Default!" @click="handleConfirm(1)">
          通过
        </Button>
        <Button type="primary" class="bg-FO-Functional-Error1-Default!" danger @click="handleConfirm(2)">
          拒绝
        </Button>
      </div>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import IIcon from '../../assets/icons/iIcon.svg?component';
import { Button, Col, Modal, Row, Tooltip } from 'ant-design-vue';
import { type ModalBaseProps, useLatestPromise } from '@hg-tech/utils-vue';
import { type SubmitInfo, type Submitter, getSubmitInfoApi } from '../../../src/api';
import { useForgeonConfigStore } from '../../store/modules/forgeonConfig';
import { store } from '../../store/pinia';
import { ref } from 'vue';

const props = defineProps< ModalBaseProps<{ updatedItem?: null }> & {
  checkQaList?: Submitter[];
  shelveCL?: number;
  submitter?: Submitter;
  checkTime?: string;
  description?: string;
  workItemURL?: string;
  workItemTitle?: string;
  canApply?: boolean;
  recordID?: number;
  sentReq?: (formValue: number) => Promise<undefined>;
}>();
const { execute: submitInfoExecute, data: submitInfo } = useLatestPromise(getSubmitInfoApi);
const forgeonConfig = useForgeonConfigStore(store);
const promptText = ref<SubmitInfo>();
async function getSubmitInfo(value: boolean) {
  if (value) {
    await submitInfoExecute({ id: forgeonConfig.currentProjectId!, recordID: props.recordID! }, {});
    promptText.value = submitInfo.value?.data?.data;
  }
}

async function handleConfirm(approve: number) {
  const updatedItem = await props.sentReq?.(approve);
  return props.modalConfirm({ updatedItem });
}
</script>
