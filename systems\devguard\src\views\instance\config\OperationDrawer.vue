<template>
  <Drawer
    :open="show" width="1000px" :closable="false" placement="right" @afterOpenChange="v => !v && modalDestroy()"
    @close="() => modalConfirm()"
  >
    <div v-if="gridOptions.data?.length">
      <FilterWrapper v-model:form="searchForm" class="mb-12px" />
      <BasicVxeTable :options="gridOptions" />
      <div class="flex justify-end p-4">
        <div class="w-100% flex items-center justify-between">
          <div>
            共计
            {{ pagerConfig.total }}
            条数据
          </div>
          <Pagination
            v-model:current="pagerConfig.currentPage" v-model:pageSize="pagerConfig.pageSize"
            :total="pagerConfig.total" showSizeChanger :pageSizeOptions="['10', '20', '50', '100']"
            @change="handlePageData"
          />
        </div>
      </div>
    </div>

    <div v-else class="h-full flex items-center justify-center">
      <Empty :image="emptyImg" description="暂无数据" />
    </div>

    <template #title>
      <div class="flex items-center justify-between">
        操作历史
      </div>
    </template>
    <template #footer>
      <div class="flex justify-end">
        <Button @click="close">
          关闭
        </Button>
      </div>
    </template>
  </Drawer>
</template>

<script lang="tsx" setup>
import { type ModalBaseProps, useLatestPromise } from '@hg-tech/utils-vue';
import { Button, Drawer, Empty, Pagination } from 'ant-design-vue';
import { FilterWrapper } from './filter-wrapper.tsx';
import { useForgeonConfigStore } from '../../../store/modules/forgeonConfig';
import { store } from '../../../store/pinia';
import { useRouter } from 'vue-router';
import { reactive, ref, watch } from 'vue';
import FillAdminIcon from '../../../assets/icons/fill-admin.svg?component';
import dayjs from 'dayjs';
import { type VxeGridProps, BasicVxeTable, EllipsisText } from '@hg-tech/oasis-common';
import { type OperationsListItem, getOperationsListApi } from '../../../api/index.ts';
import { operationTypeOptions, operationTypes } from './type.data.ts';

const props = defineProps<ModalBaseProps & {

}>();

const emptyImg = Empty.PRESENTED_IMAGE_SIMPLE;
const router = useRouter();
const { execute: getOperationsListExecute, data: operationsList } = useLatestPromise(getOperationsListApi);
const routeParams = router.currentRoute.value.params;
const forgeonConfig = useForgeonConfigStore(store);
const submitStreamID = routeParams.submitStreamID ? Number(routeParams.submitStreamID) : null;
const pagerConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 20,
});
const searchForm = ref({
  startTime: undefined,
  endTime: undefined,
  operatorNames: [],
  operationTypes: [],
});
const gridOptions = reactive<VxeGridProps<OperationsListItem>>({
  showOverflow: true,
  maxHeight: '100%',
  border: 'none',
  columns: [
    {
      field: 'point',
      title: '',
      width: '30px',
      align: 'center',
      slots: {
        default() {
          return <div class="h-6px w-6px rd-half bg-FO-Container-Fill5" />;
        },
      },
    },
    {
      field: 'execTime',
      title: '操作时间',
      width: 150,
      slots: {
        default({ row }) {
          return <div class="font-size-12px">{row.execTime ? dayjs(row.execTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</div>;
        },
      },
    },
    {
      field: 'finishTime',
      title: '完成时间',
      width: 150,
      slots: {
        default({ row }) {
          return <div class="font-size-12px">{row.finishTime ? dayjs(row.finishTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</div>;
        },
      },
    },
    {
      field: 'operator',
      title: '操作人',
      width: 150,

      slots: {
        default({ row }) {
          return (
            <div class="w-full flex items-center gap-8px">
              {row?.operator?.headerImg
                ? (
                  <img class="size-24px flex-shrink-0 b-1 b-FO-Container-Stroke0 rounded-full b-solid" src={row?.operator?.headerImg} />
                )
                : (
                  <div class="size-24px flex items-center justify-center rounded-full bg-FO-Datavis-Purple3 c-FO-Datavis-Purple1">
                    <FillAdminIcon />
                  </div>
                )}
              <EllipsisText class="min-w-0 flex-1 font-size-12px text-FO-Content-Text1">
                {row?.operator?.nickName ? row?.operator?.nickName : '提交中心'}
              </EllipsisText>
            </div>
          );
        },
      },
    },
    {
      field: 'operationType',
      title: '操作类型',
      width: 100,
      slots: {
        default({ row }) {
          return <div class="font-size-12px">{(operationTypeOptions.find((e) => e.value === row.operationType)?.label) || ''}</div>;
        },
      },
    },
    {
      field: 'modifications',
      title: '操作详情',
      slots: {
        default({ row }) {
          if (row.operationType === operationTypes.Register) {
            return <div class="font-size-12px">{`注册了实例【${row.checkInstance.name}】（${row.checkInstance.workspace}）`}</div>;
          }
          if (row.operationType === operationTypes.Modify) {
            return (
              <div class="font-size-12px">
                <div>{`修改了实例【${row.checkInstance.name}】（${row.checkInstance.workspace}）：`}</div>
                {row.modifications.map((item) => {
                  return (<div>{`${item.modifyKey === 'name' ? '实例名称' : '流水线'}：由${item.oldValue}更新为${item.newValue}`}</div>);
                })}

              </div>
            );
          }
          const operation = operationTypeOptions.find((e) => e.value === row.operationType);
          return <div class="font-size-12px">{`${operation?.action}了实例【${row.checkInstance.name}】（${row.checkInstance.workspace}）${operation?.function}`}</div>;
        },
      },
    },
  ],
  data: [],
  columnConfig: {
    resizable: false,
  },
});
async function handlePageData() {
  try {
    gridOptions.loading = true;
    await getOperationsListExecute({ id: forgeonConfig.currentProjectId!, streamID: submitStreamID!, page: pagerConfig.currentPage, pageSize: pagerConfig.pageSize, ...searchForm.value }, {});
    pagerConfig.total = operationsList.value?.data?.data?.total || 0;
    gridOptions.data = operationsList.value?.data?.data?.list || [];
  } finally {
    gridOptions.loading = false;
  }
}

function close() {
  return props.modalConfirm();
}
watch(() => searchForm.value, () => {
  handlePageData();
}, { deep: true, immediate: true });
</script>
