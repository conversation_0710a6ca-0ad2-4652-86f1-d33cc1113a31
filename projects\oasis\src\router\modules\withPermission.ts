import type { Router } from 'vue-router';
import { useUserStoreWithOut } from '../../store/modules/user.ts';
import { checkPermissionPass, PlatformEnterPoint } from '@hg-tech/oasis-common';
import { getPermissionInfo } from '../../service/permission/usePermission';

/**
 * 权限检查相关
 */
export function withPermission(router: Router) {
  router.beforeEach(async (to, _, next) => {
    if (to.meta.permissionDeclare == null) {
      // 没有声明权限的路由直接放行
      return next();
    }

    try {
      const userStore = useUserStoreWithOut();
      const permissionInfo = await getPermissionInfo(userStore.getProjectId);
      if (checkPermissionPass(to.meta.permissionDeclare, permissionInfo)) {
        // 权限检查通过
        return next();
      }
    } catch (e) {
      console.error(e);
    }

    next({
      name: PlatformEnterPoint.Forbidden,
      query: {
        status: '403',
        // 路由无法解析 origin，也无法正确解析 query
        redirect: to.path,
      },
    });
  });
}
