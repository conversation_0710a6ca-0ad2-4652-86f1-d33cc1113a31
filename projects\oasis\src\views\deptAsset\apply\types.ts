import type { ChipsetsListItem, DeviceBrandListItem, DeviceCounts, DeviceListItem, FilterTemplateListItem } from '/@/api/page/model/deptAssetModel';
import type { DeptListItem } from '/@/api/page/model/systemModel';

export interface DeptAssetStateType {
  isInit: boolean;
  tags: Record<string, any>;
  counts: DeviceCounts;
  onlyTodoDevice: boolean;
  sortTypeName: string;
  sortTypeDirection: 'desc' | 'asc';
  selectedTags: Record<string, any[]>;
  deptList: DeptListItem[];
  chipsetList: ChipsetsListItem[];
  deviceBrandList: DeviceBrandListItem[];
  urlQuery: {
    assetID?: number;
    chipsetID?: number;
    recycle?: number;
  };
  page: number;
  pageSize: number;
  totalNum: number;
  todoTotal: number;
  deviceList: DeviceListItem[];
  filterTemplateList: FilterTemplateListItem[];
  isDeviceAdmin: boolean;
}
