<template>
  <BasicDrawer
    :class="prefixCls"
    v-bind="$attrs"
    showFooter
    :title="getTitle"
    :showDetailBack="false"
    :width="700"
    @register="registerDrawer"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm">
      <template #serverID="{ model, field }">
        <Select
          v-model:value="model[field]"
          optionFilterProp="description"
          placeholder="请选择服务器"
          :options="p4ServerList"
          :loading="loadingP4ServerList"
          :fieldNames="{ label: 'description', value: 'ID' }"
          :getPopupContainer="getPopupContainer"
          showSearch
          allowClear
          @change="handleServerChange"
        >
          <template #option="{ address, description }">
            <div>{{ description || address }}</div>
            <div v-if="description" class="ml-5 c-FO-Content-Text2">
              {{ address }}
            </div>
          </template>
        </Select>
      </template>
      <template #branchVisible="{ model }">
        <BranchVisibleForm
          v-model:visible="model.visible"
          v-model:visibleMembers="model.visibleMembers"
          v-model:visibleGroups="model.visibleGroups"
          class="ml-26px"
          :chatList="showChatList"
          :groupList="groupList"
        />
      </template>
      <template #notifyForm="{ model, field }">
        <div class="mb-2 ml-2 font-bold">
          {{ field === 'version' ? '发版通知' : '包体检查通知' }}
        </div>
        <NotifyForm
          v-model="model[field]"
          class="ml-26px"
          :chatList="showChatList"
          :groupList="groupList"
        />
      </template>
    </BasicForm>
  </BasicDrawer>
</template>

<script lang="ts" setup name="GamePackageBranchesDrawer">
import { cloneDeep, pick, set } from 'lodash-es';
import { computed, ref, unref } from 'vue';
import { Select } from 'ant-design-vue';
import NotifyForm from './NotifyForm.vue';
import BranchVisibleForm from './BranchVisibleForm.vue';
import { defaultNotify, formSchema } from './branch.data';
import {
  addGamePackage,
  editGamePackage,
  getPkgClassList,
} from '/@/api/page/test';
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
import { BasicForm, useForm } from '/@/components/Form/index';
import { useTrack } from '/@/hooks/system/useTrack';
import { useDesign } from '/@/hooks/web/useDesign';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { getPopupContainer } from '/@/utils';
import { getAllPaginationList } from '/@/hooks/web/usePagination';
import type { GamePackagesListItem, PkgClassListItem } from '/@/api/page/model/testModel';
import { useSysUserGroupList } from '../../../../../hooks/useUserList.ts';
import { usePerforceServerList } from '../../../../../hooks/useP4.ts';

const emit = defineEmits(['success', 'register']);

const { prefixCls } = useDesign('game-package-branches-drawer');
const isUpdate = ref(false);
const isCopy = ref(false);
const editId = ref();
const userStore = useUserStoreWithOut();
const getTitle = computed(() => `${!unref(isUpdate) ? (!unref(isCopy) ? '新增' : '复制') : '编辑'}分支`);
const showChatList = ref<{ label: string; value: string }[]>([]);
const sort = ref(0);
const distribute = ref<PkgClassListItem[]>([]);// 分类分支
const { setTrack } = useTrack();

const serverId = ref();
const { p4ServerList, loadingP4ServerList } = usePerforceServerList();
const { userGroupList } = useSysUserGroupList(serverId);
const groupList = computed(() => (userGroupList.value || []).map((item) => ({
  label: item.name!,
  value: item.name!,
})));

const [registerForm, { getFieldsValue, resetFields, setFieldsValue, validate, clearValidate, updateSchema }] = useForm({
  labelWidth: 130,
  schemas: formSchema,
  showActionButtonGroup: false,
  baseColProps: { span: 23 },
});

const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
  setDrawerProps({ confirmLoading: true });

  await resetFields();
  isUpdate.value = !!data?.isUpdate;
  isCopy.value = !!data?.isCopy;
  sort.value = isUpdate.value ? data.record?.sort : data?.maxSort + 1;
  editId.value = data?.record?.ID;
  serverId.value = data?.record?.serverID;
  showChatList.value = data.chatList?.map((e: any) => ({
    label: e.name,
    value: e.chat_id,
  })) || [];

  const { list } = await getAllPaginationList((p) => getPkgClassList(
    userStore.getProjectId,
    {
      ...p,
      showAll: true,
    },
  ));

  distribute.value = list;
  // 获取所有的分类

  // 上传文件同步改变抽屉加载状态
  const distributeOptions = list.map((item: any) => {
    return {
      label: item.name || '',
      value: item.ID,
    };
  });
  // 获取未分类的ID

  const uncategorizedId = distribute.value.filter((item) => item.is_default)[0].ID;

  await updateSchema([
    {
      field: 'icon',
      componentProps: {
        onIsLoading: (val: boolean) => {
          setDrawerProps({ confirmLoading: val });
        },
      },
    },
    {
      field: 'classID',
      componentProps: {
        options: distributeOptions,
      },
    },
  ]);

  if (unref(isUpdate) || unref(isCopy)) {
    const clonedRecord = cloneDeep(data.record);
    await setFieldsValue({
      ...clonedRecord,
      version: clonedRecord.notifyV3?.version ?? cloneDeep(defaultNotify),
      doctor: clonedRecord.notifyV3?.doctor ?? cloneDeep(defaultNotify),
      visible: clonedRecord.visible ?? 'disable',
      visibleMembers: clonedRecord.visibleMembers ?? [],
      visibleGroups: clonedRecord.visibleGroups ?? [],
      serverID: clonedRecord.serverID || undefined,
      classID: clonedRecord.classID || uncategorizedId,
      briefName: unref(isCopy) ? undefined : clonedRecord.briefName,
    });
    await clearValidate();
  } else {
    await setFieldsValue({
      version: cloneDeep(defaultNotify),
      doctor: cloneDeep(defaultNotify),
      visible: 'disable',
      visibleMembers: [],
      visibleGroups: [],
      classID: uncategorizedId,
    });
  }
  setDrawerProps({ confirmLoading: false });
});

function handleServerChange(value: number) {
  serverId.value = value;
  const values: any = getFieldsValue();
  if (values) {
    const lastData = cloneDeep(pick(values, ['visibleGroups', 'version', 'doctor']));
    set(lastData, ['visibleGroups'], []);
    set(lastData, ['version', 'private', 'groups'], []);
    set(lastData, ['doctor', 'private', 'groups'], []);
    setFieldsValue(lastData);
  }
}

async function handleSubmit() {
  try {
    setDrawerProps({ confirmLoading: true });

    // 基本配置表单校验
    await validate();
    const values: any = getFieldsValue();
    const newData: GamePackagesListItem = {
      ...values,
      notifyV3: {
        version: values.version,
        doctor: values.doctor,
      },
      sort: sort.value,
    };

    if (!unref(isUpdate)) {
      const res = await addGamePackage(userStore.getProjectId, newData);
      if (res?.code === 7) {
        return;
      }

      setTrack('jogsvc1kw8');
      emit('success', 'add');
    } else if (unref(editId)) {
      const res = await editGamePackage(userStore.getProjectId, newData, unref(editId));
      if (res?.code === 7) {
        return;
      }
      setTrack('i7ynithqmg');
      emit('success', 'edit');
    }

    closeDrawer();
  } finally {
    setDrawerProps({ confirmLoading: false });
  }
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-game-package-branches-drawer';
.@{prefix-cls} {
  &__left {
    width: 600px;
    padding-right: 16px;
    border-right: 1px solid;
    border-right-color: @FO-Container-Stroke1;
  }

  &__right {
    flex: 1;
    padding-left: 16px;
  }
}
</style>
