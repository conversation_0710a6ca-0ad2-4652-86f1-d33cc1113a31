<template>
  <div :class="prefixCls">
    <slot />
  </div>
</template>

<script lang="ts" setup name="DevGuardTab">
import { useDesign } from '/@/hooks/web/useDesign';

const { prefixCls } = useDesign('dev-guard-tab');
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-dev-guard-tab';
.@{prefix-cls} {
  display: flex;
  flex-direction: column;
  height: 100%;

  &__header {
    flex: none;
    margin: 20px 20px 0 20px;
    padding: 16px;
    background-color: @FO-Container-Fill1;
    overflow: auto;
    border-radius: 8px;
  }

  &__body {
    flex: auto;
    overflow: auto;
  }
}
</style>
