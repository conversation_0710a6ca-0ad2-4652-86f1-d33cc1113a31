import { renderToolbar } from '@/common/utils/utils';
import { type ImageRenderToolbarProps, type UploadFileInfo, NButton, NEllipsis, NIcon, NImage, NText } from 'naive-ui';
import { type PropType, defineComponent, ref, Transition } from 'vue';
import { AudioFileOutlined, BasicStrokeDelete, BasicStrokeReset, Edit20Regular, EyeOutline, FileCsv, FileZip, Loading3QuartersOutlined, UploadFileOutlined } from '../svg-icons';

type ToolType = 'preview' | 'edit' | 'delete' | 'reupload';

const FileRenderer = defineComponent({
  props: {
    files: {
      type: Array as PropType<Array<UploadFileInfo>>,
      default: () => [],
    },
    mode: {
      type: String as PropType<'card' | 'slot' | 'file'>,
      default: 'card',
    },
    loading: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    showTools: {
      type: Array as PropType<Array<ToolType>>,
      default: () => ['preview', 'edit', 'reupload', 'delete'],
    },
    toolIconSize: {
      type: Number as PropType<number>,
      default: 20,
    },
    toolDisabled: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    onEdit: {
      type: Function as PropType<(files: UploadFileInfo[]) => void>,
      default: () => () => {},
    },
    onDelete: {
      type: Function as PropType<(files: UploadFileInfo[]) => void>,
      default: () => () => {},
    },
    onReupload: {
      type: Function as PropType<(files: UploadFileInfo[]) => void>,
      default: () => () => {},
    },
    onPreview: {
      type: Function as PropType<(files: UploadFileInfo[]) => void>,
      default: () => () => {},
    },
  },
  setup(props) {
    const isMoveIn = ref<boolean>(false);
    const imageRef = ref<typeof NImage>();
    const operations = {
      preview: {
        icon: () => <EyeOutline />,
        onclick: () => (props.mode === 'file'
          ? (props.onPreview ? props.onPreview(props.files) : window.open(props.files[0].url!, '_blank'))
          : imageRef.value?.click()),
      },
      edit: {
        icon: () => <Edit20Regular />,
        onclick: () => {
          props.onEdit(props.files);
          isMoveIn.value = false;
        },
      },
      delete: {
        icon: () => <BasicStrokeDelete />,
        onclick: () => props.onDelete(props.files),
      },
      reupload: {
        icon: () => <BasicStrokeReset />,
        onclick: () => {
          props.onReupload(props.files);
          isMoveIn.value = false;
        },
      },
    };

    const getFileTypeIcon = (type: string) => {
      switch (type) {
        case 'image/png':
        case 'image/jpeg':
        case 'image/jpg':
        case 'image/webp':
          return <UploadFileOutlined />;
        case 'audio/wav':
          return <AudioFileOutlined />;
        case 'text/csv':
          return <FileCsv />;
        case 'application/zip':
        case 'application/x-zip-compressed':
        case 'application/x-zip':
          return <FileZip />;
        default:
          return <UploadFileOutlined />;
      }
    };

    const renderOperation = (type: ToolType) => {
      return (
        <div class="flex-c-center mx-3px rd-4px">
          <NButton
            class="p-4px"
            onClick={operations[type].onclick}
            renderIcon={() => (
              <NIcon color="white" size={props.toolIconSize}>
                {operations[type].icon()}
              </NIcon>
            )}
            size="small"
            text
          />
        </div>
      );
    };

    const renderOperations = () => {
      return (
        <div
          class={[
            'operation pos-absolute bottom-0 left-0 h-full w-full transition-all transition-duration-200',
            isMoveIn.value ? 'bg-FO-Container-Mask40 ' : 'bg-transparent',
          ]}
          onMouseleave={() => (isMoveIn.value = false)}
          onMousemove={() => (isMoveIn.value = true)}
        >
          <Transition
            enterActiveClass="a-fade-in"
            leaveActiveClass="a-fade-out"
          >
            {isMoveIn.value
              ? (
                <div class="absolute-center flex flex-nowrap">
                  {props.showTools.map(renderOperation)}
                </div>
              )
              : null}
          </Transition>
        </div>
      );
    };

    return () => (
      <>
        {
          props.loading
            ? (
              <div class="file-list pos-relative h-full w-full flex flex-col items-center justify-center">
                <NIcon class={['mb-8px c-FO-Content-Icon3', props.loading ? 'animate-spin' : '']} size="20">
                  <Loading3QuartersOutlined />
                </NIcon>
                <NText class="FO-Font-R12 c-FO-Content-Text3">文件上传中...</NText>
              </div>
            )
            : (
              <div class="file-list pos-relative h-full w-full flex justify-center">
                {props.mode === 'card' && props.files.map((file) => {
                  return (
                    <NImage
                      class="h-full justify-center"
                      key={file.id}
                      objectFit="contain"
                      ref={imageRef}
                      renderToolbar={(props: ImageRenderToolbarProps) =>
                        renderToolbar(props, [
                          {
                            url: file.url ?? '',
                            name: file.name,
                          },
                        ])}
                      src={file.url!}
                    />
                  );
                })}
                {props.mode === 'file' && (
                  <div class="flex-c-center h-full w-full flex flex-col">
                    <NIcon class="mb-8px" depth={3} size="120">
                      {getFileTypeIcon(props.files[0].type!)}
                    </NIcon>
                    <NEllipsis
                      class="break-words text-center c-FO-Content-Text1"
                      line-clamp="2"
                    >{props.files[0]?.fullPath}
                    </NEllipsis>
                  </div>
                )}
                {renderOperations()}
              </div>
            )
        }
      </>
    );
  },
});

export {
  FileRenderer,
  ToolType,
};
