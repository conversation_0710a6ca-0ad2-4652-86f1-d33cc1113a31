<template>
  <div>
    <PageHeader v-if="submitStreamID" :submitStreamID="submitStreamID" @back="goBack">
      <template #extra>
        <div class="flex items-center gap-20px">
          <Popconfirm title="确认操作？" okText="确认" cancelText="取消" @confirm="handleBulkUpdate">
            <Button shape="round">
              <div class="flex items-center">
                <span>全部更新</span>
              </div>
            </Button>
          </Popconfirm>
          <Popconfirm title="确认操作？" okText="确认" cancelText="取消" @confirm="handleBulkRestart">
            <Button shape="round">
              <div class="flex items-center">
                <span>全部重启</span>
              </div>
            </Button>
          </Popconfirm>
          <Button shape="round" @click="handleShowSchedule">
            <div class="flex items-center">
              <span>定时任务</span>
            </div>
          </Button>
          <Button shape="round" @click="handleEditRule">
            <div class="flex items-center">
              <span>实例分配规则</span>
            </div>
          </Button>
          <Button shape="round" @click="handleShowOperation">
            <div class="flex items-center">
              <span>操作记录</span>
            </div>
          </Button>
        </div>
      </template>
    </PageHeader>
    <div class="instance-config-list">
      <div
        v-for="item in instanceConfig" :key="item.id"
        class="instance-config-list-item mx-20px my-10px flex justify-between b-rd-8px p-20px bg-FO-Container-Fill1!"
      >
        <div class="flex items-center">
          <Icon :icon="dragIcon" class="list-drag-btn hidden h-20px w-20px cursor-grab" />
          <div class="list-status-dot h-20px w-20px flex items-center justify-center">
            <div class="h-10px w-10px b-rd-5px" :class="getDotClass(item.workState, item.disabled!)" />
          </div>
          <div class="ml-10px">
            <div class="flex items-center gap-10px">
              <span class="FO-Font-B16">{{ item.name || '未命名' }}</span>
              <Icon :icon="editIcon" class="cursor-pointer" @click="editInstanceDetail(item)" />
            </div>
            <div class="flex gap-20px c-FO-Content-Text3">
              <span>类型：{{ instanceTypeOptions.find((i) => i.value === item.instanceType)?.label }}</span>
              <span>Workspace:{{ item.workspace }}</span>
              <span>IP:{{ getIp(item.ipAddress!) }}</span>
            </div>
          </div>
        </div>

        <div class="flex items-center gap-10px">
          <div class="flex gap-10px">
            <span v-if="item.disabled">实例已禁用</span>
            <span>{{ workStateOptions.find(i => i.value === item.workState)?.label }}</span>
          </div>

          <div class="flex gap-10px">
            <Button :disabled=" [workStateType.Offline, workStateType.Restarting, workStateType.Updating].includes(item.workState!) || [pendingStateType.WaitRestart, pendingStateType.WaitRestartAndDisable].includes(item.pendingState!)" @click="instanceUpdate(item.id, [workStateType.Checking].includes(item.workState!) && [pendingStateType.WaitUpdate, pendingStateType.WaitUpdateAndDisable].includes(item.pendingState!))">
              {{ [workStateType.Checking].includes(item.workState!) && [pendingStateType.WaitUpdate, pendingStateType.WaitUpdateAndDisable].includes(item.pendingState!) ? '取消更新' : ([workStateType.Checking].includes(item.workState!) && [pendingStateType.Null, pendingStateType.WaitDisable].includes(item.pendingState!) ? '预约更新' : '更新') }}
            </Button>
            <Button :disabled=" [workStateType.Offline, workStateType.Restarting].includes(item.workState!)" @click="instanceRestart(item.id, [workStateType.Checking, workStateType.Updating].includes(item.workState!) && [pendingStateType.WaitRestart, pendingStateType.WaitRestartAndDisable].includes(item.pendingState!))">
              {{ [workStateType.Checking, workStateType.Updating].includes(item.workState!) && [pendingStateType.WaitRestart, pendingStateType.WaitRestartAndDisable].includes(item.pendingState!) ? '取消重启' : ([workStateType.Checking, workStateType.Updating].includes(item.workState!) && ![pendingStateType.WaitUpdate, pendingStateType.WaitDisable].includes(item.pendingState!) ? '预约重启' : '重启') }}
            </Button>
            <Button :disabled=" [workStateType.Restarting].includes(item.workState!)" @click="instanceDisable(item.id, item.disabled!)">
              <span v-if="item.disabled">启用</span>
              <span v-else-if="[pendingStateType.WaitDisable, pendingStateType.WaitUpdateAndDisable, pendingStateType.WaitRestartAndDisable].includes(item.pendingState!)">取消禁用</span>
              <span v-else-if=" [workStateType.Checking].includes(item.workState!) && [pendingStateType.WaitUpdate, pendingStateType.WaitRestart].includes(item.pendingState!)">预约禁用</span>
              <span v-else>禁用</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
    <AssignRuleModalHolder />
    <InstanceDetailDrawerHolder />
    <OperationDrawerHolder />
    <ScheduleModalHolder />
  </div>
</template>

<script setup lang="ts">
import PageHeader from '../../PageHeader.vue';
import { useRouter } from 'vue-router';
import { Button, message, Popconfirm } from 'ant-design-vue';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';
import { nextTick, onMounted, ref } from 'vue';
import { useSortable } from '../../../hooks/useSortable';
import editIcon from '@iconify-icons/icon-park-outline/edit';
import dragIcon from '@iconify-icons/ic/round-drag-handle';
import { Icon } from '@iconify/vue';
import AssignRuleModal from './AssignRuleModal.vue';
import { useLatestPromise, useModalShow } from '@hg-tech/utils-vue';
import InstanceDetailDrawer from './InstanceDetailDrawer.vue';
import OperationDrawer from './OperationDrawer.vue';
import ScheduleModal from './ScheduleModal.vue';
import { TrackEventName } from '../../../constants/event';
import { traceClickEvent } from '../../../services/track';
import { type InstanceConfigItem, batchSortListApi, cancelInstanceOperateApi, getDistributeApi, getInstanceConfigListApi, getScheduleListApi, setDistributeApi, updateInstancebatchOperateApi, updateInstanceConfigItemApi, updateInstanceOperateApi } from '../../../api';
import { useForgeonConfigStore } from '../../../store/modules/forgeonConfig';
import { store } from '../../../store/pinia';
import { pendingStateType, workStateOptions, workStateType } from './type.data';
import { instanceTypeOptions } from '../type.data';
import { buildUUID } from '../../../utils/uuid';

const router = useRouter();
const routeParams = router.currentRoute.value.params;
const forgeonConfig = useForgeonConfigStore(store);
const instanceConfig = ref<InstanceConfigItem[]>([]);
const [AssignRuleModalHolder, showAssignRuleModal] = useModalShow(AssignRuleModal);
const [InstanceDetailDrawerHolder, showInstanceDetailDrawer] = useModalShow(InstanceDetailDrawer);
const [OperationDrawerHolder, showOperationRecordModal] = useModalShow(OperationDrawer);
const [ScheduleModalHolder, showScheduleModal] = useModalShow(ScheduleModal);
const { execute: instanceConfigExecute, data: instanceConfigList } = useLatestPromise(getInstanceConfigListApi);
const { execute: batchSortListExecute } = useLatestPromise(batchSortListApi);
const { execute: getScheduleListExecute, data: scheduleList } = useLatestPromise(getScheduleListApi);
const { execute: updateInstancebatchOperateExecute } = useLatestPromise(updateInstancebatchOperateApi);
const { execute: updateInstanceConfigItemExecute, data: updateInstanceConfigItemRes } = useLatestPromise(updateInstanceConfigItemApi);
const { execute: updateInstanceOperateExecute } = useLatestPromise(updateInstanceOperateApi);
const { execute: cancelInstanceOperateExecute } = useLatestPromise(cancelInstanceOperateApi);
const { execute: getDistributeExecute, data: distributeItem } = useLatestPromise(getDistributeApi);
const { execute: setDistributeExecute } = useLatestPromise(setDistributeApi);
const submitStreamID = routeParams.submitStreamID ? Number(routeParams.submitStreamID) : null;
async function handleEditRule() {
  await getDistributeExecute({ id: forgeonConfig.currentProjectId!, streamID: submitStreamID! }, {});
  await showAssignRuleModal({
    distributeType: distributeItem.value?.data?.data?.distributeType,
    ruleList: distributeItem.value?.data?.data?.ruleList?.map((item) => ({ ...item, id: buildUUID() })),
    fallbackType: distributeItem.value?.data?.data?.fallbackType,
    async sentReq(distributeType: number, ruleList: any[], fallbackType: number) {
      const res = await setDistributeExecute({ id: forgeonConfig.currentProjectId! }, { streamID: submitStreamID!, distributeType, ruleList, fallbackType });
      return res?.data?.data;
    },
  });
  message.success('保存成功');
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_ALLOCATION_SAVE);
}
function getDotClass(workState: number | undefined, disabled: boolean) {
  if (!workState) {
    return '';
  }
  if (disabled) {
    return 'bg-FO-Functional-Error1-Default';
  } else {
    switch (workState) {
      case workStateType.Checking:
      case workStateType.Updating:
        return 'bg-FO-Datavis-Yellow2';
      case workStateType.Restarting:
      case workStateType.Offline:
        return 'bg-FO-Content-Text4';
      case workStateType.Idle:
        return 'bg-FO-Datavis-Blue2';
    }
  }
}
async function handleShowSchedule() {
  await getScheduleListExecute({ id: forgeonConfig.currentProjectId!, streamID: submitStreamID! }, {});
  await showScheduleModal({
    scheduleList: scheduleList.value?.data?.data,
    instanceConfig: instanceConfig.value,
    reload: async () => {
      const res = await getScheduleListExecute({ id: forgeonConfig.currentProjectId!, streamID: submitStreamID! }, {});
      return res?.data?.data;
    },
  });
}
function handleShowOperation() {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_OPERATION_LOG_CLICK);
  showOperationRecordModal({});
}
async function getInstanceConfigList() {
  await instanceConfigExecute({ id: forgeonConfig.currentProjectId!, streamID: submitStreamID! }, {});
  instanceConfig.value = instanceConfigList.value?.data?.data || [];
}

function goBack() {
  router.push({
    name: PlatformEnterPoint.Instance,
    params: {
      submitStreamID,
    },
  });
}
function getIp(ipAddress: string) {
  if (!ipAddress) {
    return '';
  }
  const url = new URL(ipAddress);
  return url.hostname;
}

async function batchOperate(operation: number) {
  await updateInstancebatchOperateExecute({ id: forgeonConfig.currentProjectId! }, { streamID: submitStreamID!, operation });
  await getInstanceConfigList();
}
async function editInstanceDetail(item: InstanceConfigItem) {
  await showInstanceDetailDrawer({
    item,
    async sentReq(name: string, piplineUrl: string) {
      await updateInstanceConfigItemExecute({ id: forgeonConfig.currentProjectId! }, { streamID: submitStreamID!, instanceID: item.id!, name, piplineUrl });
      return updateInstanceConfigItemRes.value?.data?.code === 0;
    },
  });

  await getInstanceConfigList();
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_EDIT_SAVE);
}
function handleBulkUpdate() {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_BULK_UPDATE_CLICK);
  batchOperate(1);
}
function handleBulkRestart() {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_BULK_RESTART_CLICK);
  batchOperate(2);
}
async function updateInstanceOperate(instanceID: number, operation: number) {
  await updateInstanceOperateExecute({ id: forgeonConfig.currentProjectId! }, { streamID: submitStreamID!, instanceID: instanceID!, operation });
  await getInstanceConfigList();
}
async function cancelInstanceOperate(instanceID: number, operation: number) {
  await cancelInstanceOperateExecute({ id: forgeonConfig.currentProjectId! }, { streamID: submitStreamID!, instanceID: instanceID!, operation });
  await getInstanceConfigList();
}
async function instanceUpdate(instanceID: number, isCancel: boolean) {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_SINGLE_UPDATE_CLICK);
  if (isCancel) {
    await cancelInstanceOperate(instanceID, 1);
  } else {
    await updateInstanceOperate(instanceID, 1);
  }
}
async function instanceRestart(instanceID: number, isCancel: boolean) {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_SINGLE_RESTART_CLICK);
  if (isCancel) {
    await cancelInstanceOperate(instanceID, 2);
  } else {
    await updateInstanceOperate(instanceID, 2);
  }
}
async function instanceDisable(instanceID: number, isCancel: boolean) {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_SINGLE_DISABLE_CLICK);
  if (isCancel) {
    await cancelInstanceOperate(instanceID, 3);
  } else {
    await updateInstanceOperate(instanceID, 3);
  }
}
// 初始化拖拽
function initDrag() {
  nextTick(() => {
    const el = document.querySelector(`.instance-config-list`) as HTMLElement;
    useSortable(el, {
      handle: `.list-drag-btn`,
      onEnd: async ({ oldIndex, newIndex }: { oldIndex?: number; newIndex?: number }) => {
        if (oldIndex === undefined || newIndex === undefined || oldIndex === newIndex) {
          return;
        }
        const temp = instanceConfig.value.map((item) => item.id) as number[];
        const current = temp[oldIndex];
        temp.splice(oldIndex, 1);
        temp.splice(newIndex, 0, current);
        await batchSortListExecute({ id: forgeonConfig.currentProjectId! }, { streamID: submitStreamID!, ids: temp });
        message.success('保存成功');
      },
    });
  });
}
onMounted(async () => {
  await getInstanceConfigList();
  initDrag();
});
</script>

<style lang="less" scoped>
.instance-config-list {
  max-height: calc(100vh - 200px);
  overflow: auto;

  .instance-config-list-item {
    &:hover {
      .list-drag-btn {
        display: block;
      }

      .list-status-dot {
        display: none;
      }
    }
  }
}
</style>
