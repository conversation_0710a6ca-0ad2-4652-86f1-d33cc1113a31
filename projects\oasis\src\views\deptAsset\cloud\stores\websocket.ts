import { defineStore } from 'pinia';
import { computed, nextTick, ref } from 'vue';
import Scrcpy from '../scrcpy';
import { isHttpProtocol } from '/@/utils/is';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { useCloudDeviceStore } from './device';
import { type UploadFile, message, Modal } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';
import type { CloudDeviceAppItem, ScreenshotItem } from '../types';
import { useAudioProcessor } from '../composables/useAudioProcessor';
import type AudioProcessor from '../audio-processor';
import { getMtlInstallRecordList } from '/@/api/page/mtl/device';
import { type MtlInstallRecordListItem, MtlInstallRecordPlatform, MtlInstallRecordSource } from '/@/api/page/mtl/model/deviceModel';
import { sendEvent } from '../../../../service/tracker/index.ts';
import { useSimulationProgress } from '../composables/useSimulationProgress';
import { useErrorHandler } from '../composables/useErrorHandler';
import { useAppManagement } from '../composables/useAppManagement.ts';
import { useCloudDeviceLogStore } from './log';
import { useCloudDeviceTerminalStore } from './terminal';

export const useCloudDeviceWebsocketStore = defineStore('cloudDeviceWebsocket', () => {
  const cloudDeviceStore = useCloudDeviceStore();
  const logStore = useCloudDeviceLogStore();
  const terminalStore = useCloudDeviceTerminalStore();
  const { push } = useRouter();
  const { startInstallProgressSimulation, stopInstallProgressSimulation } = useSimulationProgress();
  const { showErrorWithFault } = useErrorHandler();
  const wsPrefix = computed(() =>
    (isHttpProtocol ? `ws://${cloudDeviceStore.agent?.host}:${cloudDeviceStore.agent?.port}/websockets` : `wss://${cloudDeviceStore.agent?.domain}/proxy/websockets`),
  );
  const userStore = useUserStoreWithOut();
  const websocket = ref<WebSocket>();
  const screenWebsocket = ref<WebSocket>();
  const scrcpy = ref<Scrcpy>();
  const terminalWebsocket = ref<WebSocket>();
  const webSocketParams = ref<Record<string, any>>({});
  const screenMode = ref(localStorage.getItem('screenMode') || 'Scrcpy');
  const oldBlob = ref<Blob>();
  const touchWrapper = ref<HTMLCanvasElement | HTMLVideoElement>();
  const directionStatus = ref<number>(0);
  const loading = ref(true);
  const imgWidth = ref(0);
  const imgHeight = ref(0);
  const androidPerfRef = ref<any>();
  const pocoLoading = ref(false);
  const pocoPaneRef = ref<any>();
  const proxyWebPort = ref(0);
  const proxyConnPort = ref(0);
  const iFrameHeight = ref(0);
  const pullLoading = ref(false);
  const pullResult = ref('');
  const pasteText = ref('');
  const pushLoading = ref(false);
  const remoteAdbLoading = ref(true);
  const remoteAdbUrl = ref('');
  const currentId = ref<number[]>([1]);
  const elementData = ref<any[]>([]);
  const isShowTree = ref(false);
  const elementLoading = ref(false);
  const webViewData = ref<any[]>([]);
  const activity = ref<any[]>([]);
  const appList = ref<CloudDeviceAppItem[]>([]);
  const driverLoading = ref(false);
  const isDriverFinish = ref(false);
  const debugLoading = ref(false);
  const checkElementLoading = ref(false);
  const webViewLoading = ref(false);
  const elementScreenLoading = ref(false);
  const imgElementUrl = ref('');
  const dialogImgElement = ref(false);
  const webViewListDetail = ref<any[]>([]);
  const isConnectWifi = ref(false);
  const currentWifi = ref('');
  const logcatOutPut = ref<any[]>([]);

  const screenshotList = ref<ScreenshotItem[]>([]);
  const remoteSIBPort = ref(0);
  const remoteWDAPort = ref(0);
  const audioPlayer = ref<AudioProcessor>();
  const isConnectAudio = ref(false);
  const canPlayAudio = ref(false);
  const isShowCloseModal = ref(false);
  const recentApps = ref<MtlInstallRecordListItem[]>([]);
  const canvasRef = ref<HTMLCanvasElement>();
  const scrcpyVideoRef = ref<HTMLVideoElement>();
  const iosCapRef = ref<HTMLCanvasElement>();
  const debugPicRef = ref<HTMLCanvasElement>();
  const audioPlayerRef = ref<HTMLVideoElement>();

  const downloadPercent = ref(0);
  const installPercent = ref(0);
  const installStatus = ref<'downloading' | 'installing' | 'resourcing' | 'success' | 'error' | undefined>();
  const curUploadingUUID = ref<string>();
  const curUploadingName = ref<string>();
  // 当前正在安装的应用信息
  const curInstallingApp = ref<MtlInstallRecordListItem>();
  const frontendUploadTime = ref<number>(0); // 前端上传耗时（秒）
  const fileList = ref<UploadFile[]>([]);
  const isUserClosed = ref(false);
  const isAgentUpdate = ref(false);
  const isUserStopUpload = ref(false);

  // 设置前端上传时间
  const setFrontendUploadTime = (time: number) => {
    frontendUploadTime.value = time;
  };

  // 清理前端上传时间
  const clearFrontendUploadTime = () => {
    frontendUploadTime.value = 0;
  };
  const hasClosed = ref(false);

  function initData() {
    loading.value = true;
    isConnectAudio.value = false;
    screenshotList.value = [];
    logcatOutPut.value = [];

    webViewListDetail.value = [];
    webViewData.value = [];
    pasteText.value = '';
    isShowCloseModal.value = false;
    remoteAdbLoading.value = true;
    downloadPercent.value = 0;
    fileList.value = [];
    curUploadingUUID.value = undefined;
    curUploadingName.value = undefined;
    curInstallingApp.value = undefined;
    installStatus.value = undefined;
    recentApps.value = [];
    appList.value = [];
    isUserClosed.value = false;
    isAgentUpdate.value = false;
    hasClosed.value = false;
    isUserStopUpload.value = false;
  }

  /**
   * 获取最近安装的应用列表
   */
  async function getRecentApps(reload = true) {
    let list: MtlInstallRecordListItem[] = recentApps.value;
    if (reload) {
      const { data: { data } } = await getMtlInstallRecordList({ platform: cloudDeviceStore.isAndroid ? MtlInstallRecordPlatform.Android : MtlInstallRecordPlatform.iOS }, {});
      list = data || [];
    }
    const key = cloudDeviceStore.isAndroid ? 'packageName' : 'bundleId';
    const versionKey = cloudDeviceStore.isAndroid ? 'versionName' : 'shortVersion';
    recentApps.value = list.map((item) => {
      const isInstalling = curInstallingApp.value?.uuid === item.uuid
        || (!!curInstallingApp.value?.pkgName && !!curInstallingApp.value?.version
          && curInstallingApp.value?.pkgName === item.pkgName && curInstallingApp.value?.version === item.version);
      return {
        ...item,
        isInstalling,
        isInstalled: appList.value.some((app) => app[key] === item.pkgName && app[versionKey] === item.version),
      };
    });
  }

  function changeScreenMode(type: string, isInit: number) {
    if (isInit !== 1) {
      loading.value = true;
      scrcpy.value?.switchMode(type);
      screenMode.value = type;
      oldBlob.value = undefined;
    }
    if (type === 'Minicap') {
      touchWrapper.value = canvasRef.value;
    } else {
      oldBlob.value = undefined;
      touchWrapper.value = scrcpyVideoRef.value;
    }
    localStorage.setItem('screenMode', type);
  }

  function setOtherWebSocketParams(param: Record<string, any>) {
    webSocketParams.value = param;
  }

  function websocketSend(msg: any) {
    websocket.value?.send(JSON.stringify(msg));
    if (cloudDeviceStore.isMultiple) {
      setOtherWebSocketParams(msg);
    }
  }

  function terminalWebsocketSend(msg: any) {
    terminalWebsocket.value?.send(JSON.stringify(msg));
  }

  async function refreshAppList(reload = false, noMsg = false) {
    if (reload) {
      appList.value = [];
      if (!noMsg) {
        message.success('加载应用列表中，请稍后...');
      }
    }
    await getRecentApps();
    terminalWebsocketSend({ type: 'appList' });
  }

  async function websocketOnmessage(msg: MessageEvent) {
    const data = JSON.parse(msg.data);
    switch (data.msg) {
      case 'share':
        remoteSIBPort.value = data.port;
        remoteAdbLoading.value = false;
        break;
      case 'perfDetail':
        androidPerfRef.value?.setData(data.detail);
        break;
      case 'poco': {
        pocoLoading.value = false;
        const { result } = data;
        if (result) {
          message.success('获取Poco控件成功');
          pocoPaneRef.value?.setPocoData(JSON.parse(result).result);
        } else {
          message.error('获取POCO控件失败，请确保已经打开对应游戏引擎并接入Poco-SDK');
        }
        break;
      }
      case 'proxyResult': {
        proxyWebPort.value = data.webPort;
        proxyConnPort.value = data.port;
        await nextTick(() => {});
        iFrameHeight.value = document.body.clientHeight - 150;
        break;
      }
      case 'pullResult': {
        pullLoading.value = false;
        if (data.status === 'success') {
          message.success('拉取文件成功');
          pullResult.value = data.url;
        } else {
          message.error('拉取文件失败');
        }
        break;
      }
      case 'paste': {
        pasteText.value = data.detail;
        message.success('获取剪切板文本成功');
        break;
      }
      case 'pushResult': {
        pushLoading.value = false;
        if (data.status === 'success') {
          message.success('上传文件成功');
        } else {
          message.error('上传文件失败，上传目录需要补齐文件名');
        }
        break;
      }
      case 'sas': {
        remoteAdbLoading.value = false;
        if (
          data.isEnable
          && data.port > 0
        ) {
          remoteAdbUrl.value = `${cloudDeviceStore.agent?.host}:${data.port}`;
        }
        break;
      }
      case 'tree': {
        message.success('获取原生控件元素成功！');
        currentId.value = [1];
        elementData.value = data.detail;
        isShowTree.value = true;
        elementLoading.value = false;
        webViewData.value = data.webView;
        activity.value = data.activity;
        break;
      }
      case 'treeFail': {
        message.error('获取原生控件元素失败！请重新获取');
        elementLoading.value = false;
        break;
      }
      case 'packageTest': {
        if (data.status === 'installing') {
          curUploadingUUID.value = data.uuid;
          downloadPercent.value = 0;
          fileList.value = [
            {
              uid: '1',
              name: curUploadingName.value || '',
              size: 1,
              status: 'done',
              url: '',
            },
          ];
        } else if (data.status === 'success') {
          if (curUploadingUUID.value === data.uuid) {
            downloadPercent.value = 50;
            fileList.value = [];
            getRecentApps();
          }
        } else {
          showErrorWithFault({ title: '上传失败', log: data.info, faultType: '应用上传' });
          if (curUploadingUUID.value === data.uuid) {
            downloadPercent.value = 0;
            curUploadingUUID.value = undefined;
            if (fileList.value[0]) {
              fileList.value[0].status = 'error';
              fileList.value[0].response = '上传失败';
            }
          }
        }
        break;
      }
      case 'downloadProcess': {
        if (curUploadingUUID.value === data.uuid) {
          downloadPercent.value = (data.ratio || 0) / 2;
        } else if (curInstallingApp.value?.uuid === data.uuid) {
          if (installStatus.value !== 'downloading') {
            installStatus.value = 'downloading';
          }
          installPercent.value = data.ratio || 0;
        }
        break;
      }
      case 'downloadFinish': {
        if (data.status === 'success') {
          // 上传区的下载流程
          if (curUploadingUUID.value === data.uuid) {
            message.success('上传成功');
            downloadPercent.value = 50;
            fileList.value = [];
            curUploadingUUID.value = undefined;
            await getRecentApps();
            const curRecentApp = recentApps.value.find((item) => item.uuid === data.uuid);
            if (curRecentApp && !curInstallingApp.value?.uuid) {
              const { handleInstall } = useAppManagement();
              handleInstall(curRecentApp);
            }
            // 计算总时间：前端上传时间 + 后端处理时间
            const backendTime = data.time ? Math.ceil(data.time) : 0;
            const totalTime = curRecentApp?.source === MtlInstallRecordSource.PackageCenter
              ? backendTime // 包体中心上传只计算后端时间
              : frontendUploadTime.value + backendTime; // 本地上传计算前端+后端时间

            sendEvent('cloud_device_app_upload', {
              is_success: true,
              time_length: totalTime,
              cloud_device_upload_type: curRecentApp?.source === MtlInstallRecordSource.PackageCenter ? '包体中心上传' : '本地上传',
              cloud_device_package_size: Number(curRecentApp?.size?.split(' ')?.[0]) || 0,
              ...cloudDeviceStore.deviceTrackInfo,
            });
            // 清理前端上传时间
            clearFrontendUploadTime();
          } else if (curInstallingApp.value?.uuid === data.uuid) {
            // 安装区的下载流程
            installPercent.value = 100;
          }
        } else {
          if (curUploadingUUID.value === data.uuid) {
            downloadPercent.value = 0;
            curUploadingUUID.value = undefined;
            showErrorWithFault({ title: '上传失败', log: data.info, faultType: '应用上传' });
            if (fileList.value?.[0]) {
              fileList.value[0].status = 'error';
              fileList.value[0].response = '上传失败';
              sendEvent('cloud_device_app_upload', {
                is_success: false,
                time_length: data.time ? Math.ceil(data.time) : 0,
                cloud_device_upload_type: curUploadingName.value ? '包体中心上传' : '本地上传',
                cloud_device_package_size: fileList.value[0]?.size ? Number((fileList.value[0].size / 1024 / 1024).toFixed(1)) : 0,
                ...cloudDeviceStore.deviceTrackInfo,
              });
            }
          } else if (curInstallingApp.value?.uuid === data.uuid) {
            installStatus.value = 'error';
            installPercent.value = 0;
            const uuid = curInstallingApp.value?.uuid;
            curInstallingApp.value = undefined;
            getRecentApps();
            showErrorWithFault({
              title: '安装失败',
              log: data.info,
              faultType: '应用安装',
              uuid,
            });
          }
        }
        curUploadingName.value = undefined;
        break;
      }
      case 'installStart': {
        if (curUploadingUUID.value === data.uuid) {
          curInstallingApp.value = {
            uuid: data.uuid,
            pkgName: data.packageName,
            version: data.versionName,
          } as MtlInstallRecordListItem;
          curUploadingUUID.value = undefined;
          fileList.value = [];
          message.success('开始安装，请稍后...');
          await getRecentApps();
        }
        installStatus.value = 'installing';
        installPercent.value = 0;

        // 获取当前安装应用的文件大小并开始进度模拟
        const curRecentApp = recentApps.value.find((item) => item.uuid === data.uuid);

        if (curRecentApp?.size) {
          startInstallProgressSimulation(curRecentApp.size, (progress) => {
            installPercent.value = progress;
          });
        }

        break;
      }
      case 'installFinish': {
        // 停止进度模拟
        stopInstallProgressSimulation();

        if (data.status === 'success') {
          installPercent.value = 100;
          message.success('安装成功');
        } else if (!isUserStopUpload.value && !cloudDeviceStore.isAndroid) {
          // 安卓的完全安装结束判断在installDone中，此处只处理 iOS 安装失败
          const uuid = curInstallingApp.value?.uuid;
          curInstallingApp.value = undefined;
          installStatus.value = 'error';
          installPercent.value = 0;
          showErrorWithFault({
            title: '安装失败',
            log: data.info,
            faultType: '应用安装',
            uuid,
          });
        }
        if (!cloudDeviceStore.isAndroid) {
          refreshAppList();
        }
        break;
      }
      case 'resourceProcess': {
        if (curInstallingApp.value?.uuid === data.uuid) {
          installStatus.value = 'resourcing';
          installPercent.value = data.ratio || 0;
        }
        break;
      }
      case 'resourceFinish': {
        if (curInstallingApp.value?.uuid === data.uuid) {
          if (data.status === 'success') {
            installPercent.value = 100;
            installStatus.value = 'success';
            curInstallingApp.value = undefined;
          } else if (!isUserStopUpload.value) {
            installStatus.value = 'error';
            installPercent.value = 0;
            showErrorWithFault({
              title: '安装失败',
              log: data.info,
              faultType: '应用安装',
              uuid: curInstallingApp.value?.uuid,
            });
          }
        }
        break;
      }
      case 'installDone': {
        if (curInstallingApp.value?.uuid === data.uuid) {
          if (data.status === 'success') {
            installPercent.value = 100;
            installStatus.value = 'success';
            curInstallingApp.value = undefined;
          } else if (!isUserStopUpload.value) {
            const uuid = curInstallingApp.value?.uuid;
            curInstallingApp.value = undefined;
            installStatus.value = 'error';
            installPercent.value = 0;
            showErrorWithFault({
              title: '安装失败',
              log: data.info,
              faultType: '应用安装',
              uuid,
            });
          }
        }
        refreshAppList();
        break;
      }
      case 'uninstallFinish': {
        if (data.detail === 'success') {
          message.success('卸载成功');
        } else {
          message.error('卸载失败');
        }
        refreshAppList(true, true);
        break;
      }
      case 'uninstallDetected': {
        refreshAppList(true, true);
        break;
      }
      case 'openDriver': {
        if (data.status !== 'success') {
          message.error('初始化 UIAutomator2 Server 失败');
        } else {
          if (cloudDeviceStore.isAndroid) {
            driverLoading.value = false;
            isDriverFinish.value = true;
          } else {
            imgWidth.value = data.width;
            imgHeight.value = data.height;
            isDriverFinish.value = true;
            remoteWDAPort.value = data.wda;
            remoteAdbLoading.value = false;
            loading.value = false;
          }
          if (canPlayAudio.value) {
            const audioWsUrl = `${wsPrefix.value}/audio/${cloudDeviceStore.agent?.secretKey}/${cloudDeviceStore.mtlDevice?.udId}`;
            const { playAudio } = useAudioProcessor();
            playAudio(audioWsUrl);
          }
        }
        break;
      }
      case 'rotation': {
        if (directionStatus.value !== data.value) {
          loading.value = true;
          message.success('检测到屏幕旋转，请稍后...');
          directionStatus.value = data.value;
        }
        if (screenMode.value === 'Scrcpy') {
          scrcpy.value?.jmuxer?.reset();
        }
        loading.value = false;
        break;
      }
      case 'status': {
        debugLoading.value = false;
        checkElementLoading.value = false;
        message.info('运行完毕');
        break;
      }
      case 'forwardView': {
        webViewLoading.value = false;
        message.success('获取成功');
        webViewListDetail.value = data.detail;
        break;
      }
      case 'eleScreen': {
        if (data.img) {
          message.success('获取快照成功');
          imgElementUrl.value = data.img;
          dialogImgElement.value = true;
        } else {
          message.error('获取快照失败');
        }
        elementScreenLoading.value = false;
        break;
      }
      case 'error': {
        if (isShowCloseModal.value) {
          return;
        }
        isShowCloseModal.value = true;
        Modal.error({
          title: '系统出现异常，已结束使用',
          onOk: () => {
            closeWebsocket();
          },
        });
        break;
      }
      case 'stopUse': {
        if (!isUserClosed.value) {
          Modal.warning({
            title: '用户自行退出，已结束使用',
            onOk: () => {
              closeWebsocket();
            },
          });
        }
        break;
      }
      case 'stopDebug': {
        Modal.warning({
          title: '被管理员解除使用，已结束使用',
          onOk: () => {
            closeWebsocket();
          },
        });
        break;
      }
      case 'idleExit': {
        Modal.warning({
          title: '闲置时长超过 30 分钟，已结束使用',
          onOk: () => {
            closeWebsocket();
          },
        });
        break;
      }
      case 'timeup': {
        Modal.warning({
          title: '达到最大使用时长，已结束使用',
          onOk: () => {
            closeWebsocket();
          },
        });
        break;
      }
      case 'agentUpdate': {
        isAgentUpdate.value = true;
        Modal.warning({
          title: '设备连接维护提醒',
          content: '由于系统升级，设备将断开连接，预计3分钟后恢复，请耐心等待设备重新上线',
          okText: '我知道了',
          onOk: () => {
            closeWebsocket(true, true);
          },
        });
        break;
      }
      case 'addTimeResult': {
        if (data.status === 'success') {
          message.success('续期成功');
        } else {
          message.error('续期失败');
        }
        break;
      }
      case 'quitAccount': {
        message.error('手机厂商账号不可登出，也不可修改');
        break;
      }
      case 'audioCheck': {
        if (data.status === 'success') {
          canPlayAudio.value = true;
        }
        break;
      }
      default:
        break;
    }
  }

  const screenWebsocketOnmessage = (msg: MessageEvent) => {
    if (typeof msg.data === 'object') {
      oldBlob.value = msg.data;
      const blob = new Blob([msg.data], { type: 'image/jpeg' });
      const URL = window.URL || window.webkitURL;
      const image = new Image();
      const canvas = cloudDeviceStore.isAndroid ? canvasRef.value : iosCapRef.value;
      if (!canvas) {
        return;
      }
      const g = canvas.getContext('2d') as CanvasRenderingContext2D;
      image.onload = () => {
        const { width } = image;
        const { height } = image;
        canvas.width = width;
        canvas.height = height;
        g.drawImage(image, 0, 0, width, height);
      };
      const u = URL.createObjectURL(blob);
      image.src = u;
    } else {
      const data = JSON.parse(msg.data);
      switch (data.msg) {
        case 'rotation': {
          if (directionStatus.value !== data.value) {
            loading.value = true;
            message.success('检测到屏幕旋转，请稍后...');
            directionStatus.value = data.value;
          }
          if (screenMode.value === 'Scrcpy') {
            scrcpy.value?.jmuxer?.reset();
          }
          break;
        }
        case 'support': {
          message.error(data.text);
          loading.value = false;
          break;
        }
        case 'size': {
          imgWidth.value = data.width;
          imgHeight.value = data.height;
          loading.value = false;
          break;
        }
        case 'picFinish': {
          loading.value = false;
          break;
        }
        case 'error':
          if (isShowCloseModal.value) {
            return;
          }
          isShowCloseModal.value = true;
          Modal.error({
            title: '系统出现异常，已结束使用！',
            onOk: () => {
              closeWebsocket();
            },
          });
          break;
        default:
          break;
      }
    }
  };

  function terminalWebsocketOnmessage(msg: MessageEvent) {
    const data = JSON.parse(msg.data);
    switch (data.msg) {
      case 'wifiListDetail': {
        isConnectWifi.value = data.detail.isConnectWifi;
        currentWifi.value = data.detail.connectedWifi.SSID;
        break;
      }
      case 'appListDetail': {
        const key = cloudDeviceStore.isAndroid ? 'packageName' : 'bundleId';
        const versionKey = cloudDeviceStore.isAndroid ? 'versionName' : 'shortVersion';
        const findIndex = appList.value.findIndex((item) => item[key] === data.detail[key]);
        if (findIndex === -1) {
          appList.value.push(data.detail);
        } else {
          appList.value[findIndex] = data.detail;
        }
        recentApps.value = recentApps.value.map((item) => {
          if (item.pkgName === data.detail[key] && item.version === data.detail[versionKey]) {
            return {
              ...item,
              isInstalled: true,
            };
          } else if (item.pkgName === data.detail[key]) {
            return {
              ...item,
              isInstalled: false,
            };
          }
          return item;
        });
        break;
      }
      case 'logcat':
      {
        logcatOutPut.value.push('连接成功');
        // 通知新的日志系统连接成功
        logStore.setConnected(true);
        break;
      }
      case 'logDetail':
      case 'logcatResp':
        logStore.addLogEntry(data.detail);
        break;
      case 'terminal':
        terminalStore.handleTerminalConnected(data.user);
        break;
      case 'terResp':
        terminalStore.handleTerminalResponse(data.detail);
        break;
      case 'terDone':
        terminalStore.handleCommandDone();
        break;
      case 'error':
        if (isShowCloseModal.value) {
          return;
        }
        isShowCloseModal.value = true;
        Modal.error({
          title: '系统出现异常，已结束使用',
          onOk: () => {
            closeWebsocket();
          },
        });
        break;
      default:
        break;
    }
  }

  /**
   * 关闭 WebSocket
   * @param goBack - 是否返回我的占用
   * @param toMyOccupy - 是否返回我的占用
   */
  function closeWebsocket(goBack = true, toMyOccupy = false) {
    if (hasClosed.value) {
      return;
    }
    hasClosed.value = true;
    curUploadingUUID.value = undefined;

    // 停止进度模拟
    stopInstallProgressSimulation();

    // 取消安装
    if (curInstallingApp.value?.uuid) {
      websocketSend({
        type: 'cancelInstall',
        uuid: curInstallingApp.value?.uuid,
      });
    }

    setOtherWebSocketParams({
      type: 'close',
    });
    if (websocket.value) {
      websocket.value.close();
      websocket.value = undefined;
    }
    if (screenWebsocket.value) {
      screenWebsocket.value.close();
      screenWebsocket.value = undefined;
      scrcpy.value?.destroy();
      scrcpy.value = undefined;
    }
    if (terminalWebsocket.value) {
      terminalWebsocket.value.close();
      terminalWebsocket.value = undefined;
    }
    if (audioPlayer.value) {
      const { destroyAudio } = useAudioProcessor();
      destroyAudio();
    }
    localStorage.removeItem('driverTime');
    if (goBack) {
      push({ name: PlatformEnterPoint.CloudDevice, query: { tab: toMyOccupy ? 'myOccupy' : undefined } });
    }
  }

  function openSocket() {
    initData();
    terminalStore.initTerminalData();
    if (!cloudDeviceStore.mtlDevice || !cloudDeviceStore.agent) {
      message.error('获取设备信息失败');
      return;
    }
    if ('WebSocket' in window) {
      websocket.value = new WebSocket(
        `${wsPrefix.value}/${cloudDeviceStore.isAndroid ? 'android' : 'ios'}/${cloudDeviceStore.agent?.secretKey}/${cloudDeviceStore.mtlDevice?.udId}/${userStore.getToken}`,
      );
      if (cloudDeviceStore.isAndroid) {
        scrcpy.value = new Scrcpy({
          socketURL: `${wsPrefix.value}/android/screen/${cloudDeviceStore.agent?.secretKey}/${cloudDeviceStore.mtlDevice?.udId}/${userStore.getToken}`,
          node: scrcpyVideoRef.value,
          onmessage: screenWebsocketOnmessage,
          excuteMode: screenMode.value,
        });
        screenWebsocket.value = scrcpy.value.websocket;
      } else {
        screenWebsocket.value = new WebSocket(
          `${wsPrefix.value}/ios/screen/${cloudDeviceStore.agent?.secretKey}/${cloudDeviceStore.mtlDevice?.udId}/${userStore.getToken}`,
        );
      }
      if (screenWebsocket.value) {
        if (!cloudDeviceStore.isAndroid) {
          screenWebsocket.value.onmessage = screenWebsocketOnmessage;
        }
        screenWebsocket.value.onopen = () => {};
      }
      changeScreenMode(screenMode.value, 1);

      terminalWebsocket.value = new WebSocket(
        `${wsPrefix.value}/${cloudDeviceStore.isAndroid ? 'android' : 'ios'}/terminal/${cloudDeviceStore.agent?.secretKey}/${cloudDeviceStore.mtlDevice?.udId}/${userStore.getToken}`,
      );
    } else {
      console.error('不支持WebSocket');
    }
    if (websocket.value) {
      websocket.value.onmessage = websocketOnmessage;
      websocket.value.onopen = () => {};
    }
    if (terminalWebsocket.value) {
      terminalWebsocket.value.onmessage = terminalWebsocketOnmessage;
      terminalWebsocket.value.onopen = () => {
        refreshAppList();
      };
    }
  }

  return {
    websocket,
    websocketSend,
    screenWebsocket,
    canvasRef,
    scrcpyVideoRef,
    iosCapRef,
    debugPicRef,
    audioPlayerRef,
    scrcpy,
    oldBlob,
    touchWrapper,
    audioPlayer,
    isConnectAudio,
    imgWidth,
    imgHeight,
    loading,
    directionStatus,
    screenshotList,
    appList,
    refreshAppList,
    fileList,
    downloadPercent,
    curUploadingUUID,
    curUploadingName,
    installStatus,
    installPercent,
    curInstallingApp,
    frontendUploadTime,
    setFrontendUploadTime,
    clearFrontendUploadTime,
    terminalWebsocket,
    terminalWebsocketSend,
    webSocketParams,
    setOtherWebSocketParams,
    closeWebsocket,
    openSocket,
    screenMode,
    changeScreenMode,
    remoteAdbLoading,
    remoteAdbUrl,
    driverLoading,
    isDriverFinish,
    pasteText,
    remoteSIBPort,
    remoteWDAPort,
    recentApps,
    getRecentApps,
    isUserClosed,
    isAgentUpdate,
    isUserStopUpload,
  };
});
