{"$schema": "https://raw.githubusercontent.com/streetsidesoftware/cspell/main/cspell.schema.json", "version": "0.2", "language": "en,en-US", "allowCompoundWords": true, "words": ["ahooks", "<PERSON><PERSON><PERSON><PERSON>", "antd", "ant<PERSON>", "antv", "authed", "brotli", "clsx", "cropperjs", "defu", "DEVGUARD", "esno", "fastify", "feishu", "forgeon", "foui", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "hypergryph", "iconify", "immer", "jedec", "jmuxer", "lttb", "mnemonist", "mockjs", "Muxer", "navigations", "OSTYPE", "protos", "qrcode", "Scrcpy", "siri", "skland", "sortablejs", "svrash", "<PERSON><PERSON><PERSON>", "tsbuildinfo", "turborepo", "UNCATEGORIZED", "unocss", "unsampled", "vditor", "VITE", "vueuse", "zustand", "zxcvbn"], "ignorePaths": ["**/node_modules/**", "**/dist/**", "**/*-dist/**", "**/icons/**", "pnpm-lock.yaml", "**/*.log", "**/es/**"]}