import { createMicroAppCtx } from '../../composables/index';
import type { PlatformRoutePath } from '../route-path';
import { MicroAppEventBusEvent } from './types';

/**
 * 主路由导航
 */
export const useRouteNavigationCtx = createMicroAppCtx<{
  /**
   * 未授权时跳转
   */
  onUnauthorized: (msg?: string) => void;
  /**
   * 禁止访问时跳转
   */
  onForbidden: (msg?: string) => void;

  /**
   * 主应用路由跳转能力
   */
  onRouteNavigate: (targetPath: PlatformRoutePath, params?: Record<string, any>, query?: Record<string, any>, options?: {
    /**
     * 是否在新标签页打开
     */
    openInNewTab?: boolean;
  }) => void;
}>(MicroAppEventBusEvent.RouteNavigation);
