<template>
  <div v-track:v="'brogmttdpi'" :class="prefixCls">
    <div class="mb-4 text-center text-lg font-bold">
      回收站
    </div>
    <div class="flex items-start">
      <div class="w-[240px] rounded-[8px] bg-FO-Container-Fill2 p-4">
        <AEmpty v-if="!packageListWithPkg?.length" :image="emptyImg" description="暂无分支数据" />
        <template v-if="packageListWithPkg?.length === 1 && packageListWithPkg[0].is_default">
          <div
            v-for="pkg in packageListWithPkg[0].game_packages" :key="pkg.ID" v-track="'io7vpmwrbw'" :class="`${prefixCls}__left-tab`"
            :active="activeTab === pkg.ID" @click="handleTabChange(pkg)"
          >
            <img :src="preprocessFilePath(pkg.icon)" alt="" :class="`${prefixCls}__left-tab-icon`">

            <ATypographyText
              :class="`${prefixCls}__left-tab-title`" :ellipsis="{ tooltip: true }"
              :content="pkg.briefName || pkg.name"
            />
          </div>
        </template>
        <template v-else>
          <a-collapse v-model:activeKey="activeKey" expandIconPosition="end" ghost :class="`${prefixCls}__packageListWithPkg-collapse`">
            <template #expandIcon="{ isActive }">
              <Icon icon="arrow-down|svg" size="8" class="collapse-arrow" :class="!isActive ? 'transform-rotate-z--90deg' : 'transform-rotate-z-0deg'" />
            </template>
            <a-collapse-panel v-for="WithPkg in packageListWithPkg" :key="WithPkg.ID">
              <template #header>
                <span class="font-bold">
                  {{ WithPkg.name }}
                </span>
              </template>
              <div
                v-for="pkg in WithPkg.game_packages" :key="pkg.ID" :class="`${prefixCls}__left-tab`"
                :active="activeTab === pkg.ID" @click="handleTabChange(pkg)"
              >
                <img :src="pkg.icon" alt="" :class="`${prefixCls}__left-tab-icon`">

                <ATypographyText
                  :class="`${prefixCls}__left-tab-title`" :ellipsis="{ tooltip: true }"
                  :content="pkg.briefName || pkg.name"
                />
              </div>
            </a-collapse-panel>
          </a-collapse>
        </template>
      </div>
      <div class="m-4 flex-1 px-4">
        <CardList v-bind="getCardListProps" ref="cardListRef" @success="handleSuccess" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  Empty as AEmpty,
  TypographyText as ATypographyText,
} from 'ant-design-vue';
import { preprocessFilePath } from '@hg-tech/oasis-common';
import { computed, nextTick, onBeforeMount, ref, watch } from 'vue';
import type {
  GamePackagesListItem,
  PkgClassWithpkgListItem,
} from '/@/api/page/model/testModel';
import { getGamePackagesRecyclePkgsVersionsListByPage } from '/@/api/page/test';
import { useDesign } from '/@/hooks/web/useDesign';
import { useUserStoreWithOut } from '/@/store/modules/user';
import CardList from '/@/views/test/gamePackage/list/CardList.vue';
import { getAllPaginationList } from '/@/hooks/web/usePagination';
import Icon from '/@/components/Icon';

const emit = defineEmits(['success']);
const { prefixCls } = useDesign('game-package-recycle-bin');
const userStore = useUserStoreWithOut();
const emptyImg = AEmpty.PRESENTED_IMAGE_SIMPLE;

const packageListWithPkg = ref<PkgClassWithpkgListItem[]>([]);
const activeTab = ref<number>();
const cardListRef = ref<InstanceType<typeof CardList>>();
const activeKey = ref<number[]>();
const getCardListProps = computed(() => ({
  pkgID: activeTab.value,
  packageList: packageListWithPkg.value.length
    ? packageListWithPkg.value?.map((item) => item.game_packages).reduce((a, b) => {
      return a.concat(b);
    })
    : [],
  isRecycle: true,
}));

async function getPackageList(isMount: boolean) {
  if (!userStore.getProjectId) {
    return;
  }

  const { list } = await getAllPaginationList((p) => getGamePackagesRecyclePkgsVersionsListByPage(userStore.getProjectId, p));

  if (list?.length > 0) {
    // Type assertion: the API actually returns PkgClassWithpkgListItem[] but is typed incorrectly
    const typedList = list as unknown as PkgClassWithpkgListItem[];
    packageListWithPkg.value = typedList;
    activeKey.value = typedList.map((item) => item.ID).filter((id): id is number => id !== undefined);

    const firstPackageID = typedList[0]?.game_packages?.[0]?.ID;

    const isActiveTabValid = isMount
      ? typedList.some((e) => e.ID === activeTab.value)
      : typedList[0]?.game_packages?.some((e: GamePackagesListItem) => e.ID === activeTab.value);

    if (!isActiveTabValid && firstPackageID) {
      activeTab.value = firstPackageID;
    }
  } else {
    packageListWithPkg.value = [];
    activeKey.value = [];
  }
}

async function init(isMount: boolean = false) {
  await getPackageList(isMount);
  nextTick(() => {
    cardListRef.value?.getList();
  });
}

onBeforeMount(() => {
  init(true);
});

function handleTabChange(pkg: GamePackagesListItem) {
  if (pkg.ID !== activeTab.value) {
    activeTab.value = pkg.ID;
    nextTick(() => {
      cardListRef.value?.getList();
    });
  }
}

function handleSuccess() {
  emit('success');
  init();
}

watch(
  () => userStore.getProjectId,
  () => {
    init();
  },
);
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-game-package-recycle-bin';

.@{prefix-cls} {
  background: @FO-Container-Fill1;
  padding: 16px;
  border-radius: 8px;
  &__packageListWithPkg-collapse {
    .ant-collapse-header {
      align-items: center;
      padding: 0 !important;
    }
    .ant-collapse-header-text {
      margin-inline-end: 0 !important;
      flex: none !important;
    }
    .ant-collapse-expand-icon {
      padding-left: 5px !important;
    }
  }
  &__left {
    &-tab {
      cursor: pointer;
      user-select: none;
      margin: 5px 0;
      padding: 10px;
      position: relative;
      display: flex;
      align-items: center;

      &[disabled='true'] {
        cursor: not-allowed;
        background-color: transparent !important;
      }

      &[active='true'],
      &:hover {
        background-color: @FO-Container-Fill1;
        border-radius: 20px;
      }

      &-icon {
        width: 20px;
        height: 20px;

        border-radius: 6px;
      }

      &-title {
        width: 150px;
        margin-left: 4px;
        font-weight: bold;
      }
    }
  }
  .collapse-arrow {
    transition: all 0.1s;
  }
}
</style>
