import { type PropType, computed, defineComponent, onBeforeUnmount, ref } from 'vue';
import { ResizeItem } from '../ResizeItem';

const RangeSlider = defineComponent({
  props: {
    value: {
      type: Array as PropType<Array<number>>,
      required: true,
    },
    min: {
      type: Number as PropType<number>,
      default: 0,
    },
    max: {
      type: Number as PropType<number>,
      default: 100,
    },
    precision: {
      type: Number as PropType<number>,
      default: 1,
    },
    placeholder: {
      type: String as PropType<string>,
      default: '可拖拽的范围',
    },
    onUpdateValue: {
      type: Function as PropType<(value: [number, number]) => void>,
    },
    immediate: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    /**
     * 滑块的最小宽度，单位为像素（会影响调整范围）
     */
    minWidth: {
      type: Number as PropType<number>,
      default: 40,
    },
  },
  setup(props, { slots }) {
    const containerRef = ref<HTMLElement | null>(null);
    // dragging: 用于拖拽整个范围
    // resizing: 用于调整开始或结束边界
    const dragging = ref<{ startX: number; origStart: number; origEnd: number } | null>(null);
    const resizing = ref<{ edge: 'start' | 'end'; startX: number; origStart: number; origEnd: number } | null>(null);
    // 用于在拖拽或调整过程中更新显示的范围
    const tempInfo = ref<{ start: number; end: number } | null>(null);
    const range = computed(() => props.max - props.min);
    // 计算当前值在容器中的百分比位置
    const scaleX = (val: number) => ((val - props.min) / (props.max - props.min)) * 100;

    // 计算最小宽度对应的百分比
    const minWidthRange = computed(() => {
      if (!containerRef.value) {
        return;
      }
      const containerWidth = containerRef.value.offsetWidth;
      const percent = (props.minWidth / containerWidth) * 100;
      return Math.floor((percent / 100) * range.value);
    });

    const sliderStyle = computed(() => {
      const temp = tempInfo.value;
      const start = temp?.start ?? props.value[0];
      const end = temp?.end ?? props.value[1];
      const x = scaleX(start);
      const width = scaleX(end) - x;

      return {
        left: `${scaleX(start)}%`,
        width: `${width}%`,
        position: 'absolute',
        top: '0',
        height: '100%',
        borderRadius: '4px',
        cursor: 'grab',
      };
    });

    const onMouseMove = (e: MouseEvent) => {
      if (!containerRef.value) {
        return;
      }
      const rect = containerRef.value.getBoundingClientRect();
      const deltaPx = e.clientX - (dragging.value?.startX ?? resizing.value?.startX ?? 0);
      const deltaPercent = (deltaPx / rect.width) * 100;
      const deltaCoord = (deltaPercent / 100) * range.value;

      if (dragging.value) {
        const width = dragging.value.origEnd - dragging.value.origStart;
        let newStart = dragging.value.origStart + deltaCoord;
        let newEnd = newStart + width;

        // 限制拖拽不能超过最大最小范围
        if (newStart < props.min) {
          newStart = props.min;
          newEnd = newStart + width;
        }
        if (newEnd > props.max) {
          newEnd = props.max;
          newStart = newEnd - width;
        }

        tempInfo.value = { start: newStart, end: newEnd };
      }

      if (resizing.value) {
        const { edge, origStart, origEnd } = resizing.value;
        let newStart = origStart;
        let newEnd = origEnd;

        if (edge === 'start') {
          newStart = Math.min(origEnd, Math.max(props.min, origStart + deltaCoord));
        } else {
          newEnd = Math.max(origStart, Math.min(props.max, origEnd + deltaCoord));
        }
        if (minWidthRange.value && newEnd - newStart < minWidthRange.value) {
          if (edge === 'start') {
            newStart = newEnd - minWidthRange.value;
          } else {
            newEnd = newStart + minWidthRange.value;
          }
        }
        tempInfo.value = { start: newStart, end: newEnd };
      }
      props.immediate && tempInfo.value && props.onUpdateValue?.([tempInfo.value.start, tempInfo.value.end]);
    };

    const onMouseUp = () => {
      if (tempInfo.value && props.onUpdateValue) {
        props.onUpdateValue([tempInfo.value.start, tempInfo.value.end]);
      }
      dragging.value = null;
      resizing.value = null;
      tempInfo.value = null;
      window.removeEventListener('mousemove', onMouseMove);
      window.removeEventListener('mouseup', onMouseUp);
    };

    const onMouseDown = (event: MouseEvent) => {
      const [start, end] = props.value;
      dragging.value = {
        startX: event.clientX,
        origStart: start,
        origEnd: end,
      };
      window.addEventListener('mousemove', onMouseMove);
      window.addEventListener('mouseup', onMouseUp);
    };
    const onResizeStart = (event: MouseEvent, edge: 'start' | 'end') => {
      const [start, end] = props.value;
      resizing.value = {
        edge,
        startX: event.clientX,
        origStart: start,
        origEnd: end,
      };
      window.addEventListener('mousemove', onMouseMove);
      window.addEventListener('mouseup', onMouseUp);
    };

    onBeforeUnmount(() => {
      window.removeEventListener('mousemove', onMouseMove);
      window.removeEventListener('mouseup', onMouseUp);
    });

    return () => (
      <div class="range-slider pos-relative h-40px w-full bg-FO-Container-Fill1" ref={containerRef}>
        <ResizeItem
          containerStyle={sliderStyle.value}
          onMouseDown={onMouseDown}
          onResizeStart={onResizeStart}
          resizeable
        >
          {{
            default: () => (slots.default
              ? slots.default()
              : (
                <div class="h-full w-full flex items-center justify-center text-center c-FO-Content-Text2">
                  {props.placeholder}
                </div>
              )),
            prefix: slots.prefix ? () => slots.prefix : null,
            suffix: slots.suffix ? () => slots.suffix : null,
          }}
        </ResizeItem>
      </div>
    );
  },
});

export { RangeSlider };
