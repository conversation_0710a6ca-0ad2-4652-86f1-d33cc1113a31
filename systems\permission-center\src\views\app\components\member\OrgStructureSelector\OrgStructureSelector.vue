<template>
  <div class="min-h-320px flex py-12px">
    <div class="flex flex-1 flex-col gap-12px overflow-hidden">
      <div class="pr-16px">
        <Input
          v-model:value="searchKwInput" class="flex-none"
          :placeholder="`搜索${schemas.filter((schema) => schema.searchFunc).map(i => i.title).join('、')}`"
          :allowClear="true"
        >
          <template #prefix>
            <SearchOutlined />
          </template>
        </Input>
      </div>
      <ToSelectList
        v-model:selectedPayloads="selectedPayloads" class="flex-auto" :searchKw="searchKw"
        :schemas="schemas" :disabledKeys="disabledKeys" :showCheckedWhenDisabled="showCheckedWhenDisabled"
      />
    </div>
    <div class="mr-16px w-1px bg-FO-Container-Stroke1" />
    <div class="flex flex-1 flex-col gap-12px overflow-hidden">
      <div class="title-14-bold flex items-center justify-between">
        <div>
          <span class="mr-8px">
            已选
          </span>
          <a class="c-FO-Brand-Primary-Default hover:c-FO-Brand-Primary-Hover" @click="handleClear">
            清空
          </a>
        </div>
        <div class="flex justify-between c-FO-Content-Text3">
          <template v-for="(g, idx) in visibleSchemas" :key="g.type">
            <span v-if="idx > 0" class="mx-4px my-5px w-1px bg-FO-Container-Stroke1" />
            {{ g.summary(getMergedSchemaCount(g)) }}
          </template>
        </div>
      </div>
      <div class="flex flex-col gap-4px overflow-hidden">
        <OrgStructureSelectorCell
          v-for="i in selectedPayloads" :key="i.data.key" :data="i.data"
          class="bg-FO-Container-Fill2"
        >
          <template #suffix>
            <Button class="btn-fill-text" @click="() => handleRemoveCell(i)">
              <template #icon>
                <CloseOutlined />
              </template>
            </Button>
          </template>
        </OrgStructureSelectorCell>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" generic="K extends string, T extends OrgStructureCellInfo<K>">
import { Button, Input } from 'ant-design-vue';
import { CloseOutlined, SearchOutlined } from '@ant-design/icons-vue';
import OrgStructureSelectorCell from './OrgStructureSelectorCell.vue';
import type { OrgStructureCellInfo, OrgStructurePayload, OrgStructureSchema } from './type.ts';
import { computed, ref } from 'vue';
import ToSelectList from './ToSelectList.vue';
import { debouncedRef } from '@vueuse/core';
import { countBy, reject, sumBy } from 'lodash';

const props = withDefaults(defineProps<{
  schemas: OrgStructureSchema<K, T>[];
  disabledKeys?: OrgStructureCellInfo<K>['key'][];
  /** 禁用时显示为已勾选 */
  showCheckedWhenDisabled?: boolean;
}>(), {
  disabledKeys: () => [],
  showCheckedWhenDisabled: false,
});

defineEmits<{
  (e: 'updated', payloads: OrgStructurePayload<K, T>[]): void;
}>();

const selectedPayloads = defineModel<OrgStructurePayload<K, T>[]>('value', { required: true });
const searchKwInput = ref('');
const searchKw = debouncedRef(searchKwInput, 200);

// 过滤掉在统计中隐藏的schema
const visibleSchemas = computed(() => {
  return reject(props.schemas, 'summaryMergeWith');
});

// 缓存所有类型的统计数量，避免重复计算
const schemaCountMap = computed(() => {
  return countBy(selectedPayloads.value, (i) => i?.data?.type);
});

// 缓存合并后的统计数量
const mergedSchemaCountMap = computed(() => {
  const countMap = schemaCountMap.value;
  const mergedMap: Record<K, number> = {} as Record<K, number>;

  // 为每个schema计算合并后的数量
  for (const schema of props.schemas) {
    const baseCount = countMap[schema.type] || 0;

    // 查找需要合并到当前schema的其他schema数量
    const mergedCount = sumBy(
      props.schemas,
      (s) => (s.summaryMergeWith === schema.type ? (countMap[s.type] || 0) : 0),
    );

    mergedMap[schema.type] = baseCount + mergedCount;
  }

  return mergedMap;
});

function getMergedSchemaCount(targetSchema: OrgStructureSchema<K, T>): number {
  return mergedSchemaCountMap.value[targetSchema.type] || 0;
}

function handleClear() {
  selectedPayloads.value = [];
}

function handleRemoveCell(cell: OrgStructurePayload<K, T>) {
  selectedPayloads.value = selectedPayloads.value.filter((i) => i.data.key !== cell.data.key);
}
</script>
