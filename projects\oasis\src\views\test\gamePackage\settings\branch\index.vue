<template>
  <div v-track:v="'idzmwym4ek'" :class="prefixCls">
    <div class="mb-4 flex items-center justify-between">
      <div />
      <div class="text-center text-lg font-bold">
        分支配置
      </div>
      <div class="cursor-pointer b-1 b-#4d4d4d b-rd-20px px-5px py-3px c-#4d4d4d" @click="handleDistributeClick">
        分类配置
      </div>
    </div>

    <div
      :style="{ maxHeight: `calc(100vh - 152px - ${headerHeightRef}px)` }"
      :class="`${prefixCls}__list`"
    >
      <div v-for="gamePackages in branchListWithpkg" :key="gamePackages.ID">
        <div class="mb font-bold">
          {{ gamePackages.name }}({{ gamePackages.game_packages.length }})
        </div>
        <div :class="`${prefixCls}__list-${gamePackages.ID}`">
          <div v-for="branch in gamePackages.game_packages" :key="branch.ID" :class="`${prefixCls}__list-item`">
            <Icon
              icon="ic:round-drag-handle"
              class="c-FO-Content-Text2 mr-2 cursor-grab"
              :class="`${prefixCls}__drag-btn`"
            />
            <img :src="branch.icon" alt="" class="ml-2px inline-block w-64px rounded-lg">
            <div class="mx-3 w-0 flex-1">
              <div>
                <span class="c-FO-Content-Text1 font-bold">{{ branch.name }}</span>
                <span class="c-FO-Content-Text2 ml-2 text-xs">ID: {{ branch.ID }}</span>
              </div>
              <div class="my-1 flex flex-wrap items-center">
                <div v-for="pID in branch.platforms" :key="pID" :class="`${prefixCls}__list-item-tag`">
                  {{ getPlatformName(pID) }}
                </div>
                <div v-if="branch.bundleID" class="mr-3 break-all text-xs">
                  Bundle ID: {{ branch.bundleID }}
                </div>
                <div v-if="branch.applicationId" class="break-all text-xs">
                  Application ID: {{ branch.applicationId }}
                </div>
              </div>
              <div class="flex flex-wrap items-center text-xs">
                <div
                  class="mr-3 min-w-200px"
                  :class="{ 'c-FO-Content-Text2': !hasNotify(branch.notifyV3?.version) }"
                >
                  发版通知: {{ getCurNotify(branch.notifyV3?.version).first }}
                  <span class="c-FO-Content-Text2 mr-1">
                    {{ getCurNotify(branch.notifyV3?.version).extra }}
                  </span>
                  <template v-if="branch.notifyV3?.version?.receiverType === 2">
                    {{ getCurNotify(branch.notifyV3?.version, true).first }}
                    <span class="c-FO-Content-Text2">
                      {{ getCurNotify(branch.notifyV3?.version, true).extra }}
                    </span>
                  </template>
                </div>
                <div :class="{ 'c-FO-Content-Text2': !hasNotify(branch.notifyV3?.doctor) }">
                  包体检测通知: {{ getCurNotify(branch.notifyV3?.doctor).first }}
                  <span class="c-FO-Content-Text2 mr-1">
                    {{ getCurNotify(branch.notifyV3?.doctor).extra }}
                  </span>
                  <template v-if="branch.notifyV3?.doctor?.receiverType === 2">
                    {{ getCurNotify(branch.notifyV3?.doctor, true).first }}
                    <span class="c-FO-Content-Text2">
                      {{ getCurNotify(branch.notifyV3?.doctor, true).extra }}
                    </span>
                  </template>
                </div>
              </div>
            </div>
            <div class="flex flex-wrap items-center">
              <a-button
                v-track="'cykrjoxeiz'"
                size="small"
                shape="round"
                preIcon="mdi:text-box-edit-outline"
                :class="`${prefixCls}__list-item-btn`"
                @click="handleEdit(branch)"
              >
                <span class="!ml-1">编辑分支</span>
              </a-button>
              <a-button
                size="small"
                shape="round"
                preIcon="ant-design:copy-outlined"
                :class="`${prefixCls}__list-item-btn`"
                @click="handleCopy(branch)"
              >
                <span class="!ml-1">复制一个</span>
              </a-button>
              <APopconfirm title="确定要删除吗" @confirm="handleDelete(branch)">
                <a-button
                  size="small"
                  shape="round"
                  :class="`${prefixCls}__list-item-btn`"
                  preIcon="icon-park-outline:delete"
                >
                  <span class="!ml-1">删除</span>
                </a-button>
              </APopconfirm>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-track="'7depkoglyn'" :class="`${prefixCls}__add`" @click="handleCreate()">
      <Icon icon="ant-design:plus-outlined" :size="30" />
    </div>
    <BranchesDrawer @register="registerDrawer" @success="handleSuccess" />
    <DistributeConfigModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" setup name="GamePackageBranches">
import { Popconfirm as APopconfirm } from 'ant-design-vue';
import { nextTick, onBeforeMount, ref, watch } from 'vue';
import BranchesDrawer from './BranchesDrawer.vue';
import type { FeishuChatListItem } from '/@/api/page/model/systemModel';
import type { GamePackagesListNotifyItem, PkgClassWithpkgListItem } from '/@/api/page/model/testModel';
import { getAppChatList } from '/@/api/page/system';
import {
  deleteGamePackage,
  getPkgClassListWithPkg,
  sortGamePkgs,
} from '/@/api/page/test';
import { useModal } from '/@/components/Modal';
import { useDrawer } from '/@/components/Drawer';
import Icon from '/@/components/Icon';
import { useTrack } from '/@/hooks/system/useTrack';
import { useUserList } from '/@/hooks/system/useUserList';
import { useDesign } from '/@/hooks/web/useDesign';
import { useSortable } from '/@/hooks/web/useSortable';
import { useLayoutHeight } from '/@/layouts/default/content/useContentViewHeight';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { isNullOrUnDef } from '/@/utils/is';
import { getPlatformName } from '/@/views/test/gamePackage/settings/settings.data';
import { getAllPaginationList } from '/@/hooks/web/usePagination';
import DistributeConfigModal from './DistributeConfigModal.vue';

const { prefixCls } = useDesign('game-package-branches');
const [registerDrawer, { openDrawer }] = useDrawer();
const [registerModal, { openModal }] = useModal();
const userStore = useUserStoreWithOut();
const { headerHeightRef } = useLayoutHeight();
const { setTrack } = useTrack();
const branchListWithpkg = ref<PkgClassWithpkgListItem[]>([]);
const { getUserList, getNickNameByFieldName } = useUserList();

async function getList() {
  if (!userStore.getProjectId) {
    return;
  }

  const { list } = await getAllPaginationList((p) => getPkgClassListWithPkg(userStore.getProjectId, { ...p, showAll: true }));

  branchListWithpkg.value = list || [];
}

const chatList = ref<FeishuChatListItem[]>([]);

async function getLarkChatList() {
  const { list } = await getAppChatList({ robot: 'tech' });

  chatList.value = list || [];
}

function getCurNotify(notify?: GamePackagesListNotifyItem, isMembers = false) {
  if (!notify) {
    return { first: '不发送', extra: '' };
  }

  switch (notify?.receiverType) {
    case 2:
    {
      const hasNotify = notify.private?.memberIDs?.length || notify.private?.groups?.length;

      if (isMembers) {
        const curMemberNickName = getNickNameByFieldName(notify.private?.memberIDs?.[0], 'ID');

        return {
          first: curMemberNickName,
          extra:
              (notify.private?.memberIDs?.length || 0) > 1
                ? `+${notify.private!.memberIDs!.length - 1}`
                : '',
        };
      } else {
        const curGroup = notify.private?.groups?.[0];

        return {
          first: curGroup || (hasNotify ? '' : '不发送'),
          extra:
              (notify.private?.groups?.length || 0) > 1
                ? `+${notify.private!.groups!.length - 1}`
                : '',
        };
      }
    }

    default:
    {
      const curChat = chatList.value.find((e) => e.chat_id === notify.chatIDs?.[0]);

      return {
        first: curChat?.name || '不发送',
        extra: (notify.chatIDs?.length || 0) > 1 ? `+${notify.chatIDs!.length - 1}` : '',
      };
    }
  }
}

function hasNotify(notify?: GamePackagesListNotifyItem) {
  switch (notify?.receiverType) {
    case 1:
      return notify?.chatIDs?.length;
    case 2:
      return notify?.private?.memberIDs?.length || notify?.private?.groups?.length;
  }
}

// 初始化拖拽
function initDrag() {
  nextTick(() => {
    branchListWithpkg.value.forEach((item, index) => {
      const el = document.querySelector(`.${prefixCls}__list-${item.ID}`) as HTMLElement;
      const { initSortable } = useSortable(el, {
        handle: `.${prefixCls}__drag-btn`,
        animation: 150,
        onEnd: async ({ oldIndex, newIndex }) => {
          if (isNullOrUnDef(oldIndex) || isNullOrUnDef(newIndex) || oldIndex === newIndex) {
            return;
          }

          const branchItem = branchListWithpkg.value[index].game_packages;
          const currentGroup = branchItem[oldIndex];

          branchItem.splice(oldIndex, 1);
          branchItem.splice(newIndex, 0, currentGroup);

          await sortGamePkgs(userStore.getProjectId, {
            classID: branchListWithpkg.value[index].ID!,
            idList: branchItem.map((e) => e.ID!),
          });

          await getList();
        },
      });

      initSortable();
    });
  });
}

onBeforeMount(async () => {
  getLarkChatList();
  await getList();
  await getUserList();
  initDrag();
});

function handleCreate() {
  openDrawer(true, {
    isUpdate: false,
    chatList: chatList.value,
    maxSort: branchListWithpkg.value.length,
  });
}

function handleEdit(record: Recordable) {
  openDrawer(true, {
    record,
    isUpdate: true,
    chatList: chatList.value,
  });
}

function handleCopy(record: Recordable) {
  openDrawer(true, {
    record,
    isUpdate: false,
    isCopy: true,
    chatList: chatList.value,
    maxSort: branchListWithpkg.value.length,
  });
}

async function handleDelete(record: Recordable) {
  await deleteGamePackage(userStore.getProjectId, record.ID);
  setTrack('ixqf5ymwlx');
  handleSuccess();
}

function handleDistributeClick() {
  openModal(true, {});
}

function handleSuccess() {
  getList(true);
}

watch(
  () => userStore.getProjectId,
  () => {
    getList();
  },
);
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-game-package-branches';
.@{prefix-cls} {
  position: relative;
  padding: 16px;
  border-radius: 8px;
  background-color: @FO-Container-Fill1;

  &__add {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    transition: all 0.3s ease-in-out;
    border-radius: 8px;
    background-color: @member-card-background;
    cursor: pointer;
    user-select: none;

    &:hover {
      filter: brightness(0.9);
    }
  }

  &__list {
    overflow: auto;

    &-item {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      padding: 8px;
      transition: all 0.3s ease-in-out;
      border-radius: 8px;
      background-color: @member-card-background;
      cursor: pointer;
      user-select: none;

      &:hover {
        filter: brightness(0.9);
      }

      &-tag {
        margin-right: 4px;
        padding: 0 2px;
        transform: scale(0.9);
        transform-origin: left center;
        border: 1px solid @FO-Content-Text1;
        border-radius: 6px;
        font-size: 12px;
      }

      &-btn {
        margin-right: 8px;
        border-color: #414141 !important;
        background-color: #414141 !important;
        color: #fff !important;

        &:hover {
          border-color: #616161 !important;
          background-color: #616161 !important;
        }
      }
    }
  }
}
</style>
