<template>
  <div class="resource-check-old">
    <div :class="prefixCls">
      <div :class="`${prefixCls}__header !mb-5`">
        <LineTab
          :tabList="menuList"
          :defaultActiveTab="curActiveTab"
          tabMargin="36px"
          @change="handleChangeTab"
        />
        <div class="ml-8">
          <slot name="Introduction" />
        </div>
      </div>
      <div :class="`${prefixCls}__body`">
        <slot />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup name="ResourceCheckTab">
import { computed, ref } from 'vue';
import LineTab from '/@/components/LineTab';
import { useDesign } from '/@/hooks/web/useDesign';
import { useGo } from '/@/hooks/web/usePage';
import { checkPermissionPass, PermissionPoint, PlatformEnterPoint } from '@hg-tech/oasis-common';
import { usePermissionInfo } from '../../service/permission/usePermission.ts';

const props = defineProps({
  curChildTab: {
    type: String,
    default: '',
  },
});

const TAB_LIST = [
  {
    name: PlatformEnterPoint.ResourceCheckSwitchesOld,
    icon: 'icon-park-outline:switch-button',
    title: '功能开关【旧】',
    permissionDeclaration: {
      any: [PermissionPoint.ResourceCheckSwitchesOld],
    },
  },
  {
    name: PlatformEnterPoint.ResourceCheckRulesOld,
    icon: 'carbon:rule-draft',
    title: '命名规范【旧】',
    permissionDeclaration: {
      any: [PermissionPoint.ResourceCheckRulesOld],
    },
  },
  {
    name: PlatformEnterPoint.ResourceCheckIndexOld,
    icon: 'bi:file-earmark-check',
    title: '资源检查规则【旧】',
    permissionDeclaration: {
      any: [PermissionPoint.ResourceCheckIndexOld],
    },
  },
];

const { prefixCls } = useDesign('resource-check-tab');
const { permissionInfo } = usePermissionInfo();
const go = useGo();
const menuList = computed(() => {
  return TAB_LIST.filter((item) => checkPermissionPass(item.permissionDeclaration, permissionInfo.value));
});
const curActiveTab = ref(props.curChildTab || menuList.value[0]?.name);

function handleChangeTab(tabName: string) {
  go({ name: tabName });
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-resource-check-tab';
.@{prefix-cls} {
  padding: 16px;

  &__header {
    display: flex;
    align-items: center;
    padding: 24px;
    border-radius: 8px;
    background-color: @FO-Container-Fill1;
  }
}
</style>
