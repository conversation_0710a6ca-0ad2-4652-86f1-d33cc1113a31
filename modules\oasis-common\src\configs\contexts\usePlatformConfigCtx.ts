import type { ForgeonTheme } from '@hg-tech/forgeon-style';
import { createMicroAppCtx } from '../../composables/index';
import { MicroAppEventBusEvent } from './types';
import type { UserInfoModel } from 'src/models';

export interface ProjectListItem {
  name?: string;
  // 代号
  alias?: string;
  projectTypeID?: number;
  // 管理列表
  admins?: UserInfoModel[];
  // 成员总数
  totalMember?: number;
  // 当前用户是否管理员
  isAdmin?: boolean;
  // 引擎类型 1:Unity 2:Unreal
  engineType?: number;
  perforceIds?: number[];
}
/**
 * 主应用公用配置
 */
export const usePlatformConfigCtx = createMicroAppCtx<{
  /**
   * 全局主题
   */
  theme: ForgeonTheme;
  /**
   * 侧边展开状态
   */
  isMenuExpanded: boolean;
  /**
   * 项目列表(基础信息)
   */
  projectList: {
    id: number;
    name: string;
  }[];
  /**
   * 当前项目id
   */
  currentProjectId: number | undefined;
  /**
   * 侧边展开方法
   */
  changeMenuExpendStatus: (expend: boolean) => void;
  /**
   * 设置网页title
   */
  setDocumentTitle: (title: string) => void;
  /**
   * 设置当前项目id
   */
  setCurrentProjectId: (id: number) => void;
  currentProjectInfo: ProjectListItem | undefined;
}>(MicroAppEventBusEvent.PlatformConfig);
