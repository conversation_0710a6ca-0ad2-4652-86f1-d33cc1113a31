import {
  type PermissionDeclaration,
  type PermissionInfo,
  checkPermissionPass,
  filterAuthedMenu,
  ModulesMenuConfig,
} from '@hg-tech/oasis-common';
import { computed, watch } from 'vue';
import { createUseFetchPoolLRU } from '@hg-tech/utils-vue';
import { asyncComputed } from '@vueuse/core';
import { getCommonAuth } from './common.ts';
import { getScopePermissions } from './scopes.ts';
import { useUserStore } from '../../store/modules/user.ts';

const [
  usePermissionInfoRaw,
  {
    get: getPermissionInfo,
    forceGet: forceGetPermissionInfo,
    clearCache: clearPermissionInfoCache,
  },
] = createUseFetchPoolLRU(
  async (projectId?: number): Promise<PermissionInfo> => {
    const userStore = useUserStore();
    if (!userStore.getToken && !userStore.getAccessToken) {
      return {
        common: [],
        scopes: {},
      };
    }
    try {
      const [commonRes, scopesRes] = await Promise.all([
        getCommonAuth(projectId),
        getScopePermissions(projectId),
      ]);

      return {
        common: commonRes,
        scopes: scopesRes,
      };
    } catch (e) {
      console.error(e);
      return {
        common: [],
        scopes: {},
      };
    }
  },
  {
    ttl: 1000 * 60, // 1分钟过期
  },
);
export { clearPermissionInfoCache, forceGetPermissionInfo, getPermissionInfo };
export function usePermissionInfo() {
  const userStore = useUserStore();
  const { data, isLoading } = usePermissionInfoRaw(
    { loadImmediately: false },
    computed(() => userStore.getProjectId),
  );
  watch(
    () => userStore.getToken,
    () => {
      if (userStore.getToken) {
        getPermissionInfo(userStore.getProjectId);
      }
    },
    { immediate: true },
  );
  return {
    permissionInfo: data,
    isLoading,
  };
}

export function usePermissionCheckPoint(permissionDeclare?: PermissionDeclaration | NonNullable<PermissionDeclaration['any']>[number]) {
  const { permissionInfo } = usePermissionInfo();

  const hasPermission = asyncComputed<boolean>(() => {
    const d = typeof permissionDeclare === 'string' ? { any: [permissionDeclare] } : permissionDeclare;
    return checkPermissionPass(d, permissionInfo.value);
  }, false);

  return [hasPermission];
}

export function useAuthedMenu() {
  const { permissionInfo, isLoading } = usePermissionInfo();

  const authedModules = computed(() => {
    return filterAuthedMenu(ModulesMenuConfig, (d) => {
      return checkPermissionPass(d, permissionInfo.value);
    });
  });

  return {
    authedModules,
    loadingMenu: isLoading,
  };
}
