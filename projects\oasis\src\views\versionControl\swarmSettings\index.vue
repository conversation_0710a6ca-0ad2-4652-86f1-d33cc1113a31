<template>
  <PageWrapper :class="prefixCls" headerSticky @back="goBack">
    <template #title>
      <div v-if="streamName" class="flex items-center">
        审查和关注: {{ streamName }}
        <div v-if="reviewProjectID" class="ml-4 flex gap-[8px]">
          <Button
            class="custom-rounded-btn"
            noIcon
            size="small"
            borderColor="black"
            @click="() => handleSwarm(true)"
          >
            审查员及白名单配置
          </Button>
          <Button
            v-track="'jt02rdcixq'"
            class="custom-rounded-btn"
            noIcon
            size="small"
            borderColor="black"
            @click="() => handleReviewUrgent()"
          >
            通知配置
          </Button>
          <Button
            size="small"
            class="custom-rounded-btn"
            borderColor="black"
            @click="() => handleMsg()"
          >
            <Icon
              icon="carbon:dot-mark"
              :class="messageList?.[0]?.chatID ? '!c-FO-Functional-Success1-Default' : '!c-FO-Container-Fill5'"
            />
            Review通知总群
          </Button>
          <Button
            size="small"
            class="custom-rounded-btn"
            borderColor="black"
            @click="() => handleMsg(true)"
          >
            <Icon
              icon="carbon:dot-mark"
              :class="lockConfig?.lockChatID ? '!c-FO-Functional-Success1-Default' : '!c-FO-Container-Fill5'"
            />
            审批通知总群
          </Button>
          <Button
            class="custom-rounded-btn"
            noIcon
            size="small"
            borderColor="black"
            @click="() => handleDeleteSwarm()"
          >
            删除审查和关注
          </Button>
        </div>
      </div>
    </template>
    <div v-if="reviewProjectID && !isLoading" class="w-full flex">
      <div class="max-h-[calc(100vh_-_206px)] w-400px flex flex-none flex-col overflow-auto rounded-md bg-FO-Container-Fill1">
        <div class="flex-none p-[12px]">
          <div class="flex items-center justify-between line-height-[24px]">
            <div class="FO-Font-B14 flex items-center">
              审查组
            </div>
            <Button
              v-track="'hbsxe5olw6'"
              shape="round"
              class="custom-rounded-btn ml-2"
              size="small"
              borderColor="black"
              @click="handleCreateAudit"
            >
              <Icon icon="ant-design:plus-outlined" :size="14" />
              添加
            </Button>
          </div>
          <div class="my-3 text-xs c-FO-Content-Text2">
            配置审查路径和对应的审查员
          </div>
          <Input v-model:value="swarmGroupKw" class="!rounded-100px" placeholder="搜索组名" allowClear>
            <template #prefix>
              <Icon icon="ant-design:search-outlined" />
            </template>
          </Input>
        </div>
        <div v-if="renderSwarmGroupList.length" :class="`${prefixCls}__tab flex-auto px-[12px] pb-[12px]`">
          <template v-for="item in renderSwarmGroupList ">
            <div
              v-if="item.swarmReviewGroup?.ID && item.reviewGroup"
              :key=" item.swarmReviewGroup.ID"
              :class="[`${prefixCls}__tab-item`, {
                [`${prefixCls}__tab-item--active`]: item.swarmReviewGroup.ID === activeGroupID,
              }]"
              @click="() => handleGroupChange(item.swarmReviewGroup!.ID!)"
            >
              <div class="w-200px flex items-center">
                <Icon icon="devGuard-auditor|svg" />
                <EllipsisText class="ml-2 line-height-[16px]">
                  {{ item.reviewGroup.name }}
                </EllipsisText>
              </div>
              <div>
                <Popconfirm
                  :title="`确认${item.reviewGroup.lockStatus === SwarmGroupLockStatus.Locked ? '关闭' : '开启'}审批配置？`"
                  @confirm="() => handleSwitchApproveConfig(item.reviewGroup!.lockStatus !== SwarmGroupLockStatus.Locked, item)"
                >
                  <Button type="text" size="small" @click.stop>
                    <Icon
                      :icon=" item.reviewGroup.lockStatus === SwarmGroupLockStatus.Locked ? 'majesticons:lock-line' : 'majesticons:unlock-open-line' "
                      :class=" item.reviewGroup.lockStatus === SwarmGroupLockStatus.Locked ? '!c-#db851f' : 'text-FO-Content-Text4'"
                    />
                  </Button>
                </Popconfirm>
                <Popconfirm
                  :title="`确认${item.reviewGroup?.isValid ? '关闭' : '开启'}审查配置？`"
                  @confirm="() => handleSwitchAuditConfig(!item.reviewGroup!.isValid, item)"
                >
                  <Button type="text" size="small" @click.stop>
                    <Icon
                      icon="devGuard-auditor-line|svg"
                      :class="item.reviewGroup?.isValid ? '!c-#db851f' : 'text-FO-Content-Text4'"
                    />
                  </Button>
                </Popconfirm>
                <Popconfirm
                  :title="`确认${item.concernGroup?.isValid ? '关闭' : '开启'}关注配置？`"
                  @confirm="() => handleSwitchConcernConfig(!item.concernGroup!.isValid, item)"
                >
                  <Button type="text" size="small" @click.stop>
                    <PreviewOpen class="flex" :class="item.concernGroup?.isValid ? '!c-#db851f' : 'text-FO-Content-Text4'" />
                  </Button>
                </Popconfirm>
                <Popconfirm title="确定要删除该审查组吗" @confirm.stop="() => handleDelete(item)">
                  <Button type="text" size="small" @click.stop>
                    <Icon class="!c-FO-Functional-Error1-Default" icon="ant-design:delete-outlined" />
                  </Button>
                </Popconfirm>
              </div>
            </div>
          </template>
        </div>
        <div v-else class="py-[24px] text-center c-gray">
          无匹配组
        </div>
      </div>
      <div class="ml-4 max-h-[calc(100vh_-_206px)] flex-auto overflow-auto">
        <div v-if="!allSwarmGroupList.length" class="rounded-md bg-FO-Container-Fill1 p-4">
          <Empty :image="emptyImg" description="该分支未配置审查组">
            <Button type="primary" @click="handleCreateAudit">
              新增审查组
            </Button>
          </Empty>
        </div>
        <PreviewPage
          v-else-if="activeGroupID"
          :swarmGroupInfo="activeGroup"
          :groupList="groupList"
          :loading="loadingGroup"
          :allSwarmGroupList="allSwarmGroupList"
          :reviewProjectID="reviewProjectID"
          :chatList="submitChatList"
          @editOrRevert="handleEditOrRevert"
          @success="handleSuccess"
          @switchApprove="(v) => handleSwitchApproveConfig(v, activeGroup!)"
          @switchAudit="(v) => handleSwitchAuditConfig(v, activeGroup!)"
          @switchConcern="(v) => handleSwitchConcernConfig(v, activeGroup!)"
        />
      </div>
    </div>
    <div v-if="!reviewProjectID && !isLoading" class="m-4 rounded-md bg-FO-Container-Fill1 p-4">
      <div class="my-10 w-full flex flex-col items-center justify-center">
        <div class="text-lg c-FO-Content-Text2">
          该分支未配置审查和关注
        </div>
        <div class="mt-6">
          <Button type="primary" :loading="cloneLoading" @click="() => handleSwarm()">
            配置
          </Button>
          <Button class="ml-3" type="primary" :loading="cloneLoading" @click="handleClone">
            克隆其他分支的配置
          </Button>
        </div>
      </div>
    </div>
    <AuditModal @register="registerModal" @success="handleSwarmChange" />
    <MsgModal @register="registerMsgModal" @success="handleSwarmChange" />
    <CloneModal @register="registerCloneModal" @success="copySwarm" />
    <DeleteModal @register="registerDeleteModal" @success="handleSwarmChange" />
    <ReviewUrgentModal @register="registerUrgentModal" />
  </PageWrapper>
</template>

<script lang="ts" setup>
import type { SelectProps } from 'ant-design-vue';
import type { PerforceServersListItem } from '../../../api/page/model/perforceModel';
import {
  type P4StreamLockConfigReviewerItem,
  type SwarmGroupItemInfo,
  type SwarmReviewMessagesListItem,
  type SwarmReviewProjectsListItem,
  SwarmGroupLockStatus,
} from '../../../api/page/model/swarmModel';
import type { FeishuChatListItem } from '../../../api/page/model/systemModel';
import { Button, Empty, Input, Popconfirm } from 'ant-design-vue';
import { computed, onBeforeMount, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import AuditModal from './components/AuditModal.vue';
import CloneModal from './components/CloneModal.vue';
import DeleteModal from './components/DeleteModal.vue';
import MsgModal from './components/MsgModal.vue';
import ReviewUrgentModal from './components/ReviewUrgentModal.vue';
import PreviewPage from './PreviewPage.vue';
import { EllipsisText } from '../../../components/EllipsisText';
import { getStreamsByID } from '../../../api/page/p4';
import { PreviewOpen } from '@icon-park/vue-next';
import { getPerforceGroupsAndLDAPGroupListByPage, getPerforceServersListByPage } from '../../../api/page/perforce';
import {
  addSwarmGroup,
  copyLockConfigs,
  copyP4StreamSwarm,
  copyReviewConfigs,
  deleteSwarmGroup,
  editAuditSwitch,
  getP4StreamLockConfigReviewer,
  getSwarmGroupByID,
  getSwarmGroupListByPage,
  getSwarmReviewMessagesListByPage,
  getSwarmReviewProjectsListByPage,
  switchGroupLockProject,
  updateSwarmReviewGroupsConcernSwitch,
} from '../../../api/page/swarm';
import { getAppChatList } from '../../../api/page/system';
import { Icon } from '../../../components/Icon';
import { useModal } from '../../../components/Modal';
import { PageWrapper } from '../../../components/Page';
import { useTimeoutFn } from '../../../hooks/core/useTimeout';
import { useTrack } from '../../../hooks/system/useTrack';
import { useDesign } from '../../../hooks/web/useDesign';
import { useMessage } from '../../../hooks/web/useMessage';
import { useTabs } from '../../../hooks/web/useTabs';
import { useAppStore } from '../../../store/modules/app';
import { useP4StoreWithOut } from '../../../store/modules/p4';
import { useUserStoreWithOut } from '../../../store/modules/user';
import { useLatestPromise } from '@hg-tech/utils-vue';

const { prefixCls } = useDesign('gitlab-group-tab');
const router = useRouter();
const userStore = useUserStoreWithOut();
const depotID = computed(() => Number(router.currentRoute.value?.params?.id));
const serverID = computed(() => Number(router.currentRoute.value?.query?.sID));
const streamID = computed(() => Number(router.currentRoute.value?.params?.stream_id));

const reviewProjectID = ref<number>();
const streamName = ref<string>('');
const allSwarmGroupList = ref<SwarmGroupItemInfo[]>([]);
const swarmGroupKw = ref('');
const renderSwarmGroupList = computed(() => {
  if (swarmGroupKw.value) {
    return allSwarmGroupList.value.filter((item) => {
      return item.reviewGroup?.name?.toLowerCase()?.includes(swarmGroupKw.value.toLowerCase());
    });
  }
  return allSwarmGroupList.value;
});

const { setTitle } = useTabs();
const { createMessage } = useMessage();
const reviewProjectList = ref<SwarmReviewProjectsListItem[]>([]);
const curReviewProject = ref<SwarmReviewProjectsListItem>();
const { refreshPage } = useTabs();
const p4Store = useP4StoreWithOut();
const emptyImg = Empty.PRESENTED_IMAGE_SIMPLE;
const groupList = ref<SelectProps['options']>([]);
const perforceServersList = ref<PerforceServersListItem[]>([]);
const messageList = ref<SwarmReviewMessagesListItem[]>([]);
const isUpdate = ref<boolean>(false);
const isLoading = ref<boolean>(false);
const [registerModal, { openModal }] = useModal();
const [registerMsgModal, { openModal: openMsgModal }] = useModal();
const [registerCloneModal, { openModal: openCloneModal }] = useModal();
const [registerDeleteModal, { openModal: openDeleteModal }] = useModal();
const [registerUrgentModal, { openModal: openUrgentModal }] = useModal();

const cloneLoading = ref<boolean>(false);
const submitChatList = ref<FeishuChatListItem[]>([]);
const swarmChatList = ref<FeishuChatListItem[]>([]);
const lockConfig = ref<P4StreamLockConfigReviewerItem>();
const appStore = useAppStore();
const { setTrack } = useTrack();

const activeGroupID = ref<NonNullable<SwarmGroupItemInfo['swarmReviewGroup']>['ID']>();
const { data: activeGroup, execute: updateActiveGroup, loading: loadingGroup, reset: resetActiveGroup } = useLatestPromise(async () => {
  if (!userStore.getProjectId || !activeGroupID.value) {
    resetActiveGroup();
    return;
  }
  return getSwarmGroupByID(userStore.getProjectId, activeGroupID.value).then((res) => {
    if ('code' in res && res.code === 7) {
      return undefined;
    }

    return res;
  });
});
watch(activeGroupID, () => updateActiveGroup(), { immediate: true });

// 获取分支信息
async function getStreamDetail() {
  if (!userStore.getProjectId) {
    return;
  }

  const { restream } = await getStreamsByID(userStore.getProjectId, streamID.value);

  streamName.value = restream.description || restream.path || '';
  p4Store.setCurStream(restream);
  // 设置Tab标题
  await setTitle(streamName.value, ' - Swarm审查管理', true);
}

// 获取所有Perforce服务器列表
async function getPerforceServerList() {
  const { list } = await getPerforceServersListByPage({
    page: 1,
    pageSize: 999,
  });

  if (list?.length > 0) {
    perforceServersList.value = list;
    getGroupList();
  } else {
    perforceServersList.value = [];
  }
}

// 获取所有组(包括LDAP)列表
async function getGroupList() {
  const { list } = await getPerforceGroupsAndLDAPGroupListByPage(userStore.getProjectId, {
    page: 1,
    pageSize: 999,
    serverID: serverID.value,
  });

  if (list?.length > 0) {
    groupList.value = list.map((e) => ({ label: e.name!, value: e.name! }));
  } else {
    groupList.value = [];
  }
}

// 获取审查项目列表
async function getSwarmReviewProjectList() {
  const { list } = await getSwarmReviewProjectsListByPage(userStore.getProjectId, {
    page: 1,
    pageSize: 999,
    depotID: depotID.value,
  });

  if (list?.length > 0) {
    reviewProjectList.value = list;
    curReviewProject.value = list.find((e) => e.streamID === streamID.value);
    reviewProjectID.value = curReviewProject.value?.ID;
    return getProjectGroupList();
  } else {
    reviewProjectList.value = [];
    curReviewProject.value = undefined;
    reviewProjectID.value = undefined;
  }
}

async function getLockConfig() {
  const { list } = await getP4StreamLockConfigReviewer(userStore.getProjectId, {
    page: 1,
    pageSize: 1,
    streamID: streamID.value,
  });

  lockConfig.value = list?.[0] || undefined;
}

async function getLarkChatList() {
  await Promise.all([
    getAppChatList({ robot: 'submit' }).then(({ list }) => {
      submitChatList.value = list || [];
    }),
    getAppChatList({ robot: 'swarm' }).then(({ list }) => {
      swarmChatList.value = list || [];
    }),
  ]);
}

// 获取审查通知群列表
async function getSwarmReviewMessagesList() {
  const { list } = await getSwarmReviewMessagesListByPage(userStore.getProjectId, {
    page: 1,
    pageSize: 999,
    streamID: streamID.value,
  });

  messageList.value = list || [];
}

// 获取审查组列表
async function getProjectGroupList() {
  if (!userStore.getProjectId || !streamID.value) {
    return;
  }

  const res = await getSwarmGroupListByPage(userStore.getProjectId, streamID.value) ?? [];

  if ('code' in res && res.code !== 0) {
    return;
  }

  swarmGroupKw.value = '';
  allSwarmGroupList.value = res || [];
  if (allSwarmGroupList.value.every((e) => e.swarmReviewGroup?.ID !== activeGroupID.value)) {
    activeGroupID.value = allSwarmGroupList.value?.[0]?.swarmReviewGroup?.ID;
  }
}

function handleSwarm(isUpdate = false) {
  openModal(true, {
    isUpdate,
    stream: p4Store.getCurStream,
    auditInfo: curReviewProject.value,
    groupList: groupList.value,
    serverID: serverID.value,
  });
}

function handleReviewUrgent() {
  openUrgentModal(true, {});
}

function handleDeleteSwarm() {
  openDeleteModal(true);
}

function handleMsg(isLockGroup = false) {
  openMsgModal(true, {
    stream: p4Store.getCurStream,
    messages: messageList.value,
    isLockGroup,
    chatList: submitChatList.value,
    swarmChatList: swarmChatList.value,
    lockConfig: lockConfig.value,
  });
}

function handleClone() {
  openCloneModal(true, {
    stream: p4Store.getCurStream,
    reviewProjectList: reviewProjectList.value,
  });
}

async function handleCreateAudit() {
  const res = await addSwarmGroup(userStore.getProjectId, {
    name: `未命名-${Math.random().toString(16).slice(2, 7)}`,
    streamId: streamID.value,
  });

  if (res.code === 7) {
    return;
  } else if ('swarmReviewGroupID' in res) {
    activeGroupID.value = Number(res.swarmReviewGroupID) ?? activeGroupID.value;
  }

  setTrack('kmlitu2oig');

  return handleSuccess();
}

function handleEdit() {
  isUpdate.value = true;
}

async function handleDelete(record: SwarmGroupItemInfo) {
  await deleteSwarmGroup(userStore.getProjectId, {
    swarmReviewGroupID: record.swarmReviewGroup?.ID,
    streamID: streamID.value,
  });
  setTrack('yeqxyaxo9t');
  isUpdate.value = false;

  handleSuccess();
}

async function handleSuccess() {
  await getProjectGroupList();
  await updateActiveGroup();
}

function handleEditOrRevert(isEdit: boolean) {
  if (isEdit) {
    handleEdit();
  } else {
    handleSuccess();
  }
}

function handleSwarmChange() {
  getSwarmReviewProjectList();
  getSwarmReviewMessagesList();
  getLockConfig();
}

// 复制审查和关注
async function copySwarm(ID?: number, copyID?: number) {
  if (!ID || !copyID) {
    createMessage.warn('请选择源审查项目');

    return;
  }

  cloneLoading.value = true;

  const fromStreamID = reviewProjectList.value.find((e) => e.ID === copyID)!.streamID!;

  const tasks = [
    () => copyP4StreamSwarm(userStore.getProjectId, {
      fromReviewProjectID: copyID,
      toReviewProjectID: ID,
    }),
    () => copyReviewConfigs(userStore.getProjectId, {
      fromStreamID,
      toStreamID: streamID.value,
    }),
    () => copyLockConfigs(userStore.getProjectId, {
      fromStreamID,
      toStreamID: streamID.value,
    }),
  ];

  for (const task of tasks) {
    const res = await task();

    if (res?.code === 7) {
      cloneLoading.value = false;
      return;
    }
  }

  p4Store.setIsAfterCopy(true);
  createMessage.success('复制成功，页面将在2秒后自动刷新');
  useTimeoutFn(() => {
    cloneLoading.value = false;
    refreshPage();
  }, 2000);
}

function handleGroupChange(id: number) {
  activeGroupID.value = id;
}

async function handleSwitchApproveConfig(enable: boolean, groupItem: SwarmGroupItemInfo) {
  const res = await switchGroupLockProject(userStore.getProjectId, groupItem.reviewGroup?.lockGroup?.ID, {
    switch: enable,
  });

  if (res?.code === 7) {
    return;
  }

  if (enable) {
    createMessage.success(`[${groupItem.reviewGroup?.name}]审批配置已启用`);
  } else {
    createMessage.info(`[${groupItem.reviewGroup?.name}]审批配置已禁用`);
  }

  return handleSuccess();
}
async function handleSwitchConcernConfig(enable: boolean, groupItem: SwarmGroupItemInfo) {
  if (!groupItem.reviewGroup?.lockGroup?.ID) {
    return;
  }
  const res = await updateSwarmReviewGroupsConcernSwitch(userStore.getProjectId!, {
    concernGroupID: groupItem.reviewGroup?.lockGroup?.ID,
    swarmReviewGroupID: groupItem.swarmReviewGroup?.ID,
    streamID: streamID.value,
    concernSwitch: enable,
  });
  if (res?.code === 7) {
    return;
  }

  if (enable) {
    createMessage.success(`[${groupItem.reviewGroup?.name}]关注配置已启用`);
  } else {
    createMessage.info(`[${groupItem.reviewGroup?.name}]关注配置已禁用`);
  }

  return handleSuccess();
}

async function handleSwitchAuditConfig(enable: boolean, groupItem: SwarmGroupItemInfo) {
  if (!groupItem.reviewGroup) {
    return;
  }

  const res = await editAuditSwitch(
    userStore.getProjectId,
    {
      swarmReviewGroupID: groupItem.swarmReviewGroup?.ID,
      streamID: streamID.value,
      reviewSwitch: enable,
    },
  );

  if (res?.code === 7) {
    return;
  }

  if (enable) {
    createMessage.success(`[${groupItem.reviewGroup.name}]审查配置已启用`);
  } else {
    createMessage.info(`[${groupItem.reviewGroup.name}]审查配置已禁用`);
  }

  return handleSuccess();
}

onBeforeMount(async () => {
  appStore.setPageLoadingAction(true);
  isLoading.value = true;
  await Promise.all([
    getStreamDetail(),
    getPerforceServerList(),
    getSwarmReviewMessagesList(),
    getLockConfig(),
    getLarkChatList(),
    getSwarmReviewProjectList(),
  ]);
  isLoading.value = false;
  appStore.setPageLoadingAction(false);
});

// 页面左侧点击返回链接时的操作
function goBack() {
  router.push({ name: 'P4Depots', query: { ID: depotID.value } });
}

watch(
  () => userStore.getProjectId,
  (v, oldValue) => {
    if (v && v !== oldValue) {
      // 切换项目后返回仓库列表
      router.push({ name: 'P4Depots' });
    }
  },
);
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-gitlab-group-tab';
.@{prefix-cls} {
  &__tab {
    overflow: auto;

    &-item {
      display: flex;
      justify-content: space-between;
      margin: 8px 0;
      padding: 8px;
      border: 1px solid transparent;
      border-radius: 6px;
      cursor: pointer;

      &:hover {
        border-color: @FO-Container-Stroke1;
      }

      &--active {
        border-color: @FO-Brand-Primary-Default;
      }
    }
  }
}
</style>
