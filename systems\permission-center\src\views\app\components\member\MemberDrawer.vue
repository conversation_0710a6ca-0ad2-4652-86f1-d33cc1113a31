<template>
  <Drawer
    :width="924" :open="show" title="添加成员" :maskClosable="false" :destroyOnClose="true" :closable="false"
    :centered="true" :bodyStyle="{ paddingTop: '12px', paddingBottom: 0 }" @afterOpenChange="o => !o && modalDestroy()"
    @close="() => modalCancel()"
  >
    <template #title>
      <span class="FO-Font-B18">添加成员</span>
    </template>
    <template #extra>
      <Button class="btn-fill-text" @click="() => modalCancel()">
        <template #icon>
          <CloseIcon />
        </template>
      </Button>
    </template>
    <div class="h-full flex flex-col">
      <OrgStructureSelector
        v-model:value="formValue.selectedPayloads" class="mb-24px flex-auto"
        :schemas="orgSelectorSchemas" :disabledKeys="disabledKeys"
        :showCheckedWhenDisabled="true"
      />
      <FormItem label="自动移出" help="到期后成员会被移出角色列表">
        <DatePicker
          v-model:value="formValue.expireTime" class="w-full" :showTime="true" :showNow="false"
          :disabledDate="disabledDate"
          :disabledTime="disabledTime"
          placeholder="选择日期和时间" :presets="[
            { label: '明天', value: dayjs().endOf('day').add(1, 'day') },
            { label: '一周后', value: dayjs().endOf('day').add(7, 'day') },
            { label: '一个月后', value: dayjs().endOf('day').add(30, 'day') },
            { label: '三个月后', value: dayjs().endOf('day').add(90, 'day') },
            { label: '半年后', value: dayjs().endOf('day').add(180, 'day') },
            { label: '一年后', value: dayjs().endOf('day').add(365, 'day') },
          ]"
        />
      </FormItem>
    </div>
    <template #footer>
      <div class="flex justify-end gap-12px px-8px py-12px">
        <Button class="btn-fill-default w-100px" :disabled="submitting" @click="() => modalCancel()">
          取消
        </Button>
        <Button class="btn-fill-primary w-100px" :loading="submitting" :disabled="!formValue.selectedPayloads?.length" @click="handleConfirm">
          保存
        </Button>
      </div>
    </template>
  </Drawer>
</template>

<script setup lang="ts">
import { h, ref, shallowReactive } from 'vue';
import { Button, DatePicker, Drawer, FormItem, message } from 'ant-design-vue';
import type { ModalBaseProps } from '@hg-tech/utils-vue';
import OrgStructureSelector from './OrgStructureSelector/OrgStructureSelector.vue';
import type { OrgStructureCellInfo, OrgStructurePayload, OrgStructureSchema, OrgStructureSchemaBase } from './OrgStructureSelector/type.ts';
import { getOrgLists, getPmpGroups } from '../../../../api/ldap.ts';
import GroupDefaultAvatar from './assets/group-default-avatar.svg?url';
import OrgDefaultAvatar from './assets/org-default-avatar.svg?url';
import MemberDefaultAvatar from './assets/member-default-avatar.svg?url';
import { queryUserList } from '../../../../api/users.ts';
import dayjs, { type Dayjs } from 'dayjs';
import { type PermissionAppGroupDetail, OrgStructureType } from '../../../../api/group.ts';
import type { PermissionAppInfo } from '../../../../api/app.ts';
import CloseIcon from '../../../../assets/icons/fill-close.svg?component';
import MemberTitleWithTag from './MemberTitleWithTag.vue';
import { formatUserDisplay } from '../../../../composables/useUserInfo.ts';
import { useConcatKey } from '../../../../composables/useConcatKey.ts';

export interface MemberFormData {
  selectedPayloads: OrgStructurePayload<OrgStructureType>[];
  expireTime?: Dayjs;
}

const props = defineProps<ModalBaseProps & {
  appId?: PermissionAppInfo['id'];
  groupId?: PermissionAppGroupDetail['id'];
  disabledKeys?: OrgStructurePayload<OrgStructureType>['key'][];
  sentReq?: (formData: MemberFormData) => Promise<boolean>;
}>();

const submitting = ref(false);
const formValue = shallowReactive({
  selectedPayloads: [] as OrgStructurePayload<OrgStructureType, OrgStructureCellInfo<OrgStructureType>>[],
  expireTime: undefined as Dayjs | undefined,
});

const { getConcatKey, getSecondByKey } = useConcatKey();

function outsourceTitleRender(params: { isOutsource: boolean; title: string; tag?: string }) {
  const { isOutsource, title, tag } = params;
  return isOutsource ? h(MemberTitleWithTag, { title, tag }) : title;
}

const orgSelectorSchemas = [
  {
    type: OrgStructureType.Member,
    title: '人员',
    summary: (i) => `${i}人`,
    async searchFunc(_pageInfo, kw) {
      const res = await queryUserList({ query: kw }, {});
      return {
        total: res.data?.data?.length || 0,
        pageSize: res.data?.data?.length || 0,
        list: (res.data?.data || []).map((i) => ({
          key: i.hgId,
          title: outsourceTitleRender({ isOutsource: Boolean(i.outsourceFlag), title: formatUserDisplay(i), tag: '外包' }),
          subTitle: i.orgPath,
          avatar: i.avatar || MemberDefaultAvatar,
          type: OrgStructureType.Member,
        })),
      };
    },
  } as OrgStructureSchemaBase<OrgStructureType>,
  {
    treeTypeList: [OrgStructureType.Department],
    type: OrgStructureType.Department,
    title: '部门',
    summary: (i) => `${i}部门`,
    async initFunc(_pageInfo, parentId) {
      const res = await getOrgLists({ parent_no: getSecondByKey(parentId) }, {});
      const data = res.data?.data;

      // 处理人员数据
      const memberList = (data?.member || []).map((i) => ({
        key: i.hgId!,
        title: outsourceTitleRender({ isOutsource: Boolean(i.outsourceFlag), title: formatUserDisplay(i) }),
        subTitle: i.orgPath,
        avatar: i.avatar || MemberDefaultAvatar,
        type: OrgStructureType.Member,
      }));

      // 处理子部门数据
      const childList = (data?.child || []).map((i) => ({
        key: getConcatKey(OrgStructureType.Department, i.no),
        title: i.name?.replace(/\([^)]*\)/g, '') || i.name,
        subTitle: i.orgPath,
        avatar: OrgDefaultAvatar,
        type: OrgStructureType.Department,
      }));

      // 合并列表，人员在前
      const combinedList = [...memberList, ...childList];

      return {
        total: combinedList.length,
        pageSize: combinedList.length,
        list: combinedList,
      };
    },
    async searchFunc(pageInfo, kw) {
      const res = await getPmpGroups({
        ...pageInfo,
        query: kw,
        type: OrgStructureType.Department,
      }, {});
      return {
        total: res.data?.data?.total || 0,
        list: (res.data?.data?.list || []).map((i) => ({
          key: getConcatKey(OrgStructureType.Department, i.no),
          title: i.name?.replace(/\([^)]*\)/g, '') || i.name,
          subTitle: i.orgPath,
          avatar: OrgDefaultAvatar,
          type: OrgStructureType.Department,
        })),
      };
    },
    breadcrumbRender: (i) => i?.title,
  } as OrgStructureSchema<OrgStructureType>,
  {
    treeTypeList: [OrgStructureType.OutsourceDepartment],
    type: OrgStructureType.OutsourceDepartment,
    title: '部门(含外包)',
    summary: (i) => `${i}部门(含外包)`,
    summaryMergeWith: OrgStructureType.Department,
    async initFunc(_pageInfo, parentId) {
      const res = await getOrgLists({ parent_no: getSecondByKey(parentId), include_outsource: true }, {});
      const data = res.data?.data;

      // 处理人员数据
      const memberList = (data?.member || []).map((i) => ({
        key: i.hgId!,
        title: outsourceTitleRender({ isOutsource: Boolean(i.outsourceFlag), title: formatUserDisplay(i), tag: '外包' }),
        subTitle: i.orgPath,
        avatar: i.avatar || MemberDefaultAvatar,
        type: OrgStructureType.Member,
      }));

      // 处理子部门数据
      const childList = (data?.child || []).map((i) => ({
        key: getConcatKey(OrgStructureType.OutsourceDepartment, i.no),
        name: i.name?.replace(/\([^)]*\)/g, '') || i.name,
        title: outsourceTitleRender({ isOutsource: true, title: i.name?.replace(/\([^)]*\)/g, '') || i.name, tag: '含外包' }),
        subTitle: i.orgPath,
        avatar: OrgDefaultAvatar,
        type: OrgStructureType.OutsourceDepartment,
      }));

      // 合并列表，人员在前
      const combinedList = [...memberList, ...childList];

      return {
        total: combinedList.length,
        pageSize: combinedList.length,
        list: combinedList,
      };
    },
    async searchFunc(pageInfo, kw) {
      const res = await getPmpGroups({
        ...pageInfo,
        query: kw,
        type: OrgStructureType.OutsourceDepartment,
      }, {});
      return {
        total: res.data?.data?.total || 0,
        list: (res.data?.data?.list || []).map((i) => ({
          key: getConcatKey(OrgStructureType.OutsourceDepartment, i.no),
          title: outsourceTitleRender({ isOutsource: true, title: i.name?.replace(/\([^)]*\)/g, '') || i.name, tag: '含外包' }),
          subTitle: i.orgPath,
          avatar: OrgDefaultAvatar,
          type: OrgStructureType.OutsourceDepartment,
        })),
      };
    },
    breadcrumbRender: (i) => i?.name,
  } as OrgStructureSchema<OrgStructureType, OrgStructureCellInfo<OrgStructureType> & { name?: string }>,
  {
    type: OrgStructureType.CustomGroup,
    title: '自定义组',
    summary: (i) => `${i}组`,
    async initFunc(pageInfo) {
      const res = await getPmpGroups({
        ...pageInfo,
        type: OrgStructureType.CustomGroup,
      }, {});
      return {
        total: res.data?.data?.total || 0,
        list: (res.data?.data?.list || []).map((i) => ({
          key: i.no,
          title: i.name,
          subTitle: '',
          avatar: GroupDefaultAvatar,
          type: OrgStructureType.CustomGroup,
        })),
      };
    },
    async searchFunc(pageInfo, kw) {
      const res = await getPmpGroups({
        ...pageInfo,
        query: kw,
        type: OrgStructureType.CustomGroup,
      }, {});
      return {
        total: res.data?.data?.total || 0,
        list: (res.data?.data?.list || []).map((i) => ({
          key: i.no,
          title: i.name,
          subTitle: '',
          avatar: GroupDefaultAvatar,
          type: OrgStructureType.CustomGroup,
        })),
      };
    },
  } as OrgStructureSchemaBase<OrgStructureType>,
];

// 禁用过去的日期
function disabledDate(current: Dayjs) {
  return current && current < dayjs().startOf('day');
}

// 禁用过去的时间
function disabledTime(current: Dayjs | null) {
  if (!current) {
    return {};
  }

  const now = dayjs();
  const isToday = current.isSame(now, 'day');

  if (!isToday) {
    return {};
  }

  const currentHour = now.hour();
  const currentMinute = now.minute();
  const currentSecond = now.second();

  return {
    disabledHours: () => Array.from({ length: currentHour }, (_, i) => i),
    disabledMinutes: (selectedHour: number) => {
      if (selectedHour === currentHour) {
        return Array.from({ length: currentMinute }, (_, i) => i);
      }
      return [];
    },
    disabledSeconds: (selectedHour: number, selectedMinute: number) => {
      if (selectedHour === currentHour && selectedMinute === currentMinute) {
        return Array.from({ length: currentSecond }, (_, i) => i);
      }
      return [];
    },
  };
}

async function handleConfirm() {
  try {
    submitting.value = true;

    // 校验过期时间
    if (formValue.expireTime && formValue.expireTime.isBefore(dayjs())) {
      message.error('请选择未来的时间');
      return;
    }

    const isSuccess = await props.sentReq?.(formValue);
    if (isSuccess) {
      await props.modalConfirm();
    }
  } catch (error) {
    console.error('添加成员失败:', error);
  } finally {
    submitting.value = false;
  }
}
</script>
