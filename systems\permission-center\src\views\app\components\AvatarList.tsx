import { defineComponent } from 'vue';
import { Dropdown, Menu, Tooltip } from 'ant-design-vue';
import type { SysUserInfo } from '../../../api/users';
import { formatUserDisplay } from '../../../composables/useUserInfo';
import { LARK_URL } from '../../../constants/appInfo';
import { EllipsisText } from '@hg-tech/oasis-common';
import FillAdminIcon from '../../../assets/icons/fill-admin.svg?component';

export default defineComponent({
  props: {
    avatarList: {
      type: Array as () => SysUserInfo[],
      default: () => [],
    },
    maxCount: {
      type: Number,
      default: 3,
    },
  },
  setup(props) {
    const handleUserClick = (feishuOpenId?: string) => {
      if (!feishuOpenId) {
        return;
      }
      window.open(`${LARK_URL}?openId=${feishuOpenId}`, '_blank');
    };

    // 渲染头像组的公共逻辑
    const renderAvatarGroup = () => {
      const userCount = props.avatarList?.length || 0;
      const displayUsers = props.avatarList?.slice(0, props.maxCount) || [];
      const remainingCount = userCount > props.maxCount ? userCount - props.maxCount : 0;

      return (
        <div class="flex items-center -space-x-1">
          {displayUsers.map((avatar) => (
            <Tooltip key={avatar.hgId} placement="top" title={formatUserDisplay(avatar)}>
              <div
                class={avatar.feishuOpenId ? 'cursor-pointer' : 'cursor-not-allowed'}
                onClick={(e: Event) => {
                  e.stopPropagation();
                  handleUserClick(avatar.feishuOpenId);
                }}
              >
                {avatar.avatar
                  ? (
                    <img class="size-24px flex-shrink-0 b-1 b-FO-Container-Stroke0 rounded-full b-solid" src={avatar.avatar} />
                  )
                  : (
                    <div class="size-24px flex items-center justify-center b-1 b-FO-Container-Stroke0 rounded-full bg-FO-Datavis-Purple3 c-FO-Datavis-Purple1">
                      <FillAdminIcon />
                    </div>
                  )}
              </div>
            </Tooltip>
          ))}
          {remainingCount > 0 && (
            <div
              class="FO-Font-R12 size-24px flex cursor-pointer items-center justify-center b-1 b-FO-Container-Stroke0 rounded-full bg-FO-Container-Fill3 text-FO-Content-Text2"
            >
              +{remainingCount}
            </div>
          )}
        </div>
      );
    };

    // 渲染下拉列表内容
    const renderDropdownOverlay = () => {
      return (
        <Menu class="max-h-200px max-w-200px overflow-y-auto rounded-6px">
          {props.avatarList?.map((avatar) => (
            <Menu.Item
              class="hover:bg-FO-Container-Fill3"
              disabled={!avatar.feishuOpenId}
              key={avatar.hgId}
              onClick={() => handleUserClick(avatar.feishuOpenId)}
            >

              <div class="w-full flex items-center gap-8px">
                {avatar.avatar
                  ? (
                    <img class="size-24px flex-shrink-0 b-1 b-FO-Container-Stroke0 rounded-full b-solid" src={avatar.avatar} />
                  )
                  : (
                    <div class="size-24px flex items-center justify-center rounded-full bg-FO-Datavis-Purple3 c-FO-Datavis-Purple1">
                      <FillAdminIcon />
                    </div>
                  )}
                <EllipsisText class="FO-Font-R14 min-w-0 flex-1 text-FO-Content-Text1">
                  {formatUserDisplay(avatar)}
                </EllipsisText>
              </div>
            </Menu.Item>
          ))}
        </Menu>
      );
    };

    return () => {
      const userCount = props.avatarList?.length || 0;

      // 当用户数量 > maxCount 时显示下拉列表
      if (userCount > props.maxCount) {
        return (
          <Dropdown
            placement="bottomLeft"
            trigger={['hover']}
            v-slots={{
              overlay: renderDropdownOverlay,
            }}
          >
            {renderAvatarGroup()}
          </Dropdown>
        );
      }

      // 当用户数量 <= maxCount 时显示原有的 Tooltip
      return renderAvatarGroup();
    };
  },
});
