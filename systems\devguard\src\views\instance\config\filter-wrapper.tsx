import Icon, { CloseCircleFilled } from '@ant-design/icons-vue';
import { <PERSON><PERSON>, RangePicker, Select } from 'ant-design-vue';
import { type PropType, computed, defineComponent, ref } from 'vue';
import dayjs, { type Dayjs } from 'dayjs';
import { ForgeonFilterPopover, ForgeonUserSelector } from '@hg-tech/oasis-common';
import { useUserListOption } from '../../../composables/useUserSearch';
import { useUserAuthStore } from '../../../store/modules/userAuth';
import { store } from '../../../store/pinia';
import BasicFillFilter from '../../../assets/icons/BasicFillFilter.svg?component';
import { operationTypeOptions } from './type.data';

const FilterWrapper = defineComponent({
  props: {
    form: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
  },
  emits: ['update:form'],
  setup(props, { emit }) {
    const form = computed({
      get: () => props.form,
      set: (value) => emit('update:form', value),
    });

    const { userProfile } = useUserAuthStore(store);
    const { userListOptions, userListLoading, getUserItemFromSession, queryUser, resetUserList } = useUserListOption(computed(() => [{
      hgAccount: userProfile?.userName || '',
      name: userProfile?.nickName || '',
      nickName: userProfile?.moniker || '',
      openId: userProfile?.openID || '',
      avatar: userProfile?.headerImg || '',
    }]));

    const rangePresets = ref([
      { label: '今天', value: [dayjs().startOf('day'), dayjs().endOf('day')] },
      { label: '昨天', value: [dayjs().add(-1, 'd').startOf('day'), dayjs().add(-1, 'd').endOf('day')] },
      { label: '最近3天', value: [dayjs().add(-3, 'd'), dayjs()] },
      { label: '最近7天', value: [dayjs().add(-7, 'd'), dayjs()] },
      { label: '最近14天', value: [dayjs().add(-14, 'd'), dayjs()] },
      { label: '最近30天', value: [dayjs().add(-30, 'd'), dayjs()] },
    ]);

    const renderUserItem = (item: string) => {
      const targetUser = getUserItemFromSession(item);
      return targetUser ? targetUser.name || targetUser.nickName : '未知用户';
    };

    const renderDate = (timestamp?: string) => {
      return timestamp ? dayjs(Number(timestamp) * 1000).format('YYYY-MM-DD HH:mm:ss') : '';
    };

    const renderClear = (visible: boolean, clear: () => void) => {
      return visible && (
        <Icon
          class="ml-4px cursor-pointer px-4px"
          component={CloseCircleFilled}
          onClick={(e) => {
            e.stopPropagation();
            clear();
          }}
        />
      );
    };

    const filterConfigs = computed(() => [
      {
        key: 'time',
        label: '操作时间',
        value: [form.value.startTime, form.value.endTime],
        tooltip: Boolean(form.value.startTime || form.value.endTime),
        tooltipContent: `操作时间${renderDate(form.value.startTime)} - ${renderDate(form.value.endTime)}`,
        isActive: Boolean(form.value.startTime || form.value.endTime),
        iconClass: (form.value.startTime || form.value.endTime ? 'c-FO-Brand-Primary-Default' : 'c-FO-Content-Icon2'),
        buttonClass: (form.value.startTime || form.value.endTime ? 'btn-fill-secondary' : 'btn-fill-default'),
        clear: () => {
          form.value.startTime = undefined;
          form.value.endTime = undefined;
        },
        content: () => {
          const submitTime = computed({
            get: () => [
              form.value.startTime ? dayjs(Number(form.value.startTime) * 1000) : undefined,
              form.value.endTime ? dayjs(Number(form.value.endTime) * 1000) : undefined,
            ] as [Dayjs | undefined, Dayjs | undefined],
            set: (value: [Dayjs | undefined, Dayjs | undefined]) => {
              form.value.startTime = value?.[0]
                ? Math.floor(value[0]?.valueOf() / 1000).toString()
                : undefined;
              form.value.endTime = value?.[1]
                ? Math.floor(value[1]?.valueOf() / 1000).toString()
                : undefined;
            },
          });
          return (
            <RangePicker presets={rangePresets.value} show-time v-model:value={submitTime.value} />
          );
        },
        buttonText: () => (
          <>
            <span>操作时间</span>
            {
              form.value.startTime || form.value.endTime
                ? <span>：{renderDate(form.value.startTime)} - {renderDate(form.value.endTime)}</span>
                : null
            }
          </>
        ),
      },
      {
        key: 'operatorNames',
        label: '操作人',
        value: form.value.operatorNames,
        tooltip: Boolean(form.value.operatorNames && form.value.operatorNames.length),
        tooltipContent: `处理人：${form.value.operatorNames && form.value.operatorNames.length ? form.value.operatorNames.map(renderUserItem).join(', ') : ''}`,
        isActive: Boolean(form.value.operatorNames && form.value.operatorNames.length),
        iconClass: (form.value.operatorNames && form.value.operatorNames.length ? 'c-FO-Brand-Primary-Default' : 'c-FO-Content-Icon2'),
        buttonClass: (form.value.operatorNames && form.value.operatorNames.length ? 'btn-fill-secondary' : 'btn-fill-default'),
        clear: () => {
          form.value.operatorNames = [];
        },
        content: () => (
          <ForgeonUserSelector
            class="w-300px"
            loading={userListLoading.value}
            multiple
            onReset={resetUserList}
            onSearch={(params) => {
              queryUser(params, {});
            }}
            options={userListOptions.value}
            placeholder="请输入用户姓名/昵称/邮箱/拼音"
            showAvatar
            v-model:value={form.value.operatorNames}
          />
        ),
        buttonText: () => (
          <>
            <span>处理人</span>
            {form.value.operatorNames && form.value.operatorNames.length
              ? form.value.operatorNames.length > 1
                ? <span>：{renderUserItem(form.value.operatorNames[0])}等{form.value.operatorNames.length}人</span>
                : <span>：{renderUserItem(form.value.operatorNames[0])}</span>
              : null}
          </>
        ),
      },
      {
        key: 'operationTypes',
        label: '操作类型',
        value: form.value.operationTypes,
        tooltip: Boolean(form.value.operationTypes.length),
        tooltipContent: `操作类型：${form.value.operationTypes.map((type: number) => operationTypeOptions.find((operationType) => operationType.value === type)?.label || '').join(', ')}`,
        isActive: Boolean(form.value.operationTypes.length),
        iconClass: (form.value.operationTypes.length ? 'c-FO-Brand-Primary-Default' : 'c-FO-Content-Icon2'),
        buttonClass: (form.value.operationTypes.length ? 'btn-fill-secondary' : 'btn-fill-default'),
        clear: () => {
          form.value.operationTypes = [];
        },
        content: () => (
          <Select class="w-300px" mode="multiple" options={operationTypeOptions} placeholder="请选择操作类型" v-model:value={form.value.operationTypes} />
        ),
        buttonText: () => (
          <>
            <span>操作类型</span>
            {form.value.operationTypes.length ? <span>：{form.value.operationTypes.map((type: number) => operationTypeOptions.find((operationType) => operationType.value === type)?.label || '').join(', ')}</span> : null}
          </>
        ),
      },

    ]);

    return () => (
      <div class="filter-wrapper flex flex-wrap items-center gap-10px">
        {filterConfigs.value.map((config) => (
          <ForgeonFilterPopover
            key={config.key}
            placement="bottomLeft"
            tooltip={config.tooltip}
            tooltipContent={config.tooltipContent}
            trigger="click"
          >
            {{
              default: () => (
                <Button
                  class={[
                    config.buttonClass,
                    'flex items-center justify-between gap-4px',
                  ]}
                  icon={<Icon class={config.iconClass} component={BasicFillFilter} />}
                >
                  <span class={config.isActive ? 'c-FO-Brand-Primary-Default' : 'c-FO-Content-Text2'}>
                    {config.buttonText()}
                    {renderClear(config.isActive, config.clear)}
                  </span>
                </Button>
              ),
              content: config.content,
            }}
          </ForgeonFilterPopover>
        ))}
      </div>
    );
  },
});

export {
  FilterWrapper,
};
