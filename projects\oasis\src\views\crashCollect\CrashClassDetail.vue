<template>
  <div :class="prefixCls">
    <div
      class="relative h-80px flex items-center bg-FO-Container-Fill1 px-24px py-20px text-center font-size-20px font-bold"
    >
      <div class="absolute flex cursor-pointer" @click="goBack()">
        <Icon icon="icon-park-outline:left" :size="24" />
        <div class="ml-2">
          返回
        </div>
      </div>
      <div class="flex-1">
        Crash分类详情
      </div>
    </div>

    <div class="m-4 min-w-1200px rounded-md bg-FO-Container-Fill1 p-4">
      <Spin :spinning="!crashClassDetail?.ID">
        <div
          v-if="crashClassDetail?.ID"
          class="relative h-32px flex items-center justify-between font-bold"
          :style="`background: ${getBackgroundColor(crashClassDetail?.name)};`"
        >
          <div :class="`${prefixCls}_rhombus`" />
          <div v-if="openValue !== crashClassDetail?.ID" class="m-2 ml-5 color-#252525">
            #{{ crashClassDetail?.ID }}.{{ crashClassDetail?.name }}
            <Icon
              v-if="crashClassDetail?.ID"
              icon="icon-park-outline:editor"
              class="cursor-pointer"
              @click="addOpenValue(crashClassDetail?.ID, crashClassDetail?.name)"
            />
          </div>
          <div v-else :class="`flex flex-items-center  ml-5 h-20px ${prefixCls}_input-class`">
            <span class="mr-2 color-#252525">#{{ crashClassDetail?.ID }}</span>
            <div class="mx-2 h-100% flex items-center rounded-6px bg-FO-Container-Fill1">
              <FitInput
                v-model="inputValue"
                :minWidth="70"
                class="h-100%"
                :inputProps="{
                  placeholder: '分类名称',
                  size: 'small',
                  bordered: false,
                  maxlength: 30,
                }"
              />
              <Icon
                icon="icon-park-outline:check-small"
                size="13"
                class="mx-1 cursor-pointer"
                @click="checkClick(crashClassDetail?.ID, inputValue)"
              />
              <Icon
                icon="icon-park-outline:return"
                size="13"
                class="mx-1 cursor-pointer"
                @click="returnClick"
              />
            </div>
          </div>
          <div class="flex color-#252525">
            <div class="mr-20">
              触发总次数：{{ crashCategoryStat?.total }}
            </div>
            <div class="mr-20">
              近两周触发次数：{{ crashCategoryStat?.count }}
            </div>
            <div class="mr-20 flex">
              近两周频繁触发人：
              <div v-for="(v, i) in crashCategoryStat?.username" :key="i" class="mr-2">
                {{ v }}
              </div>
            </div>
          </div>
        </div>
        <div ref="scrollBoxRef" :class="`${prefixCls}__scroll-box`">
          <template v-for="item in crashClassDetail?.records" :key="item.ID">
            <div
              class="b-1 b-FO-Container-Fill0" :class="[`${prefixCls}__records-item`, { '!b-FO-Brand-Primary-Default': nowDetail === item.ID }]" @click="openDetail(item.ID!)"
            >
              <div class="flex items-center">
                <div class="font-bold">
                  Crash-{{ item.ID }}
                </div>
                <div v-if="item.bugID" class="ml-3 border-2 b-rounded-6 px-2 py-2px font-size-12px">
                  已提单
                </div>
              </div>
              <div class="mt-2 flex flex-wrap gap-2">
                <div class="mr-20">
                  触发时间：{{ formatTISOToDate(item.time) }}
                </div>
                <div class="mr-20">
                  触发人：{{ item.equipment?.user }}
                </div>
                <div class="mr-20 flex items-center">
                  <span>引擎版本：</span>  <EllipsisText class="max-w-300px">
                    {{ item.engineVersion?.engineVersion }}
                  </EllipsisText>
                </div>
                <div v-if="item?.streamClVersion" class="item-center flex flex-1">
                  <span>工程CL号：</span><EllipsisText class="max-w-300px">
                    {{ item?.streamClVersion }}
                  </EllipsisText>
                </div>
              </div>
            </div>
          </template>
        </div>
        <div class="flex justify-end pt-10px">
          <APagination v-model:current="page" v-model:pageSize="pageSize" :total="crashCategoryStat?.total" @change="paginationChange" />
        </div>
      </Spin>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { Pagination as APagination, Spin } from 'ant-design-vue';
import { useCrashClassDetail, useCrashCollect } from './crashCollectHook';
import { updateCategoriesName } from '/@/api/page/crashCollect';
import { FitInput } from '/@/components/FitInput';
import Icon from '/@/components/Icon';
import { useDesign } from '/@/hooks/web/useDesign';
import { useGo } from '/@/hooks/web/usePage';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { formatTISOToDate } from '/@/utils/dateUtil';
import { useClassCrashStore } from './stores';

defineOptions({
  name: 'CrashClassDetail',
});

const { prefixCls } = useDesign('crash-class-detail');
const { currentRoute } = useRouter();
const userStore = useUserStoreWithOut();
const crashClassStore = useClassCrashStore();

const categoryID = Number(currentRoute.value.params.categoryID);
const { getBackgroundColor } = useCrashCollect();
const { getCrashClassDetail, crashClassDetail, getCrashStatByClass, crashCategoryStat }: any
    = useCrashClassDetail();

const go = useGo();
const router = useRouter();
const inputValue = ref();
const oldName = ref();
const openValue = ref();
const page = ref(1);
const pageSize = ref(20);
const scrollBoxRef = ref<HTMLElement | null>(null);
const nowDetail = ref<number | undefined>(undefined);
function openDetail(ID: number) {
  nowDetail.value = ID;
  crashClassStore.setItem({ page: page.value, pageSize: pageSize.value, clickID: ID, scroll: scrollBoxRef.value?.scrollTop });
  go({
    name: 'CrashDetail',
    params: {
      recordID: ID,
    },
    query: {
      from: 'classDetail',
      categoryID,
    },
  });
}

function goBack() {
  crashClassStore.setItem({ page: 1, pageSize: 20, clickID: undefined, scroll: 0 });
  if (window.history.state.back?.includes('crashCollect')) {
    router.back();
  } else {
    go({ name: 'CrashCollect' });
  }
}

function addOpenValue(id: number, name: number) {
  // 存储旧值
  inputValue.value = name;
  oldName.value = name;
  openValue.value = id;
}

async function checkClick(id: number, name: string) {
  // 删除openValue.value中的id
  const res = await updateCategoriesName(userStore.getProjectId, id, { name });

  if (res?.code !== 7) {
    await getCrashClassDetail(categoryID, page.value, pageSize.value);
    openValue.value = undefined;
    inputValue.value = '';
    oldName.value = '';
  }
}

function returnClick() {
  openValue.value = undefined;
  inputValue.value = '';
  oldName.value = '';
}
function paginationChange(pageParam: number, pageSizeParam: number) {
  page.value = pageParam;
  pageSize.value = pageSizeParam;
  nowDetail.value = undefined;
  if (scrollBoxRef.value) {
    scrollBoxRef.value.scrollTo({
      top: 0, // 替换为 scroll.value
      left: 0, // 水平滚动位置
    });
  }

  crashClassStore.setItem({ page: page.value, pageSize: pageSize.value, clickID: undefined, scroll: 0 });
  getCrashClassDetail(categoryID, page.value, pageSize.value);
}
onMounted(async () => {
  // 获取page.value, pageSize.value
  page.value = crashClassStore.getPage;
  pageSize.value = crashClassStore.getPageSize;
  nowDetail.value = crashClassStore.getClickID;
  await getCrashClassDetail(categoryID, page.value, pageSize.value);
  await getCrashStatByClass(categoryID);
  nextTick(() => {
    if (scrollBoxRef.value && crashClassStore.getScroll) {
      scrollBoxRef.value.scrollTo({
        top: crashClassStore.getScroll, // 替换为 scroll.value
        left: 0, // 水平滚动位置
        behavior: 'smooth', // 平滑滚动
      });
    }
  });
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-crash-class-detail';
.@{prefix-cls} {
  &__scroll-box {
    @apply mt-4;
    height: calc(100vh - 300px);
    overflow: auto;
    display: flex;
    flex-direction: column;
    row-gap: 16px;
  }

  &_input-class {
    input {
      height: 100% !important;
    }
  }

  &_rhombus {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;

    &::before {
      content: ' ';
      position: absolute;
      top: 0;
      left: 0;
      width: 0;
      height: 0;
      border-top: 16px solid @FO-Container-Fill1;
      border-bottom: 16px solid @FO-Container-Fill1;
      border-right: 16px solid transparent;
      z-index: 2;
    }
  }

  &__records-item {
    @apply rounded-md ml-3 mr-2 cursor-pointer;

    padding: 10px;
    background-color: @report-card-background;
  }
}
</style>
