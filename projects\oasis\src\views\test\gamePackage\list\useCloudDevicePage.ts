import { PlatformEnterPoint, preprocessFilePath } from '@hg-tech/oasis-common';
import { useUserStore } from '../../../../store/modules/user.ts';
import { useRouter } from 'vue-router';
import { type MaybeRef, unref } from 'vue';
import type { GamePackagesListItem, GamePackagesVersionsListItem } from '../../../../api/page/model/testModel.ts';

export function useCloudPhonePage(isOasis: MaybeRef) {
  const userStore = useUserStore();
  const router = useRouter();

  function openCloudPhonePage(pkgId: GamePackagesListItem['ID'], pkgVersionId: GamePackagesVersionsListItem['ID']) {
    const cloudDeviceLink = router.resolve({
      name: PlatformEnterPoint.CloudDevice,
      query: {
        p: userStore.getProjectId,
        pkgId,
        pkgVersionId,
      },
    }).href;
    if (unref(isOasis)) {
      window.open(`oasisdownload://open-browser?url=${encodeURIComponent(preprocessFilePath(cloudDeviceLink))}`, '_blank');
    } else {
      window.open(cloudDeviceLink, '_blank');
    }
  }

  return {
    openCloudPhonePage,
  };
}
