<template>
  <BasicModal
    :title="getTitle" :width="600" :maskClosable="false"
    :afterClose="handleAfterClose" :footer="null" @register="registerModal" @ok="handleSubmit"
  >
    <AAlert v-if="isRenew" message="续期后总占用时长不超过24小时（1440分钟）" type="info" showIcon />
    <div class="my-6">
      <BasicForm :schemas="formSchema" @register="registerForm">
        <template #pkgInfo>
          <div class="flex items-center gap-2">
            <AImage :src="preprocessFilePath(pkgInfo?.icon)" class="rounded-4px !h-8 !w-8" :preview="false" />
            <div class="flex flex-col">
              <div>
                {{ pkgInfo?.name }}
              </div>
              <div class="text-12px c-FO-Content-Text2">
                {{ pkgVersionInfo?.version }}
              </div>
            </div>
          </div>
        </template>
        <template #deviceID="{ model, field }">
          <div class="flex flex-col gap-2">
            <ASelect
              v-model:value="model[field]" :options="showDeviceList" class="max-w-260px" showSearch
              optionFilterProp="label"
            />
            <ACard v-if="showDevice(model[field])" class="max-w-360px rd-4px bg-FO-Container-Fill1 [&>.ant-card-body]:(h-full p-4)">
              <div class="h-full flex flex-col justify-between gap-2">
                <div class="w-full flex items-center justify-between gap-1">
                  <EllipsisText
                    class="min-w-0 flex-1 font-bold" :class="{
                      'c-FO-Functional-Error1-Default': showDevice(model[field])?.mobileType === 2,
                    }"
                  >
                    {{ showDevice(model[field])?.deviceName + (showDevice(model[field])?.mobileType === 2 ? '(开发机)'
                      : '') }}
                  </EllipsisText>
                </div>
                <div class="flex items-center">
                  <div class="mr-3 h-160px w-120px flex items-center justify-center">
                    <AImage
                      v-if="showDevice(model[field])?.picURL" class="max-h-160px max-w-120px object-contain"
                      :src="preprocessFilePath(showDevice(model[field])?.picURL)" :preview="false"
                    />
                    <AEmpty v-else :image="emptyImg" description="暂无图片" />
                  </div>
                  <div class="min-w-0 flex-1">
                    <div class="flex flex-col gap-1">
                      <div v-if="showDevice(model[field])?.chipset?.socPK">
                        跑分: <span class="w-fit">{{ showDevice(model[field])?.chipset?.socPK }}</span>
                      </div>
                      <div v-for="text in cardContentList" :key="text.value" class="flex items-center">
                        <span class="whitespace-nowrap">
                          {{ text.label }}
                        </span>
                        <EllipsisText class="c-FO-Content-Text2">
                          {{ getCardContent(text, showDevice(model[field])) }}
                        </EllipsisText>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </ACard>
          </div>
        </template>
        <template #duration="{ model, field, schema }">
          <AInputNumber v-model:value="model[field]" v-bind="schema.componentProps" />
          <div class="mt-2 flex items-center gap-2">
            <div class="flex gap-2">
              <div
                v-for="duration in quickDurations" :key="duration"
                class="cursor-pointer b-1 b-FO-Container-Stroke1 rd-8 px-4 text-14px hover:c-FO-Brand-Primary-Default"
                :class="model[field] === duration ? 'c-FO-Brand-Primary-Default b-FO-Brand-Primary-Default' : ''" @click="handleQuickSelect(duration)"
              >
                {{ duration }}分
              </div>
            </div>
          </div>
        </template>
      </BasicForm>
    </div>
    <template #footer>
      <div v-if="isRenew && (cloudDeviceStore.mtlDevice?.deviceOccupy?.planUseMinutes || 0) >= MAX_RENEW_MINUTES" class="flex justify-center gap-4">
        <AButton @click="closeModal">
          取消
        </AButton>
        <ATooltip>
          <template #title>
            已达占用时长上限
          </template>
          <AButton type="primary" :disabled="true" @click="handleSubmit">
            {{ pkgVersionInfo ? '开始测试' : '确定' }}
          </AButton>
        </ATooltip>
      </div>
      <div v-else class="flex justify-center gap-4">
        <AButton @click="closeModal">
          取消
        </AButton>
        <AButton type="primary" @click="handleSubmit">
          {{ pkgVersionInfo ? '开始测试' : '确定' }}
        </AButton>
      </div>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup>
import type { ModalMethods } from '/@/components/Modal';
import { BasicModal, useModalInner } from '/@/components/Modal';
import type { FormSchema } from '/@/components/Form';
import { computed, ref } from 'vue';
import { BasicForm, useForm } from '/@/components/Form';
import { PlatformEnterPoint, preprocessFilePath } from '@hg-tech/oasis-common';
import { useRoute, useRouter } from 'vue-router';
import { Alert as AAlert, Card as ACard, Empty as AEmpty, Image as AImage, InputNumber as AInputNumber, Select as ASelect, Tooltip as ATooltip, message } from 'ant-design-vue';
import { type DeviceListItem, DeviceAccessLevelEnum } from '/@/api/page/model/deptAssetModel';
import { addDeviceUseTime, createDeviceUseRecord, getMtlDeviceDetail } from '/@/api/page/mtl/device';
import type { MtlDeviceListItem } from '/@/api/page/mtl/model/deviceModel';
import { useUserStoreWithOut } from '/@/store/modules/user';
import type { GamePackagesListItem, GamePackagesVersionsListItem } from '/@/api/page/model/testModel';
import { cardContentList } from '../apply/device.data';
import { EllipsisText } from '/@/components/EllipsisText';
import { useDeptAssetApply } from '../apply/hook';
import { orderBy } from 'lodash-es';
import { useCloudDeviceStore } from './stores';
import { getGamePackagesVersionByID } from '/@/api/page/test';
import { getDeviceByID } from '/@/api/page/deptAsset';
import { DEFAULT_RENEW_MINUTES, MAX_RENEW_MINUTES } from './device.data';

const emits = defineEmits<{
  success: [duration?: number];
  register: [methods: ModalMethods, uuid: number];
}>();

const emptyImg = AEmpty.PRESENTED_IMAGE_SIMPLE;
const quickDurations = [15, 30, 60, 120];
const { push, replace } = useRouter();
const route = useRoute();
const device = ref<DeviceListItem>();
const mtlDevice = ref<MtlDeviceListItem>();
const userStore = useUserStoreWithOut();
const { getCardContent } = useDeptAssetApply();
const isRenew = ref(false);
const deviceList = ref<DeviceListItem[]>([]);
const pkgVersionInfo = ref<GamePackagesVersionsListItem>();
const pkgInfo = ref<GamePackagesListItem>();
const cloudDeviceStore = useCloudDeviceStore();

const getTitle = computed(() => {
  if (isRenew.value) {
    return '续期时长';
  }
  if (pkgVersionInfo.value) {
    return '包体测试';
  }
  return '使用时长';
});

const showDeviceList = computed(() => {
  return deviceList.value.map((item) => ({
    label: `${item.deviceName}(${item.assetNo})`,
    value: item.ID,
  }));
});

const isPkgTest = computed(() => {
  return pkgInfo.value && pkgVersionInfo.value && deviceList.value?.length;
});
const formSchema = computed(() => [
  {
    label: '测试包体',
    field: 'pkgInfo',
    component: 'Input',
    ifShow: () => isPkgTest.value,
    slot: 'pkgInfo',
  },
  {
    label: '测试设备',
    field: 'deviceID',
    component: 'Select',
    required: true,
    ifShow: () => isPkgTest.value,
    slot: 'deviceID',
  },
  {
    label: isRenew.value ? '续期时长' : '使用时长',
    field: 'duration',
    component: 'InputNumber',
    slot: 'duration',
    defaultValue: 15,
    componentProps: {
      placeholder: isRenew.value ? '请输入续期时长' : '请输入使用时长',
      min: isRenew.value && (cloudDeviceStore.mtlDevice?.deviceOccupy?.planUseMinutes || 0) >= MAX_RENEW_MINUTES ? 0 : 1,
      max: isRenew.value ? MAX_RENEW_MINUTES - (cloudDeviceStore.mtlDevice?.deviceOccupy?.planUseMinutes ?? 0) : MAX_RENEW_MINUTES,
      addonAfter: '分',
      precision: 0,
      class: '!w-100px',
    },

    required: true,
  },
] as FormSchema[]);

/**
 * 选中的设备
 * @param ID 设备ID
 * @returns 设备信息
 */
function showDevice(ID?: number) {
  return deviceList.value?.find((e) => e.ID === ID);
}

/** 获取云真机设备详情 */
async function getMtlDeviceInfo(mtlDeviceID?: number) {
  const deviceID = mtlDeviceID ?? device.value?.deviceId;
  if (!deviceID) {
    return;
  }
  const { data: { data: mtlDeviceInfo } } = await getMtlDeviceDetail({ id: deviceID }, {});
  mtlDevice.value = mtlDeviceInfo;
}

/** 获取包版本信息 */
async function getPkgVersionInfo() {
  if (!userStore.getProjectId || !pkgInfo.value?.ID || !pkgVersionInfo.value?.ID) {
    return;
  }
  const versionID = pkgVersionInfo.value?.ID;
  try {
    const { repkgVersion } = await getGamePackagesVersionByID(
      userStore.getProjectId,
      pkgInfo.value?.ID,
      versionID,
      'none',
    );
    pkgVersionInfo.value = repkgVersion;
  } catch {
    pkgVersionInfo.value = undefined;
  }
}

const [registerForm, { resetFields, validate, setFieldsValue }] = useForm({
  labelWidth: 120,
  baseColProps: { span: 22 },
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  setModalProps({ confirmLoading: true });
  device.value = data.device;
  isRenew.value = data.isRenew;
  // 按跑分倒序排序
  deviceList.value = orderBy(data.deviceList || [], 'chipset.socPK', 'desc');
  pkgInfo.value = data.pkgInfo;
  pkgVersionInfo.value = data.pkgVersionInfo;
  await resetFields();
  setFieldsValue({
    deviceID: deviceList.value?.[0]?.ID,
    duration: isRenew.value && (cloudDeviceStore.mtlDevice?.deviceOccupy?.planUseMinutes ?? 0) > (MAX_RENEW_MINUTES - DEFAULT_RENEW_MINUTES) ? MAX_RENEW_MINUTES - (cloudDeviceStore.mtlDevice?.deviceOccupy?.planUseMinutes ?? 0) : DEFAULT_RENEW_MINUTES,
  });

  setModalProps({ confirmLoading: false });
});

async function handleSubmit() {
  try {
    const values = await validate();
    setModalProps({ confirmLoading: true });
    if (isRenew.value) {
      await cloudDeviceStore.getMtlDeviceInfo();
      mtlDevice.value = cloudDeviceStore.mtlDevice;
    } else {
      if (isPkgTest.value) {
        const curDevice = showDevice(values.deviceID);
        await getMtlDeviceInfo(curDevice?.deviceId);
        if (!curDevice?.ID) {
          message.warning('设备ID不存在');
          return;
        }
        const { assetDevice } = await getDeviceByID(curDevice.ID);
        if (!assetDevice || assetDevice.accessLevel === DeviceAccessLevelEnum.UNAVAILABLE) {
          message.warning('设备不可借用');
          return;
        }
        await getPkgVersionInfo();
      } else {
        await getMtlDeviceInfo();
      }
    }
    if (isPkgTest.value && !pkgVersionInfo.value) {
      message.warning('获取包版本信息失败，请重新选择');
      closeModal();
      return;
    }
    if (['DISCONNECTED', 'OFFLINE'].includes(mtlDevice.value?.status ?? '')) {
      message.warning('设备已断开，无法使用设备');
      if (isPkgTest.value) {
        // 移除已断开的设备
        deviceList.value = deviceList.value.filter((e) => e.ID !== values.deviceID);
      } else {
        emits('success');
        closeModal();
      }
      return;
    } else if (mtlDevice.value?.deviceOccupy?.username && mtlDevice.value.deviceOccupy?.username !== userStore.getUserInfo.userName) {
      message.warning('设备占用中，无法使用设备');
      if (isPkgTest.value) {
        // 移除已占用的设备
        deviceList.value = deviceList.value.filter((e) => e.ID !== values.deviceID);
      } else {
        emits('success');
        closeModal();
      }
      return;
    }
    if (!mtlDevice.value?.udId) {
      message.warning('设备udId不存在');
      return;
    }
    if (isRenew.value) {
      if (mtlDevice.value?.deviceOccupy?.planUseMinutes + values.duration > MAX_RENEW_MINUTES) {
        message.warning('总占用时长超过24小时，请修改');
        return;
      }
      const { data: { code, message: msg, data: mtlDeviceInfo } } = await addDeviceUseTime({}, {
        udId: mtlDevice.value?.udId,
        addUseTime: values.duration,
      });
      if (code === 2000) {
        cloudDeviceStore.mtlDevice = mtlDeviceInfo;
        emits('success', values.duration);
        message.success('续期成功');

        closeModal();
      } else {
        message.error(msg);
      }
    } else {
      const { data: { code, message: msg } } = await createDeviceUseRecord({}, {
        udId: mtlDevice.value?.udId,
        addUseTime: values.duration,
      });
      if (code === 2000) {
        if (isPkgTest.value) {
          push({
            name: PlatformEnterPoint.CloudDeviceDetail,
            params: { id: values.deviceID },
            query: {
              deviceId: mtlDevice.value?.id,
              pkgId: pkgInfo.value?.ID,
              pkgVersionId: pkgVersionInfo.value?.ID,
            },
          });
        } else {
          push({
            name: PlatformEnterPoint.CloudDeviceDetail,
            params: { id: device.value?.ID },
            query: {
              deviceId: device.value?.deviceId,
            },
          });
        }
      } else {
        message.error(msg);
      }
    }
  } catch (error) {
    console.error(error);
  } finally {
    setModalProps({ confirmLoading: false });
  }
}

function handleQuickSelect(duration: number) {
  setFieldsValue({ duration });
}

/** 关闭模态框 */
async function handleAfterClose() {
  if (route.name === PlatformEnterPoint.CloudDevice && (route.query.pkgId || route.query.pkgVersionId)) {
    await replace({ query: { ...route.query, pkgId: undefined, pkgVersionId: undefined } });
  }
}
</script>
