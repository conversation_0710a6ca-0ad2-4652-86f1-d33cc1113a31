<template>
  <div class="streams-list">
    <div v-for="stream in streamsList" :key="stream.id" class="streams-list-item">
      <div class="flex items-center">
        <MainlineIcon v-if="stream.streamType === StreamType.Mainline" class="h-20px" />
        <ReleaseIcon v-else-if="stream.streamType === StreamType.Release" class="h-20px" />
        <TaskIcon v-else-if="stream.streamType === StreamType.Task" class="h-20px" />
        <VirtualIcon v-else-if="stream.streamType === StreamType.Virtual" class="h-20px" />
        <DevelopmentIcon v-else class="h-20px" />

        <div class="ml-16px">
          <div class="flex items-center font-bold">
            {{ stream.description || stream.path }}
          </div>
          <div class="flex items-center text-xs c-FO-Content-Text2">
            <span>{{ stream.path }}</span>
          </div>
        </div>
      </div>
      <div class="flex">
        <div class="px-10px">
          <div class="flex cursor-pointer items-center gap-10px b-rd-8px bg-FO-Container-Fill3 p-10px" @click="goToCommitList(stream)">
            <div class="FO-Font-b18">
              正在提交
            </div>
            <div class="FO-Font-B18">
              {{ stream.submittingCount }}
            </div>
            <Icon :icon="RightIcon" class="FO-Font-B18 c-FO-Content-Text2" />
          </div>
        </div>
        <div v-if=" stream.groupedInstanceList?.length" class="b-l-1px b-r-FO-Container-Stroke4 px-10px">
          <div class="flex cursor-pointer items-center gap-10px b-rd-8px bg-FO-Container-Fill3 p-10px" @click="goToInstance(stream)">
            <div class="FO-Font-b18">
              检查实例状态
            </div>
            <div
              v-for="(item, index) in stream.groupedInstanceList?.[0]?.instanceList" :key="index"
              class="h-10px w-10px b-rd-5px"
              :class="{ 'bg-FO-Datavis-Yellow2': item.workState === InstanceStatus.Busy, 'bg-FO-Datavis-Blue2': item.workState === InstanceStatus.Idle, 'bg-FO-Content-Text4': item.workState === InstanceStatus.Offline }"
            />
            <div v-if="stream.groupedInstanceList?.[1]?.instanceList.length" class="h-20px w-1px bg-FO-Container-Stroke4" />
            <div
              v-for="(item, index) in stream.groupedInstanceList?.[1]?.instanceList" :key="index"
              class="h-10px w-10px b-rd-5px"
              :class="{ 'bg-FO-Datavis-Yellow2': item.workState === InstanceStatus.Busy, 'bg-FO-Datavis-Blue2': item.workState === InstanceStatus.Idle, 'bg-FO-Content-Text4': item.workState === InstanceStatus.Offline }"
            />
            <div class="FO-Font-b18">
              待检查队列
            </div>
            <div class="FO-Font-B18 flex items-center gap-2px">
              <span>{{ stream.groupedInstanceList?.[0]?.queueCount }}</span>
              <span v-if="stream.groupedInstanceList?.[1]?.instanceList.length">+</span>
              <span>{{ stream.groupedInstanceList?.[1]?.queueCount }}</span>
            </div>
            <Icon :icon="RightIcon" class="FO-Font-B18 c-FO-Content-Text2" />
          </div>
        </div>
        <!-- 暂时隐藏 -->
        <!-- <div class="px-10px">
          <div class="flex cursor-pointer items-center gap-10px b-rd-8px p-10px hover:bg-FO-Container-Fill2">
            <div class="FO-Font-b18">
              今日已提交
            </div>
            <div class="FO-Font-B18">
              {{ stream.todaySubmittedCount }}
            </div>
          </div>
        </div> -->
      </div>
    </div>
    <div v-if="!streamsList.length" class="flex justify-center">
      <Empty description="暂无数据" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router';
import type { StreamsListItem } from '../../api';
import DevelopmentIcon from '../../assets/icons/stream-development.svg?component';
import MainlineIcon from '../../assets/icons/stream-mainline.svg?component';
import ReleaseIcon from '../../assets/icons/stream-release.svg?component';
import TaskIcon from '../../assets/icons/stream-task.svg?component';
import VirtualIcon from '../../assets/icons/stream-virtual.svg?component';
import RightIcon from '@iconify-icons/icon-park-outline/right';
import { Icon } from '@iconify/vue';
import { InstanceStatus, StreamType } from './steams.data';
import { getMicroAppData, PlatformEnterPoint, usePermissionCtx } from '@hg-tech/oasis-common';
import { MergePermission } from '../../constants/premission';
import { checkAdminPermission } from '../../services/permission';

withDefaults(defineProps<{
  streamsList: StreamsListItem[];

}>(), {
  streamsList: () => [],

});
const router = useRouter();
const permissionData = getMicroAppData(usePermissionCtx);
function goToCommitList(stream: StreamsListItem) {
  if (!checkAdminPermission({ any: [MergePermission.SubmitCenterAccessRecords] }, permissionData?.permissionInfo)) {
    return;
  }
  router.push({
    name: PlatformEnterPoint.CommitList,
    params: {
      submitStreamID: stream.submitStreamID,
    },
  });
}
function goToInstance(stream: StreamsListItem) {
  if (!checkAdminPermission({ any: [MergePermission.SubmitCenterInstanceStatus] }, permissionData?.permissionInfo)) {
    return;
  }
  router.push({
    name: PlatformEnterPoint.Instance,
    params: {
      submitStreamID: stream.submitStreamID,
    },
  });
}
</script>

<style lang="less" scoped>
@import (reference) '@hg-tech/forgeon-style/vars.less';

.streams-list {
  &-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 16px;
    border: 1px solid @FO-Container-Stroke1;
    border-radius: 8px;
    background-color: @FO-Container-Fill1;

    &:hover {
      border-color: @FO-Brand-Primary-Default;
    }

    &:not(:last-child) {
      margin-bottom: 8px;
    }
  }
}
</style>
