import { createRequestService } from '@hg-tech/oasis-common';
import { store } from '../store/pinia.ts';
import { useUserAuthStore } from '../store/modules/userAuth.ts';
import { useRouteNavigationStore } from '../store/modules/routeNavigation.ts';

const authStore = useUserAuthStore(store);
const routeNavigationStore = useRouteNavigationStore(store);

export const requestService = createRequestService(
  {
    authTokens: [
      {
        accessTokenKey: 'Access-Token',
        getAccessToken: () => authStore.userAuthInfo?.privateToken,
      },
      {
        accessTokenKey: 'X-Token',
        getAccessToken: () => authStore.userAuthInfo?.accessToken,
        newTokenKey: 'new-token',
        setNewToken: authStore.userAuthInfo?.setAccessToken,
      },
    ],
    onUnauthorized() {
      routeNavigationStore.onUnauthorized();
    },
    onForbidden() {
      // 后端未符合error code 规范，先注释掉
      // routeNavigationStore.onForbidden();
    },
  },
  {
    baseURL: `${import.meta.env.VITE_BASE_API_ORIGIN}/api` /* TODO 推荐使用 cors 方式访问后端 API， */,
  },
);
