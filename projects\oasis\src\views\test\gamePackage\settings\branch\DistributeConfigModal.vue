<template>
  <BasicModal title="分支分类配置" :class="prefixCls" @register="register" @ok="ok">
    <div
      class="c-FO-Content-Text2 mb flex flex-col items-center"
    >
      <div>添加分类用于分类显示分支，</div>
      <div>删除分类时若该分类下有分支，则会划分到未分类中。</div>
    </div>
    <div>
      <ScrollContainer>
        <div :key="keyNum" class="max-h-300px px-4" :class="`${prefixCls}__additional-Info`">
          <div v-for="(item, index) in distribute" :key="item.ID || item.mark" :class="`${prefixCls}__distribute`" class="mb-2 flex cursor-pointer items-center gap-2 b-1 b-#D9D9D9 b-rd-5px px-2 py-2px dark:b-gray-700">
            <Icon
              icon="ic:round-drag-handle"
              class="c-FO-Content-Text2 cursor-grab"
              :class="`${prefixCls}__drag-btn`"
            />
            <div v-if="item.is_default" class="h-25px flex-1 line-height-25px">
              {{ item.name }}
            </div>
            <a-input v-else v-model:value="item.name" :maxlength="20" :bordered="false" type="text" size="small" class="h-25px flex-1 p-0!" />
            <div v-if="!isDrag">
              <div
                v-if="!item.is_default"
                class="i-icon-park-outline:close distribute-icon hidden cursor-pointer text-#8d8d8d hover:text-#222"
                @click="handleDelete(index)"
              />
            </div>
          </div>
        </div>
      </ScrollContainer>
      <div class="mx-4 my-2 flex cursor-pointer items-center justify-center gap-2 b-1 b-#D9D9D9 b-rd-5px py-2px dark:b-gray-700" @click="handleAdd">
        <div class="i-icon-park-outline:plus" />
        <div>添加分类</div>
      </div>
    </div>
    <div
      v-if="hasError"
      class="my text-center c-#AD0000"
    >
      {{ errorFont }}
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { useDesign } from '/@/hooks/web/useDesign';
import { BasicModal, useModalInner } from '/@/components/Modal';
import Icon from '/@/components/Icon';
import { nextTick, ref, watch } from 'vue';
import { useSortable } from '/@/hooks/web/useSortable';
import { isNullOrUnDef } from '/@/utils/is';
import { ScrollContainer } from '/@/components/Container';
import {
  getPkgClassList,
  setPkgClassList,
} from '/@/api/page/test';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { getAllPaginationList } from '/@/hooks/web/usePagination';

defineOptions({
  name: 'DistributeConfigModal',
});

const emit = defineEmits(['success']);
const { prefixCls } = useDesign('distribute-config-modal');
const userStore = useUserStoreWithOut();
const distribute = ref();
const keyNum = ref<number>(1);
const hasError = ref(false);
const isDrag = ref(false);
const errorFont = ref('');
const [register, { closeModal, redoModalHeight }] = useModalInner(async () => {
  initDrag();

  // 查看数据
  const { list } = await getAllPaginationList((p) => getPkgClassList(
    userStore.getProjectId,
    {
      ...p,
      showAll: true,
    },
  ));

  distribute.value = list || [];
  redoModalHeight();
});

function initDrag() {
  nextTick(() => {
    const el = document.querySelector(`.${prefixCls}__additional-Info`) as HTMLElement;
    const { initSortable } = useSortable(el, {
      handle: `.${prefixCls}__drag-btn`,
      onStart: () => {
        isDrag.value = true;
      },
      onEnd: async ({ oldIndex, newIndex }) => {
        if (isNullOrUnDef(oldIndex) || isNullOrUnDef(newIndex) || oldIndex === newIndex) {
          return;
        }

        // 排序接口
        const currentGroup = distribute.value[oldIndex];

        distribute.value.splice(oldIndex, 1);
        distribute.value.splice(newIndex, 0, currentGroup);

        setTimeout(() => {
          keyNum.value++;
          isDrag.value = false;
        });
      },
    });

    initSortable();
  });
}

// 添加分类
function handleAdd() {
  distribute.value.push({
    name: '',
    mark: Math.floor(Math.random() * 10000000) + String(new Date().getTime()),

  });
  redoModalHeight();
}

function handleDelete(index: number) {
  distribute.value.splice(index, 1);
}

async function ok() {
  // 只保留distribute.value的name和ID
  const data = distribute.value.map((item) => ({
    name: item.name.replace(/\s*/g, ''),
    id: item.ID,
  }));

  const res = await setPkgClassList(userStore.getProjectId, { pkgClasses: data });

  if (res?.code !== 7) {
    closeModal();
    emit('success');
    userStore.setGamestoreOpenClassList(userStore.getGamestoreOpenClassList.concat(res?.createdIds || []));
    userStore.setGamestoreOpenClassList(userStore.getGamestoreOpenClassList.filter((item) => !(res?.deletedIds || []).includes(item)));
  } else {
    hasError.value = true;
    errorFont.value = res?.msg || '操作失败';
    redoModalHeight();
  }
}

watch(() => keyNum.value, () => {
  initDrag();
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-distribute-config-modal';
.@{prefix-cls} {
  &__distribute:hover {
    .distribute-icon {
      display: block;
    }
  }
}
</style>
