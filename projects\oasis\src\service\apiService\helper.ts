import { useUserStoreWithOut } from '../../store/modules/user.ts';
import { router } from '../../router';
import { PlatformEnterPoint, useForgeOnSider } from '@hg-tech/oasis-common';
import { FORGEON_MENU_FOLDED_KEY } from '/@/enums/cacheEnum.ts';
import { Persistent } from '/@/utils/cache/persistent.ts';

const { collapsed } = useForgeOnSider();

export async function handleUnauthorized() {
  const userStore = useUserStoreWithOut();
  userStore.setToken(undefined);
  userStore.setProjectId(undefined);
  await userStore.logout(true);
}

export async function handleForbidden() {
  if (router.currentRoute.value.name !== PlatformEnterPoint.Forbidden) {
    await router.replace({
      name: PlatformEnterPoint.Forbidden,
      query: {
        status: '403',
        // 路由无法解析 origin，也无法正确解析 query
        redirect: window.location.pathname,
      },
    });
  }
}

export function handleMenuExpand(expend: boolean) {
  collapsed.value = !expend;
  Persistent.setLocal(FORGEON_MENU_FOLDED_KEY, '0');
}
