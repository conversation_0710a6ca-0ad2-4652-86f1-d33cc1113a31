import type { DescItem } from '/@/components/Description';
import type { FormSchema } from '/@/components/Table';
import { AssetTypeEnum, DeviceAccessLevelEnum, DeviceFsmStateEnum, DeviceOptLogOperationTypeEnum, DeviceUsageEnum, MobileTypeEnum } from '/@/api/page/model/deptAssetModel';
import { matchPinYin, userFilterOption } from '/@/hooks/system/useUserList';
import type { SelectProps } from 'ant-design-vue';
import { getPopupContainer } from '/@/utils';
import { cloudDeviceStatusList } from '../cloud/device.data';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';

/** 设备管理tab列表 */
export const settingTabList = [
  { title: '设备管理', name: PlatformEnterPoint.DeviceManagement },
  { title: '操作日志', name: PlatformEnterPoint.DeviceManagementLogs },
  { title: '报障列表', name: PlatformEnterPoint.DeviceManagementFaultList },
  { title: '管理员配置', name: PlatformEnterPoint.DeviceManagementAdminConfig },
];

/** 设备操作日志类型下拉 */
export const deviceOptLogOperationTypeOptions = [
  { label: '申请使用', value: DeviceOptLogOperationTypeEnum.APPLY },
  { label: '同意审批', value: DeviceOptLogOperationTypeEnum.APPROVE },
  { label: '拒绝审批', value: DeviceOptLogOperationTypeEnum.REJECT },
  { label: '确认借出', value: DeviceOptLogOperationTypeEnum.CONFIRM_RECEIVE },
  { label: '申请归还', value: DeviceOptLogOperationTypeEnum.APPLY_RETURN },
  { label: '确认归还', value: DeviceOptLogOperationTypeEnum.CONFIRM_RETURN },
  { label: '尚未归还', value: DeviceOptLogOperationTypeEnum.REJECT_RETURN },
  { label: '直接借用', value: DeviceOptLogOperationTypeEnum.DIRECT_BORROW },
  { label: '直接归还', value: DeviceOptLogOperationTypeEnum.DIRECT_RETURN },
  { label: '更改归还日期', value: DeviceOptLogOperationTypeEnum.CHANGE_RETURN_TIME },
];

/** 流通级别列表 */
export const accessLevelList = [
  { label: '公共', value: DeviceAccessLevelEnum.PUBLIC },
  { label: '部门内流通', value: DeviceAccessLevelEnum.DEPT },
  { label: '项目内流通', value: DeviceAccessLevelEnum.PROJECT },
  { label: '不可借用', value: DeviceAccessLevelEnum.UNAVAILABLE },
];

/** 手机归属 */
export const mobileOwnerOptions = [
  { title: '全部', name: 'allDevice' },
  { title: '我借用的', name: 'myOccupy' },
  { title: '我管理的', name: 'myDevice' },
];

/** 手机状态 */
export const mobileStatusOptions = [
  { label: '空闲', value: DeviceFsmStateEnum.FREE },
  { label: '申请中', value: DeviceFsmStateEnum.APPLYING },
  { label: '领用中', value: DeviceFsmStateEnum.BORROWING },
  { label: '使用中', value: DeviceFsmStateEnum.USING },
  { label: '归还中', value: DeviceFsmStateEnum.RETURNING },
  { label: '离线', value: DeviceFsmStateEnum.OFFLINE },
];

/** 排序类型 */
export const sortTypeOptions = [
  { label: '入库时间', value: 'inTime' },
  { label: '跑分', value: 'score' },
  { label: '预计归还时间', value: 'returnTime' },
  { label: '已使用天数', value: 'useDays' },
];

/** 资产类型下拉 */
export const assetTypeOptions = [
  { label: '安卓', value: AssetTypeEnum.ANDROID },
  { label: 'iOS', value: AssetTypeEnum.IOS },
  // { label: 'PC', value: AssetTypeEnum.PC },
  // { label: '显示器', value: AssetTypeEnum.DISPLAY },
  // { label: '其他', value: AssetTypeEnum.OTHER },
];
/** 资产用途下拉 */
export const assetUsageOptions = [
  { label: '个人使用', value: DeviceUsageEnum.PERSONAL },
  { label: '外包设备', value: DeviceUsageEnum.OUTSOURCE },
  { label: '部门公共设备', value: DeviceUsageEnum.DEPT_PUBLIC },
];
/** 手机类型下拉 */
export const mobileTypeOptions = [
  { label: '商用机', value: MobileTypeEnum.BUSINESS },
  // 只有安卓有开发机
  { label: '开发机', value: MobileTypeEnum.DEVELOP, assetType: AssetTypeEnum.ANDROID, disabled: false },
];

export const formSchema: FormSchema[] = [
  {
    field: 'onlineStatus',
    label: '在线状态',
    component: 'BorderBox',
    children: [
      {
        field: 'online',
        label: '',
        colProps: { span: 24, offset: 16, pull: 16 },
        component: 'RadioGroup',
        defaultValue: true,
        componentProps: {
          options: [
            {
              label: '上线',
              value: true,
            },
            {
              label: '离线',
              value: false,
            },
          ],
        },
        required: true,
      },
    ],
  },
  {
    field: 'deviceInformation',
    label: '设备信息',
    component: 'BorderBox',

    children: [
      {
        label: '资产编号',
        field: 'assetNo',
        component: 'Input',
        colProps: { span: 7, offset: 16, pull: 16 },
        componentProps: {
          placeholder: '请输入资产编号',
        },
        required: true,
      },
      {
        label: '设备名称',
        field: 'deviceName',
        component: 'Input',
        componentProps: {
          placeholder: '请输入设备名称',
        },
        required: true,
        colProps: { span: 7, offset: 1, pull: 1 },
      },
      {
        label: '所属人',
        field: 'ownerID',
        component: 'UserSelect',
        colProps: { span: 7, offset: 1, pull: 1 },
        required: true,
      },
      {
        label: '所属部门',
        field: 'deptID',
        component: 'TreeSelect',
        colProps: { span: 7, offset: 1, pull: 1 },
        componentProps: {
          placeholder: '请选择所属部门',
          fieldNames: {
            label: 'name',
            key: 'ID',
            value: 'ID',
          },
          treeNodeLabelProp: 'orgPath',
        },
        required: true,
      },
      {
        field: 'usageID',
        label: '资产用途',
        component: 'Select',
        colProps: { span: 7, offset: 1, pull: 1 },
        required: true,
      },
      {
        label: '资产分类',
        field: 'assetType',
        component: 'Select',
        colProps: { span: 7, offset: 1, pull: 1 },
        componentProps: {
          placeholder: '请选择资产分类',
          showSearch: true,
          allowClear: true,
          options: assetTypeOptions,
        },
        required: true,
      },
      {
        field: 'mobileType',
        label: '设备类型',
        component: 'Select',
        colProps: { span: 7, offset: 1, pull: 1 },
        componentProps: {
          placeholder: '请选择手机类型',
          showSearch: true,
          allowClear: true,
          options: mobileTypeOptions,
        },
        ifShow: ({ values }) => {
          return [1, 2].includes(values?.assetType);
        },
        required: true,
      },
      {
        label: '流通级别',
        field: 'accessLevel',
        component: 'Select',
        colProps: { span: 7, offset: 1, pull: 1 },
        defaultValue: DeviceAccessLevelEnum.PUBLIC,
        componentProps: {
          options: accessLevelList,
        },
        required: true,
      },
      {
        label: '流通部门范围',
        field: 'deptIds',
        component: 'TreeSelect',
        colProps: { span: 7, offset: 1, pull: 1 },
        componentProps: {
          treeCheckable: true,
          showSearch: false,
          showArrow: true,
          placeholder: '请选择流通部门范围',
          fieldNames: {
            label: 'name',
            key: 'ID',
            value: 'ID',
          },
          treeNodeLabelProp: 'orgPath',
          showCheckedStrategy: 'SHOW_PARENT',
          maxTagCount: 3,
        },
        ifShow: ({ values }) => {
          return values?.accessLevel === DeviceAccessLevelEnum.DEPT;
        },
        required: true,
      },
      {
        label: '项目范围',
        field: 'projectIds',
        component: 'Select',
        colProps: { span: 7, offset: 1, pull: 1 },
        componentProps: {
          placeholder: '请选择项目范围',
          mode: 'multiple',
          optionFilterProp: 'name',
          fieldNames: {
            label: 'name',
            key: 'ID',
            value: 'ID',
          },
          maxTagCount: 3,
        },
        ifShow: ({ values }) => {
          return values?.accessLevel === DeviceAccessLevelEnum.PROJECT;
        },
        required: true,
      },
      {
        label: '备注',
        field: 'remark',
        component: 'Input',
        colProps: { span: 24 },
        slot: 'remark',
      },
    ],
  },
  {
    field: 'hardwareParam',
    label: '硬件参数',
    component: 'BorderBox',
    children: [
      {
        label: 'UDID',
        field: 'deviceUDID',
        component: 'Input',
        colProps: { span: 7, offset: 1, pull: 1 },
        componentProps: {
          placeholder: '请输入UDID',
          maxLength: 50,
        },
        ifShow: ({ values }) => {
          return values?.assetType;
        },
      },
      {
        label: '唯一标识',
        field: 'deviceUID',
        component: 'Input',
        colProps: { span: 7, offset: 1, pull: 1 },
        componentProps: {
          placeholder: '请输入设备唯一标识',
          disabled: true,
        },
        required: true,

      },
      {
        label: '设备型号',
        field: 'deviceType',
        component: 'Input',
        colProps: { span: 7, offset: 1, pull: 1 },
        componentProps: {
          placeholder: '请输入设备型号, 如: iPhone11 Pro',
          disabled: true,
        },
        required: true,
      },
      {
        label: '',
        field: 'brandID',
        component: 'Input',
        colProps: { span: 5 },
        slot: 'brandID',
        rules: [{ required: true, validator: (_, value) => {
          if (!value) {
            return Promise.reject(new Error('请选择设备品牌'));
          }
          return Promise.resolve();
        } }],
      },
      {
        label: '',
        field: 'addBrandID',
        slot: 'addBrandID',
        component: 'Input',
        colProps: { span: 2, offset: 1, pull: 1 },
      },
      {
        label: '系统版本',
        field: 'osVersion',
        component: 'Input',
        colProps: { span: 7, offset: 1, pull: 1 },
        componentProps: {
          placeholder: '请输入操作系统版本,如: 14.4.2(18D70)',
          disabled: true,
        },
        required: true,
      },
      {
        label: '定制版本',
        field: 'romVersion',
        component: 'Input',
        colProps: { span: 7, offset: 1, pull: 1 },
        componentProps: {
          placeholder: '请输入定制版本,如: HyperOS 2',
          disabled: true,
        },
      },
      {
        label: '',
        field: 'chipsetID',
        component: 'Input',
        slot: 'chipsetID',
        rules: [{ required: true, validator: (_, value) => {
          if (!value) {
            return Promise.reject(new Error('请选择CPU'));
          }
          return Promise.resolve();
        } }],
        colProps: {
          span: 5,
        },
      },
      {
        label: '',
        field: 'addChipsetID',
        slot: 'addChipsetID',
        component: 'Input',
        colProps: { span: 2, offset: 1, pull: 1 },
      },
      {
        label: 'GPU',
        field: 'gpuType',
        component: 'Input',
        colProps: { span: 7, offset: 1, pull: 1 },
        componentProps: {
          placeholder: '请输入GPU',
        },
        required: true,
      },
      {
        label: '运行内存',
        field: 'ram',
        component: 'InputNumber',
        colProps: { span: 7, offset: 1, pull: 1 },
        componentProps: {
          placeholder: '请输入运行内存',
          addonAfter: 'GB',
          precision: 0,
          min: 0,
        },
        required: true,
      },
      {
        label: '机身内存',
        field: 'memory',
        component: 'InputNumber',
        colProps: { span: 7, offset: 1, pull: 1 },
        componentProps: {
          placeholder: '请输入机身内存',
          addonAfter: 'GB',
          precision: 0,
          min: 0,
        },
        required: true,
      },
      {
        label: '分辨率',
        field: 'resolutionX',
        component: 'Input',
        slot: 'resolution',
        colProps: { span: 7, offset: 1, pull: 1 },
        dynamicRules: ({ values }) => {
          return [
            {
              required: true,
              validator(_, value) {
                if (!value || !values.resolutionY) {
                  return Promise.reject(new Error('请输入屏幕分辨率'));
                }
                return Promise.resolve();
              },
            },
          ];
        },
      },
      {
        label: '',
        field: 'resolutionY',
        component: 'Input',
        show: false,
      },

    ],
  },
];
export const showFormSchema: DescItem[] = [
  {
    label: '设备名称',
    field: 'deviceName',
    labelMinWidth: 60,
    contentMinWidth: 160,
  },
  {
    label: '唯一标识',
    field: 'deviceUID',
    labelMinWidth: 60,
    contentMinWidth: 160,
  },
  {
    label: '设备型号',
    field: 'deviceType',
  },
  {
    label: '设备品牌',
    field: 'brandID',
  },
  {
    label: '资产编号',
    field: 'assetNo',
  },
  {
    label: '资产分类',
    field: 'assetType',
  },
  {
    label: '所属人',
    field: 'ownerID',
  },
  {
    label: '所属部门',
    field: 'deptID',
  },
  {
    label: '资产用途',
    field: 'usage',
  },
  {
    field: 'mobileType',
    label: '设备类型',
  },
  {
    label: '流通级别',
    field: 'accessLevel',
    span: 24,
  },
  {
    label: '备注',
    field: 'remark',
    span: 24,
  },
];

/** 筛选选项方法 */
const filterOption: SelectProps['filterOption'] = (inputVal, option) => {
  const input = inputVal?.toString()?.toLowerCase();
  return (
    option?.label?.toString()?.toLowerCase()?.includes(input)
    || matchPinYin(input, option?.label)
  );
};

/** 通用搜索选择器配置 */
const commonSearchSelectProps: SelectProps = {
  mode: 'multiple',
  maxTagCount: 1,
  maxTagTextLength: 3,
  showArrow: true,
  showSearch: true,
  allowClear: true,
  optionFilterProp: 'label',
  filterOption,
  getPopupContainer,
};

// 搜索表单配置

export const searchFormSchema: FormSchema[] = [
  {
    field: 'keyword',
    label: '搜索',
    component: 'Input',
    componentProps: {
      placeholder: '设备名称、资产编号等',
      allowClear: true,
    },
  },
  {
    field: 'fsmStateList',
    label: '状态',
    component: 'Select',
    componentProps: {

      ...commonSearchSelectProps,
    },
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      options: cloudDeviceStatusList,
      ...commonSearchSelectProps,
    },
  },
  {
    field: 'brandIDList',
    label: '设备品牌',
    component: 'Select',
    componentProps: {
      mode: 'multiple',
      ...commonSearchSelectProps,
    },
  },
  {
    field: 'deptIDList',
    label: '所属部门',
    component: 'TreeSelect',
    componentProps: {
      mode: 'multiple',
      maxTagCount: 1,
      maxTagTextLength: 2,
      showArrow: true,
      showSearch: false,
      allowClear: true,
      fieldNames: {
        label: 'name',
        key: 'ID',
        value: 'ID',
      },
      treeNodeLabelProp: 'orgPath',
      treeCheckable: true,
      showCheckedStrategy: 'SHOW_PARENT',
      dropdownMatchSelectWidth: false,
      getPopupContainer,
    },
  },
  {
    field: 'ownerIDList',
    label: '所属人',
    component: 'Select',
    componentProps: {
      ...commonSearchSelectProps,
      filterOption: userFilterOption,
    },
  },
  {
    field: 'ownerAdminIDList',
    label: '所属人',
    component: 'Select',
    componentProps: {
      ...commonSearchSelectProps,
      filterOption: userFilterOption,
    },
  },
  {
    field: 'usageIDList',
    label: '资产用途',
    component: 'Select',
    componentProps: {
      ...commonSearchSelectProps,
    },
  },
  {
    field: 'currentUserIDList',
    label: '使用人',
    component: 'Select',
    componentProps: {
      ...commonSearchSelectProps,
      filterOption: userFilterOption,
    },
  },
  {
    field: 'mobileTypeList',
    label: '设备类型',
    component: 'Select',
    componentProps: {
      options: mobileTypeOptions,
      ...commonSearchSelectProps,
    },
  },
  {
    field: 'assetTypeList',
    label: '资产分类',
    component: 'Select',
    componentProps: {
      options: assetTypeOptions,
      ...commonSearchSelectProps,
    },
  },
  {
    field: 'chipsetIdList',
    label: 'CPU',
    component: 'Select',
    componentProps: {
      ...commonSearchSelectProps,
    },
  },
  {
    field: 'deviceTypeList',
    label: '设备型号',
    component: 'Select',
    componentProps: {
      ...commonSearchSelectProps,
    },
  },
  {
    field: 'gpuTypeList',
    label: 'GPU',
    component: 'Select',
    componentProps: {
      ...commonSearchSelectProps,
    },
  },
  {
    field: 'osVersionList',
    label: '系统版本',
    component: 'Select',
    componentProps: {
      ...commonSearchSelectProps,
    },
  },
  {
    field: 'romVersionList',
    label: '定制版本',
    component: 'Select',
    componentProps: {
      ...commonSearchSelectProps,
    },
  },
  {
    field: 'memList',
    label: '机身内存',
    component: 'Select',
    componentProps: {
      ...commonSearchSelectProps,
      filterOption: null,
    },
  },
  {
    field: 'ramList',
    label: '运行内存',
    component: 'Select',
    componentProps: {
      ...commonSearchSelectProps,
      filterOption: null,
    },
  },
  {
    field: 'resolutionList',
    label: '分辨率',
    component: 'Select',
    componentProps: {
      ...commonSearchSelectProps,
    },
  },
];

export interface cardContentListItem {
  label: string;
  value: string;
  unit?: string;
  extraValue?: string;
  cardHidden?: boolean;
}

export const cardContentList: cardContentListItem[] = [
  {
    label: 'UDID：',
    value: 'deviceUDID',
    cardHidden: true,
  },
  {
    label: '系统：',
    value: 'romVersion',
    extraValue: 'osVersion',
  },
  {
    label: 'CPU：',
    value: 'chipset.brand.nameCN',
    extraValue: 'chipset.socName',
  },
  {
    label: 'GPU：',
    value: 'gpuType',
  },
  {
    label: '运行内存：',
    value: 'ram',
    unit: 'GB',
  },
  {
    label: '机身内存：',
    value: 'memory',
    unit: 'GB',
  },
  {
    label: '分辨率：',
    value: 'resolution',
  },

];
