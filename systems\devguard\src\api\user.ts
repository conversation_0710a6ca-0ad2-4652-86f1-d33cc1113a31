import type { ApiServiceMergeBaseConfig } from '@hg-tech/api-schema-merge';
import { type CommonBaseRes, createRequestService } from '@hg-tech/oasis-common';
import { useRouteNavigationStore } from '../store/modules/routeNavigation';
import { useUserAuthStore } from '../store/modules/userAuth';
import { store } from '../store/pinia';

export interface SysUserInfo {
  /**
   * 鹰角id
   */
  hgId?: string;
  /** 鹰角账号 */
  hgAccount?: string;
  name?: string;
  nickname?: string;
  /**
   * 组织id
   */
  organization?: number;
  /**
   * 组织编码
   */
  organizationNo?: string;
  /**
   * 组织路径
   */
  orgPath?: string;
  /**
   * 企业邮箱
   */
  enterpriseEmail?: string;
  avatar?: string;
  feishuUnionId?: string;
  feishuOpenId?: string;
  /**
   * 是否外包
   */
  outsourceFlag?: boolean;
}
export enum PermissionErrorCode {
  /**
   * 请求资源不存在
   */
  ErrBadRequestNotFound = 10004,
  /**
   * 内部错误
   */
  ErrServiceInternalUnknown = 10010,
  /**
   * 配置错误
   */
  ErrServiceInternalConfigEmpty = 10011,
  /**
   * 重复操作
   */
  ErrServiceInternalRepeatOperator = 10013,
  /**
   * 代理 stable diffusion 错误
   */
  ErrServiceInternalProxySD = 10100,
  /**
   * 代理 chat_gpt 错误
   */
  ErrServiceInternalProxyGPT = 10101,
  /**
   * DB 错误
   */
  ErrServiceInternalDB = 10110,
  /**
   * Redis 错误
   */
  ErrServiceInternalRedis = 10111,
  /**
   * OSS 错误
   */
  ErrServiceInternalOSS = 10120,
}
const authStore = useUserAuthStore(store);
const routeNavigationStore = useRouteNavigationStore(store);

export const requestService = createRequestService<ApiServiceMergeBaseConfig>(
  {
    authTokens: [
      {
        accessTokenKey: 'Access-Token',
        getAccessToken: () => authStore.userAuthInfo?.privateToken,
      },
      {
        accessTokenKey: 'X-Token',
        getAccessToken: () => authStore.userAuthInfo?.accessToken,
        newTokenKey: 'new-token',
        setNewToken: authStore.userAuthInfo?.setAccessToken,
      },
    ],
    onUnauthorized() {
      routeNavigationStore.onUnauthorized();
    },
    onForbidden() {
      // 后端未符合error code 规范，先注释掉
      // routeNavigationStore.onForbidden();
    },
  },
  {
    baseURL: import.meta.env.VITE_BASE_API_ORIGIN_PERMISSION_CENTER,
  },
);

export type PermissionBaseRes<T = never> = CommonBaseRes<T, PermissionErrorCode>;

export const queryUserList = requestService.GET<
  {
    /**
     * 必填，姓名/昵称/邮箱/拼音复合搜索
     */
    query: string;
  },
  Record<string, never>,
  PermissionBaseRes<SysUserInfo[]>
>(`/api/auth/v1/user/search`);
