import type { Options } from 'sortablejs';
import type Sortable from 'sortablejs';
import type { MaybeRef } from 'vue';
import { nextTick, ref, unref, watch } from 'vue';

export function useSortable(elRef: MaybeRef<HTMLElement>, options?: Options) {
  const sortableRef = ref<Sortable>();

  watch(() => unref(elRef), async (el, _, onCleanup) => {
    if (el) {
      await nextTick();
      const { default: SortableClass } = await import('sortablejs');
      const ins = SortableClass.create(el, {
        animation: 500,
        delay: 400,
        delayOnTouchOnly: true,
        ...options,
      });
      sortableRef.value = ins;
      onCleanup(() => {
        ins.destroy();
      });
    }
  }, { immediate: true });

  return { sortableRef };
}
