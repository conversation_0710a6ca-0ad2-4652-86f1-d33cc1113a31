<template>
  <ConfigProvider :locale="zhCN" :theme="themeConfig">
    <AppProvider>
      <RouterView />
    </AppProvider>
  </ConfigProvider>
</template>

<script lang="ts" setup>
import { ConfigProvider } from 'ant-design-vue';
import { AppProvider } from '/@/components/Application';
import { useTitle } from '/@/hooks/web/useTitle';
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import updateCheckImg from '/@/assets/images/update-check.png';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import relativeTime from 'dayjs/plugin/relativeTime';
import duration from 'dayjs/plugin/duration';
import { checkVersion } from 'version-rocket';
import microApp from '@micro-zoe/micro-app';
import { computed } from 'vue';
import { useDarkModeTheme } from '/@/hooks/setting/useDarkModeTheme';
import { isProdMode } from '/@/utils/env';
import { AntdOverrideDark, AntdOverrideLight, ForgeonThemeCssVar, withCustomAntdTheme } from '@hg-tech/forgeon-style';
import {
  checkPermissionPass,
  preprocessFilePath,
  useForgeOnSider,
  useForgeonTrackCtx,
  useMicroAppProvide,
  usePermissionCtx,
  usePlatformConfigCtx,
  useRouteNavigationCtx,
  useUserAuthInfoCtx,
  useUserProfileInfoCtx,
} from '@hg-tech/oasis-common';
import { useUserStore } from './store/modules/user.ts';
import { handleForbidden, handleMenuExpand, handleUnauthorized } from './service/apiService/helper.ts';
import { getPermissionInfo, usePermissionInfo } from './service/permission/usePermission.ts';
import { useRootSetting } from './hooks/setting/useRootSetting.ts';
import { sendEvent, traceRouteChange } from './service/tracker/index.ts';
import { useGo } from './hooks/web/usePage.ts';
import { router } from './router/index.ts';
import { useCachedProjectList } from './hooks/useProjects.ts';
import { createRouterMatcher, stringifyQuery } from 'vue-router';
import { isEmpty } from 'lodash';

dayjs.extend(relativeTime);
dayjs.extend(duration);
dayjs.locale('zh-cn');

const go = useGo();
const userStore = useUserStore();
const { data: projectList } = useCachedProjectList({ loadImmediately: false });
const { getDarkMode } = useRootSetting();
const { collapsed } = useForgeOnSider();

// Listening to page changes and dynamically changing site titles
const { setTitle } = useTitle();
useMicroAppProvide(useUserAuthInfoCtx, computed(() => {
  return {
    accessToken: userStore.getToken,
    setAccessToken: userStore.setToken,
    privateToken: userStore.getAccessToken,
    doLogout() {
      userStore.logout();
    },
  };
}), microApp);
useMicroAppProvide(useRouteNavigationCtx, computed(() => ({
  onUnauthorized: handleUnauthorized,
  onForbidden: handleForbidden,
  onRouteNavigate: (path, params, query, options) => {
    const { openInNewTab } = options || {};
    const matcher = createRouterMatcher([], {});
    matcher.addRoute({
      path,
      name: 'TempRouteNavigation',
      component: {},
    });
    const targetUrl = matcher.resolve({ name: 'TempRouteNavigation', params }, router.currentRoute.value).path;
    const queryString = isEmpty(query) ? '' : `?${stringifyQuery(query || {})}`;
    const fullpath = targetUrl + queryString;
    matcher.clearRoutes();
    if (openInNewTab) {
      window.open(new URL(fullpath, window.location.origin).href, '_blank');
    } else {
      router.push(fullpath);
    }
  },
})), microApp);
useMicroAppProvide(useUserProfileInfoCtx, computed(() => {
  const userInfo = userStore.getUserInfo;
  return ({
    ...userInfo,
    // 解决相对路径问题
    headerImg: preprocessFilePath(userInfo.headerImg),
  });
}), microApp);
const { permissionInfo } = usePermissionInfo();
useMicroAppProvide(usePermissionCtx, computed(() => ({
  permissionInfo: permissionInfo.value,
  async checkPermission(pDeclare, scope = window.__MICRO_APP_NAME__) {
    const permissionInfo = await getPermissionInfo(userStore.getProjectId);
    return checkPermissionPass({ ...pDeclare, scope }, permissionInfo);
  },
})), microApp);
useMicroAppProvide(usePlatformConfigCtx, computed(() => {
  return {
    theme: getDarkMode.value,
    isMenuExpanded: !collapsed.value,
    projectList: projectList.value?.map((item) => ({
      id: item.ID ?? 0,
      name: item.name ?? '',
    })) || [],
    currentProjectId: userStore.getProjectId,
    alias: projectList.value?.find((item) => item.ID === userStore.getProjectId)?.alias,
    changeMenuExpendStatus: handleMenuExpand,
    setDocumentTitle: (customTitle: string) => setTitle(customTitle),
    setCurrentProjectId: async (projectId: number | undefined) => {
      userStore.setProjectId(projectId);
      go({
        query: { ...router.currentRoute?.value.query, p: projectId },
      }, true);
      // 重新获取菜单
      await userStore.getUserInfoAction();
    },
  };
}), microApp);
useMicroAppProvide(useForgeonTrackCtx, computed(() => {
  return {
    sendPV: (params) => {
      traceRouteChange({
        ...params,
        isFromMicroApp: true,
      });
    },
    sendEvent,
  };
}), microApp);

// support Multi-language
const { isDark } = useDarkModeTheme();

const { antdThemeTokensDark, antdThemeTokensLight } = withCustomAntdTheme(
  {
    common: {
      colorFillAlter: 'ContainerFill2',
    },
    components: {
      Input: {
        colorBgContainer: 'ContainerFill1',
        colorBorder: 'ContainerStroke2',
      },
      InputNumber: {
        colorBgContainer: 'ContainerFill1',
        colorBorder: 'ContainerStroke2',
      },
      DatePicker: {
        colorBgContainer: 'ContainerFill1',
        colorBorder: 'ContainerStroke2',
      },
      Select: {
        colorBgContainer: 'ContainerFill1',
        colorBorder: 'ContainerStroke2',
      },
    },
  },
  {
    light: AntdOverrideLight,
    dark: AntdOverrideDark,
  },
);
const themeConfig = computed(() => (isDark.value ? antdThemeTokensDark : antdThemeTokensLight));

checkVersion(
  // config
  {
    // 1分钟检测一次版本
    pollingTime: 60000,
    checkOriginSpecifiedFilesUrl: [
      `${location.origin}/index.html`,
    ],
    clearIntervalOnDialog: true,
    // 版本实时更新检测，只作用于线上环境
    enable: isProdMode(),
  },
  // options
  {
    title: '提示',
    description: '该页面已发布新版',
    buttonText: '立即刷新',
    imageUrl: updateCheckImg,
    buttonStyle: `background-color: ${ForgeonThemeCssVar.BrandPrimaryDefault}`,
  },
);
</script>
