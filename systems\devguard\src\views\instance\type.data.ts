export enum instanceTypeEnum {
// 1 - 通用；2 - 编译；3 - 非编译
  Common = 1,
  Compile = 2,
  NonCompile = 3,
}

export const instanceTypeOptions = [
  {
    label: '通用',
    value: instanceTypeEnum.Common,
  },
  {
    label: '代码',
    value: instanceTypeEnum.Compile,
  },
  {
    label: '资产',
    value: instanceTypeEnum.NonCompile,
  },
];
// 提交的检查状态
export enum checkStateType {
// 1 - 检查中；2 - 通过；3 - 出错；4 - 中断
  Checking = 1,
  Passed = 2,
  Failed = 3,
  Interrupted = 4,
}
export const checkStateOptions = [
  {
    label: '检查中',
    value: checkStateType.Checking,
    color: 'c-FO-Functional-Warning1-Default',
  },
  {
    label: '通过',
    value: checkStateType.Passed,
    color: 'c-FO-Functional-Success1-Default',
  },
  {
    label: '出错',
    value: checkStateType.Failed,
    color: 'c-FO-Functional-Error1-Default',
  },
  {
    label: '中断',
    value: checkStateType.Interrupted,
    color: 'c-FO-Functional-Error1-Default',
  },
];
