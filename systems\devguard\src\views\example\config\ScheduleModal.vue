<template>
  <Modal
    :width="700" :open="show" :maskClosable="false" destroyOnClose centered :afterClose="modalDestroy" :footer="null"
    @cancel="() => modalCancel()"
  >
    <template #title>
      <div class="text-center">
        <span class="font-size-[16px] font-bold">
          <span>定时任务</span>
        </span>
      </div>
    </template>
    <div class="m-[20px] mb-0">
      <div class="h-400px">
        <div v-if="!isEdit" class="h-full">
          <div class="flex justify-end">
            <Button shape="round" size="small" @click="addSchedule">
              <div class="flex items-center gap-4px">
                <Icon :icon="addIcon" />
                新增
              </div>
            </Button>
          </div>
          <div v-if="!scheduleList.length" class="h-full flex items-center justify-center">
            暂无定时任务
          </div>
          <div v-else class="h-350px overflow-y-auto">
            <div v-for="item in scheduleList" :key="item.id" class="mt-10px flex justify-between b-rd-8px bg-FO-Container-Fill3 px-20px py-10px">
              <div>
                <div class="FO-Font-B14">
                  <span class="mr-4px">{{ item.triggerType === triggerType.Periodic ? `每周${getWeekDay(item.triggerCron!)}` : dayjs(item.triggerTimestamp).format('YYYY/MM/DD HH:mm') }}</span>
                  <span v-if="item.operationType === operationTypes.Update">更新实例</span>
                  <span v-if="item.operationType === operationTypes.Restart">重启实例</span>
                </div>
                <div class="FO-Font-b12">
                  适用实例：{{ exampleConfig?.filter((e) => item?.instanceIDs?.includes(e.id)).map((e) => e.name).join(',') }}
                </div>
              </div>
              <div class="flex gap-10px">
                <Button type="primary" size="small" @click="handleEditSchedule(item)">
                  修改
                </Button>
                <Popconfirm
                  title="确认删除？"
                  okText="确认"
                  cancelText="取消"
                  @confirm="deleteSchedule(item.id!)"
                >
                  <Button type="primary" class="bg-FO-Functional-Error1-Default!" size="small">
                    删除
                  </Button>
                </Popconfirm>
              </div>
            </div>
          </div>
        </div>
        <div v-else>
          <Button shape="round" size="small" @click="handleBack">
            <div class="flex items-center gap-4px">
              <Icon :icon="LeftIcon" />
              <span>返回</span>
            </div>
          </Button>
          <EditSchedule ref="editScheduleRef" class="h-300px" :exampleConfig="exampleConfig" :schedule="schedule" />
          <div class="flex justify-center">
            <Button type="primary" @click="handleSave">
              保存
            </Button>
            <Button class="ml-2 bg-FO-Functional-Error1-Default!" type="primary" @click="handleBack">
              取消
            </Button>
          </div>
        </div>
      </div>
    </div>
  </Modal>
</template>

<script lang="ts" setup>
import { Button, Modal, Popconfirm } from 'ant-design-vue';
import { type ModalBaseProps, useLatestPromise } from '@hg-tech/utils-vue';
import addIcon from '@iconify-icons/icon-park-outline/plus';
import LeftIcon from '@iconify-icons/icon-park-outline/left';
import { Icon } from '@iconify/vue';
import { ref } from 'vue';
import { traceClickEvent } from '../../../services/track';
import { TrackEventName } from '../../../constants/event';
import { type ExampleConfigItem, type scheduleItem, addScheduleApi, deleteScheduleApi, updateSchedule } from '../../../api';
import EditSchedule from './EditSchedule.vue';
import dayjs from 'dayjs';
import { useForgeonConfigStore } from '../../../store/modules/forgeonConfig';
import { store } from '../../../store/pinia';
import { useRouter } from 'vue-router';
import { operationTypes, triggerType, weekDayMap } from './type.data';

const props = defineProps<ModalBaseProps<{ updatedItem?: any }> & {
  scheduleList?: scheduleItem[];
  exampleConfig: ExampleConfigItem[];
  reload: () => Promise<scheduleItem[] | undefined>;
}>();

const { execute: deleteScheduleExecute } = useLatestPromise(deleteScheduleApi);
const { execute: addScheduleExecute } = useLatestPromise(addScheduleApi);
const { execute: updateScheduleExecute } = useLatestPromise(updateSchedule);
const forgeonConfig = useForgeonConfigStore(store);
const router = useRouter();
const routeParams = router.currentRoute.value.params;
const submitStreamID = routeParams.submitStreamID ? Number(routeParams.submitStreamID) : null;
const isEdit = ref(false);
const schedule = ref<scheduleItem>();
const editScheduleRef = ref();
const isUpdate = ref(false);
const scheduleList = ref(props.scheduleList ?? []);
function getWeekDay(weekDay: string) {
  if (!weekDay) {
    return '';
  }
  let str = '';
  weekDay.split(' ')[4].split(',').map(Number).sort().forEach((item) => {
    str += `${weekDayMap.find((e) => e.value === item)!.label}、`;
  });
  str += ` ${weekDay.split(' ')[1]}:${weekDay.split(' ')[0]}`;
  return str;
}
function addSchedule() {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_SCHEDULE_ADD);
  schedule.value = undefined;
  isUpdate.value = false;
  isEdit.value = true;
}
function handleEditSchedule(item: scheduleItem) {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_SCHEDULE_MODIFY);
  isEdit.value = true;
  isUpdate.value = true;
  schedule.value = item;
}
async function deleteSchedule(id: number) {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_SCHEDULE_DELETE);
  await deleteScheduleExecute({ id: forgeonConfig.currentProjectId! }, { recordID: id!, streamID: submitStreamID! });
}
async function handleBack() {
  isEdit.value = false;
  const res = await props.reload();
  scheduleList.value = res || [];
}
async function handleSave() {
  const params = await editScheduleRef.value.getParams();
  if (!params || !schedule.value) {
    return;
  }
  if (isUpdate.value) {
    await updateScheduleExecute({ id: forgeonConfig.currentProjectId! }, { ...params, recordID: schedule.value.id! });
  } else {
    await addScheduleExecute({ id: forgeonConfig.currentProjectId! }, params);
  }
  handleBack();
}
</script>

<style lang="less" scoped>
.file-select :deep(.ant-select-selector) {
  min-height: 60px;
  align-items: flex-start;
}
</style>
