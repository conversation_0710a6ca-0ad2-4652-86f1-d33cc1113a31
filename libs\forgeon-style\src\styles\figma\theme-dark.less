/* This file is automatically generated. DO NOT EDIT it manually. */

/** map theme vars to basic var for dark.  */
:root[data-theme='dark'] {
  --FO-Brand-Primary-Default: var(--Dark-Violet-6);
  --FO-Brand-Primary-Hover: var(--Dark-Violet-5);
  --FO-Brand-Primary-Active: var(--Dark-Violet-4);
  --FO-Brand-Primary-Disabled: var(--Dark-Violet-4);
  --FO-Brand-Secondary-Default: var(--Dark-Violet-1);
  --FO-Brand-Secondary-Hover: var(--Dark-Violet-2);
  --FO-Brand-Secondary-Active: var(--Dark-Violet-3);
  --FO-Brand-Secondary-Disabled: var(--Dark-Violet-1);
  --FO-Brand-Tertiary-Active: var(--Dark-Violet-1);
  --FO-Functional-Success1-Default: var(--Dark-Green-6);
  --FO-Functional-Success1-Hover: var(--Dark-Green-5);
  --FO-Functional-Success1-Active: var(--Dark-Green-4);
  --FO-Functional-Success1-Disabled: var(--Dark-Green-2);
  --FO-Functional-Success2-Default: var(--Dark-Green-2);
  --FO-Functional-Success2-Hover: var(--Dark-Green-3);
  --FO-Functional-Success2-Active: var(--Dark-Green-4);
  --FO-Functional-Success2-Disabled: var(--Dark-Green-2);
  --FO-Functional-Warning1-Default: var(--Dark-Orange-6);
  --FO-Functional-Warning1-Hover: var(--Dark-Orange-5);
  --FO-Functional-Warning1-Active: var(--Dark-Orange-4);
  --FO-Functional-Warning1-Disabled: var(--Dark-Orange-2);
  --FO-Functional-Warning2-Default: var(--Dark-Orange-1);
  --FO-Functional-Warning2-Hover: var(--Dark-Orange-3);
  --FO-Functional-Warning2-Active: var(--Dark-Orange-4);
  --FO-Functional-Warning2-Disabled: var(--Dark-Orange-2);
  --FO-Functional-Error1-Default: var(--Dark-Red-6);
  --FO-Functional-Error1-Hover: var(--Dark-Red-5);
  --FO-Functional-Error1-Active: var(--Dark-Red-4);
  --FO-Functional-Error1-Disabled: var(--Dark-Red-2);
  --FO-Functional-Error2-Default: var(--Dark-Red-1);
  --FO-Functional-Error2-Hover: var(--Dark-Red-3);
  --FO-Functional-Error2-Active: var(--Dark-Red-4);
  --FO-Functional-Error2-Disabled: var(--Dark-Red-2);
  --FO-Functional-Info1-Default: var(--Dark-Blue-6);
  --FO-Functional-Info1-Hover: var(--Dark-Blue-5);
  --FO-Functional-Info1-Active: var(--Dark-Blue-4);
  --FO-Functional-Info1-Disabled: var(--Dark-Blue-2);
  --FO-Functional-Info2-Default: var(--Dark-Blue-1);
  --FO-Functional-Info2-Hover: var(--Dark-Blue-3);
  --FO-Functional-Info2-Active: var(--Dark-Blue-4);
  --FO-Functional-Info2-Disabled: var(--Dark-Blue-1);
  --FO-Content-Text0: var(--Dark-Gray-1);
  --FO-Content-Text1: var(--Dark-Gray-15);
  --FO-Content-Text2: var(--Dark-Gray-10);
  --FO-Content-Text3: var(--Dark-Gray-7);
  --FO-Content-Text4: var(--Dark-Gray-5);
  --FO-Content-Icon0: var(--Dark-Gray-1);
  --FO-Content-Icon1: var(--Dark-Gray-14);
  --FO-Content-Icon2: var(--Dark-Gray-9);
  --FO-Content-Icon3: var(--Dark-Gray-6);
  --FO-Content-Icon4: var(--Dark-Gray-5);
  --FO-Content-Components1: var(--Dark-Gray-16);
  --FO-Content-Components2: var(--Dark-Gray-11);
  --FO-Content-Link-Default: var(--Dark-Blue-6);
  --FO-Content-Link-Hover: var(--Dark-Blue-5);
  --FO-Content-Link-Active: var(--Dark-Blue-4);
  --FO-Content-Link-Disabled: var(--Dark-Blue-2);
  --FO-Container-Mask0: var(--Basic-White-60);
  --FO-Container-Mask1: var(--Basic-Black-40);
  --FO-Container-Background: var(--Dark-Gray-0);
  --FO-Container-Background2: var(--Basic-Black-40);
  --FO-Container-Fill0: var(--Basic-Black-0);
  --FO-Container-Fill1: var(--Dark-Gray-1);
  --FO-Container-Fill2: var(--Dark-Gray-2);
  --FO-Container-Fill3: var(--Dark-Gray-3);
  --FO-Container-Fill4: var(--Dark-Gray-4);
  --FO-Container-Fill5: var(--Dark-Gray-5);
  --FO-Container-Fill6: var(--Dark-Gray-0);
  --FO-Container-Stroke0: var(--Dark-Gray-2);
  --FO-Container-Stroke1: var(--Dark-Gray-3);
  --FO-Container-Stroke2: var(--Dark-Gray-4);
  --FO-Container-Stroke3: var(--Dark-Gray-6);
  --FO-Container-Stroke4: var(--Dark-Gray-7);
  --FO-Datavis-Violet1: var(--Dark-Violet-7);
  --FO-Datavis-Violet2: var(--Dark-Violet-4);
  --FO-Datavis-Violet3: var(--Dark-Violet-1);
  --FO-Datavis-Blue1: var(--Dark-Blue-7);
  --FO-Datavis-Blue2: var(--Dark-Blue-4);
  --FO-Datavis-Blue3: var(--Dark-Blue-1);
  --FO-Datavis-Lightblue1: var(--Dark-Lightblue-6);
  --FO-Datavis-Lightblue2: var(--Dark-Lightblue-3);
  --FO-Datavis-Lightblue3: var(--Dark-Lightblue-1);
  --FO-Datavis-Teal1: var(--Dark-Teal-6);
  --FO-Datavis-Teal2: var(--Dark-Teal-3);
  --FO-Datavis-Teal3: var(--Dark-Teal-1);
  --FO-Datavis-Green1: var(--Dark-Green-6);
  --FO-Datavis-Green2: var(--Dark-Green-3);
  --FO-Datavis-Green3: var(--Dark-Green-1);
  --FO-Datavis-Lightgreen1: var(--Dark-Lightgreen-6);
  --FO-Datavis-Lightgreen2: var(--Dark-Lightgreen-3);
  --FO-Datavis-Lightgreen3: var(--Dark-Lightgreen-1);
  --FO-Datavis-Yellow1: var(--Dark-Yellow-5);
  --FO-Datavis-Yellow2: var(--Dark-Yellow-3);
  --FO-Datavis-Yellow3: var(--Dark-Yellow-1);
  --FO-Datavis-Orange1: var(--Dark-Orange-6);
  --FO-Datavis-Orange2: var(--Dark-Orange-4);
  --FO-Datavis-Orange3: var(--Dark-Orange-1);
  --FO-Datavis-Red1: var(--Dark-Red-7);
  --FO-Datavis-Red2: var(--Dark-Red-4);
  --FO-Datavis-Red3: var(--Dark-Red-1);
  --FO-Datavis-Pink1: var(--Dark-Pink-7);
  --FO-Datavis-Pink2: var(--Dark-Pink-4);
  --FO-Datavis-Pink3: var(--Dark-Pink-1);
  --FO-Datavis-Purple1: var(--Dark-Purple-7);
  --FO-Datavis-Purple2: var(--Dark-Purple-4);
  --FO-Datavis-Purple3: var(--Dark-Purple-1);
}
