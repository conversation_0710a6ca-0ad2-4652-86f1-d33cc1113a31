<template>
  <div class="mt-10px">
    <Form :labelCol="labelCol" :wrapperCol="wrapperCol" :colon="false">
      <FormItem label="操作类型" v-bind="validateInfos.operationType">
        <Select v-model:value="modelRef.operationType" :options="scheduleOperationTypeOptions" />
      </FormItem>
      <FormItem label="触发类型" v-bind="validateInfos.triggerType">
        <RadioGroup v-model:value="modelRef.triggerType">
          <Radio :value="1">
            触发单次
          </Radio>
          <Radio :value="2">
            周期触发
          </Radio>
        </RadioGroup>
      </FormItem>
      <FormItem v-if="modelRef.triggerType === triggerType.Once" label="触发时间" v-bind="validateInfos.triggerTimestamp">
        <DatePicker v-model:value="modelRef.triggerTimestamp" showTime />
      </FormItem>
      <FormItem v-if="modelRef.triggerType === triggerType.Periodic" label="触发时间" v-bind="validateInfos.triggerTime">
        <TimePicker
          v-model:value="modelRef.triggerTime"
        />
      </FormItem>
      <FormItem v-if="modelRef.triggerType === triggerType.Periodic" v-bind="validateInfos.triggerWeekDay" class="ml-100px w-full">
        <CheckboxGroup v-model:value="modelRef.triggerWeekDay" class="w-full flex">
          <Checkbox :value="1">
            周一
          </Checkbox>
          <Checkbox :value="2">
            周二
          </Checkbox>
          <Checkbox :value="3">
            周三
          </Checkbox>
          <Checkbox :value="4">
            周四
          </Checkbox>
          <Checkbox :value="5">
            周五
          </Checkbox>
          <Checkbox :value="6">
            周六
          </Checkbox>
          <Checkbox :value="7">
            周日
          </Checkbox>
        </CheckboxGroup>
      </FormItem>
      <FormItem label="执行实例" v-bind="validateInfos.instanceIDs">
        <Select v-model:value="modelRef.instanceIDs" mode="multiple" :options="instanceConfig" :fieldNames="{ label: 'name', value: 'id' }" />
      </FormItem>
    </Form>
  </div>
</template>

<script lang="ts" setup>
import { Checkbox, DatePicker, Form, FormItem, Radio, RadioGroup, Select, TimePicker } from 'ant-design-vue';
import { computed, reactive, watch } from 'vue';
import { operationTypes, scheduleOperationTypeOptions, triggerType } from './type.data';
import type { InstanceConfigItem, scheduleItem } from '../../../api';
import dayjs from 'dayjs';
import { useRouter } from 'vue-router';

const props = defineProps<{
  instanceConfig: InstanceConfigItem[];
  schedule?: scheduleItem;
}>();

const CheckboxGroup = Checkbox.Group;
const router = useRouter();
const routeParams = router.currentRoute.value.params;
const submitStreamID = routeParams.submitStreamID ? Number(routeParams.submitStreamID) : null;
const labelCol = { span: 4 };
const wrapperCol = { span: 20 };
const useForm = Form.useForm;
const modelRef = reactive({
  operationType: operationTypes.Update,
  triggerType: triggerType.Once,
  triggerTimestamp: dayjs().startOf('day'),
  triggerTime: dayjs(),
  triggerWeekDay: [] as number[],
  instanceIDs: [] as number[],
});
const rulesRef = reactive({
  operationType: [
    {
      required: true,
      message: '请选择操作类型',
    },
  ],
  triggerType: [
    {
      required: true,
      message: '请选择触发类型',
    },
  ],
  triggerTimestamp: [
    {
      required: computed(() => modelRef.triggerType === triggerType.Once),
      message: '请选择触发时间',
    },
  ],
  triggerTime: [
    {
      required: computed(() => modelRef.triggerType === triggerType.Periodic),
      message: '请选择触发时间',
    },
  ],
  triggerWeekDay: [
    {
      required: computed(() => modelRef.triggerType === triggerType.Periodic),
      message: '请选择触发时间',
    },
  ],
  instanceIDs: [
    {
      required: true,
      message: '请选择执行实例',
    },
  ],

});
const { validate, validateInfos } = useForm(modelRef, rulesRef);

watch(() => props.schedule, () => {
  modelRef.operationType = props.schedule?.operationType ?? operationTypes.Update;
  modelRef.triggerType = props.schedule?.triggerType ?? triggerType.Once;
  if (modelRef.triggerType === triggerType.Once) {
    modelRef.triggerTimestamp = props.schedule?.triggerTimestamp ? dayjs(props.schedule.triggerTimestamp) : dayjs().startOf('day');
  } else {
    modelRef.triggerTime = props.schedule?.triggerCron ? dayjs(`${props.schedule.triggerCron.split(' ')[1]}:${props.schedule.triggerCron.split(' ')[0]}`, 'HH:mm') : dayjs();
    modelRef.triggerWeekDay = props.schedule?.triggerCron ? props.schedule.triggerCron.split(' ')[4].split(',').map(Number) : [] as number[];
  }
  modelRef.instanceIDs = props.schedule?.instanceIDs ?? [] as number[];
}, { deep: true, immediate: true });

async function getParams() {
  await validate();
  if (!submitStreamID) {
    return;
  }
  const params: scheduleItem = {
    operationType: modelRef.operationType,
    triggerType: modelRef.triggerType,
    instanceIDs: modelRef.instanceIDs,
    streamID: submitStreamID,
  };

  if (modelRef.triggerType === triggerType.Once) {
    params.triggerTimestamp = dayjs(modelRef.triggerTimestamp).valueOf();
  } else {
    params.triggerCron = `${modelRef.triggerTime.minute()} ${modelRef.triggerTime.hour()} * * ${modelRef.triggerWeekDay.join(',')}`;
  }
  return params;
}
defineExpose({
  getParams,
});
</script>
