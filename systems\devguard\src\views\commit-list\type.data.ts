export enum SubmitStateType {
  Initial = 0,
  CreateCommit = 1,
  Submitting = 2,
  SubmittingSucceeded = 3,
  SubmittingFailed = 4,
}
export enum ResCheckStateType {
  NoResCheckState = 0,
  RequestSubmission = 1,
  GeneralInspection = 2,
  InstanceCheck = 3,
  InstancePass = 4,
  InstanceError = 5,
}
export enum ReviewStateType {
  NoReviewState = 0,
  CreateReview = 1,
  AwaitingReview = 2,
  ReviewResultsSucceeded = 3,
  ReviewResultsFailed = 4,
}
export enum CheckStateType {
  NoCheckState = 0,
  CreateApproval = 1,
  AwaitingApproval = 2,
  ApprovalResultsSucceeded = 3,
  ApprovalResultsFailed = 4,
}
export enum processTagKey {
  All = 0,
  Submitted = 1,
  Terminated = 2,
  InProcess = 3,
  PendingApproval = 4,
}
export const processTag = [
  { name: processTagKey.All, label: '全部' },
  { name: processTagKey.Submitted, label: '已提交' },
  { name: processTagKey.Terminated, label: '已终止' },
  { name: processTagKey.InProcess, label: '流程中' },
  { name: processTagKey.PendingApproval, label: '待审批' },
];
