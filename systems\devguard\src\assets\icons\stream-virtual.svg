<?xml version="1.0" encoding="UTF-8"?>
<svg id="_图层_2" data-name="图层 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 281.58 281.58">
  <defs>
    <style>
      .cls-1 {
        fill: #333;
      }

      .cls-2 {
        fill: none;
      }

      .cls-3 {
        fill: #666;
      }
    </style>
  </defs>
  <g id="_图层_1-2" data-name="图层 1">
    <rect class="cls-2" width="281.58" height="281.58"/>
    <rect class="cls-3" x="232.29" y="11.35" width="39.47" height="33.99" rx="6.9" ry="6.9" transform="translate(450.28 -129.83) rotate(135)"/>
    <rect class="cls-3" x="194.47" y="49.34" width="39.47" height="33.99" rx="6.9" ry="6.9" transform="translate(412.58 -38.23) rotate(135)"/>
    <rect class="cls-3" x="156.64" y="87.33" width="39.47" height="33.99" rx="6.9" ry="6.9" transform="translate(374.87 53.37) rotate(135)"/>
    <circle class="cls-1" cx="138.16" cy="142.55" r="61.34"/>
  </g>
</svg>