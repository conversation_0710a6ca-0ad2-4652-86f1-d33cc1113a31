<template>
  <div class="p-4-group-management">
    <div :class="prefixCls">
      <div :class="`${prefixCls}__header !mb-5 `">
        <LineTab
          :tabList="menuList"
          :defaultActiveTab="curActiveTab"
          tabMargin="36px"
          @change="handleChangeTab"
        />
        <div class="ml-8">
          <slot name="Introduction" />
        </div>
      </div>
      <div :class="`${prefixCls}__body`">
        <slot />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup name="P4GroupManagementTab">
import { computed, ref } from 'vue';
import LineTab from '/@/components/LineTab';
import { useDesign } from '/@/hooks/web/useDesign';
import { useGo } from '/@/hooks/web/usePage';
import { checkPermissionPass, PermissionPoint, PlatformEnterPoint } from '@hg-tech/oasis-common';
import { usePermissionInfo } from '../../../service/permission/usePermission.ts';

const props = defineProps({
  curChildTab: {
    type: String,
    default: '',
  },
});

const TAB_LIST = [
  {
    name: PlatformEnterPoint.P4CustomGroupManagement,
    icon: 'ant-design:usergroup-add-outlined',
    title: '自定义组',
    permissionDeclaration: {
      any: [PermissionPoint.P4CustomGroupManagement],
    },
  },
  {
    name: PlatformEnterPoint.P4LdapGroupManagement,
    icon: 'ant-design:gold-outlined',
    title: 'ldap组',
    permissionDeclaration: {
      any: [PermissionPoint.P4LdapGroupManagement],
    },
  },
];

const { prefixCls } = useDesign('p4-group-management-tab');

const { permissionInfo } = usePermissionInfo();
const go = useGo();
const menuList = computed(() => {
  return TAB_LIST.filter((item) => checkPermissionPass(item.permissionDeclaration, permissionInfo.value));
});
const curActiveTab = ref(props.curChildTab || menuList.value[0]?.name);

function handleChangeTab(tabName: string) {
  go({ name: tabName });
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-p4-group-management-tab';
.@{prefix-cls} {
  padding: 16px;

  &__header {
    display: flex;
    align-items: center;
    padding: 24px;
    background-color: @FO-Container-Fill1;
    border-radius: 8px;
  }
}
</style>
