import { type PropType, defineComponent } from 'vue';

const ResizeItem = defineComponent({
  name: 'ResizeItem',
  props: {
    resizeable: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    containerClass: {
      type: String as PropType<string>,
      default: '',
    },
    containerStyle: {
      type: Object as PropType<Record<string, string>>,
      default: () => ({}),
    },

    contentStyle: {
      type: Object as PropType<Record<string, string>>,
      default: () => ({}),
    },
    handlerStyle: {
      type: Object as PropType<Record<string, string>>,
      default: () => ({}),
    },

    onMouseDown: { type: Function as PropType<(e: MouseEvent) => void> },
    onResizeStart: { type: Function as PropType<(e: MouseEvent, edge: 'start' | 'end') => void> },
  },
  setup(props, { slots }) {
    return () => {
      const onHandleMouseDown = (e: MouseEvent) => {
        e.stopPropagation();
        if (props.resizeable) {
          props.onMouseDown?.(e);
        }
      };

      const onResizeStart = (e: MouseEvent, edge: 'start' | 'end') => {
        e.stopPropagation();
        if (props.resizeable) {
          props.onResizeStart?.(e, edge);
        }
      };

      return (
        <div class="resize-item select-none">
          <div
            class={['flex items-center overflow-hidden rd-8px bg-FO-Brand-Tertiary-Active', props.containerClass]}
            onMousedown={onHandleMouseDown}
            style={props.containerStyle}
          >
            {props.resizeable && (
              <div
                class="h-full w-10px flex-shrink-0 cursor-ew-resize"
                onMousedown={(e: MouseEvent) => onResizeStart(e, 'start')}
                style={props.handlerStyle}
              >
                {slots.prefix
                  ? slots.prefix()
                  : (
                    <div class="h-full w-10px bg-FO-Brand-Primary-Default" />
                  )}
              </div>
            )}
            <div class="h-full min-w-20px flex flex-grow-1 select-none items-center justify-center" style={props.contentStyle}>
              {slots.default?.()}
            </div>
            {props.resizeable && (
              <div
                class="h-full flex-shrink-0 cursor-ew-resize"
                onMousedown={(e: MouseEvent) => onResizeStart(e, 'end')}
                style={props.handlerStyle}
              >
                {slots.suffix
                  ? slots.suffix()
                  : (
                    <div class="h-full w-10px bg-FO-Brand-Primary-Default" />
                  )}
              </div>
            )}
          </div>
        </div>
      );
    };
  },
});

export { ResizeItem };
