export enum workStateType {
// 1 - 空闲；2 - 检查中；3 - 更新中；4 - 重启中；5 - 离线
  Idle = 1,
  Checking = 2,
  Updating = 3,
  Restarting = 4,
  Offline = 5,
}
export enum triggerType {
  // 1 - 触发单次；2 - 周期触发
  Once = 1,
  Periodic = 2,
}
export enum pendingStateType {
// 1 - 等待更新；2 - 等待重启；3 - 等待禁用；4 - 等待更新+禁用；5 - 等待重启+禁用
  Null = 0,
  WaitUpdate = 1,
  WaitRestart = 2,
  WaitDisable = 3,
  WaitUpdateAndDisable = 4,
  WaitRestartAndDisable = 5,
}
export const workStateOptions = [
  {
    label: '空闲',
    value: workStateType.Idle,
  },
  {
    label: '检查',
    value: workStateType.Checking,
  },
  {
    label: '更新',
    value: workStateType.Updating,
  },
  {
    label: '重启',
    value: workStateType.Restarting,
  },
  {
    label: '实例已下线',
    value: workStateType.Offline,
  },
];

export enum operationTypes {
  // 【 1, 2, 3】 - 预约 【更新、重启、禁用 】；【4，5，6】- 取消预约【更新、重启、禁用】; 【7 8 9】 - 执行【更新、重启、禁用】；10 - 修改；11 - 注册；12 - 启用；13 - 更新失败；14 - 重启失败
  Update = 1,
  Restart = 2,
  Disable = 3,
  CancelUpdate = 4,
  CancelRestart = 5,
  CancelDisable = 6,
  ExecuteUpdate = 7,
  ExecuteRestart = 8,
  ExecuteDisable = 9,
  Modify = 10,
  Register = 11,
  Enable = 12,
  UpdateFailed = 13,
  RestartFailed = 14,
}
export const operationTypeOptions = [
  {
    label: '预约更新',
    function: '更新',
    action: '预约',
    value: operationTypes.Update,
  },
  {
    label: '预约重启',
    function: '重启',
    action: '预约',
    value: operationTypes.Restart,
  },
  {
    label: '预约禁用',
    function: '禁用',
    action: '预约',
    value: operationTypes.Disable,
  },
  {
    label: '取消更新',
    function: '更新',
    action: '取消',
    value: operationTypes.CancelUpdate,
  },
  {
    label: '取消重启',
    function: '重启',
    action: '取消',
    value: operationTypes.CancelRestart,
  },
  {
    label: '取消禁用',
    function: '禁用',
    action: '取消',
    value: operationTypes.CancelDisable,
  },
  {
    label: '执行更新',
    function: '更新',
    action: '执行',
    value: operationTypes.ExecuteUpdate,
  },
  {
    label: '执行重启',
    function: '重启',
    action: '执行',
    value: operationTypes.ExecuteRestart,
  },
  {
    label: '执行禁用',
    function: '禁用',
    action: '执行',
    value: operationTypes.ExecuteDisable,
  },
  {
    label: '修改实例',
    function: '修改',
    value: operationTypes.Modify,
  },
  {
    label: '注册实例',
    function: '注册',
    value: operationTypes.Register,
  },
  {
    label: '启用实例',
    function: '启用',
    action: '执行',
    value: operationTypes.Enable,
  },
  {
    label: '更新失败',
    function: '更新失败',
    action: '执行',
    value: operationTypes.UpdateFailed,
  },
  {
    label: '重启失败',
    function: '重启失败',
    action: '执行',
    value: operationTypes.RestartFailed,
  },
];
export const scheduleOperationTypeOptions = [
  {
    label: '更新实例',
    value: operationTypes.Update,
  },
  {
    label: '重启实例',
    value: operationTypes.Restart,
  },
];

export const weekDayMap = [
  {
    label: '一',
    value: 1,
  },
  {
    label: '二',
    value: 2,
  },
  {
    label: '三',
    value: 3,
  },
  {
    label: '四',
    value: 4,
  },
  {
    label: '五',
    value: 5,
  },
  {
    label: '六',
    value: 6,
  },
  {
    label: '日',
    value: 7,
  },
];
