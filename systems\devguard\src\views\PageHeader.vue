<template>
  <PageHeader :title="`${pageTitle}${stream?.description}`" :subTitle="stream?.path" class="m-20px b-rd-8px bg-FO-Container-Fill1!" @back="goBack">
    <template #backIcon>
      <Icon :icon="LeftIcon" class="h-20px c-FO-Content-Icon1" title="返回" />
    </template>
    <template #extra>
      <slot name="extra" />
    </template>
  </PageHeader>
</template>

<script lang="ts" setup>
import { PageHeader } from 'ant-design-vue';
import { Icon } from '@iconify/vue';
import LeftIcon from '@iconify-icons/icon-park-outline/left';
import { computed, watch } from 'vue';
import { getStreamsInfo } from '../../src/api';
import { useForgeonConfigStore } from '../store/modules/forgeonConfig';
import { store } from '../store/pinia';
import { useLatestPromise } from '@hg-tech/utils-vue';

const props = withDefaults(defineProps<{
  submitStreamID?: number;
  pageTitle?: string;
}>(), {
  submitStreamID: 0,
  pageTitle: '',
});

const emits = defineEmits<{
  (e: 'back'): void;
}>();
const forgeonConfig = useForgeonConfigStore(store);
const { execute, data: streamsInfo } = useLatestPromise(getStreamsInfo);

const stream = computed(() => {
  return streamsInfo.value?.data?.data?.stream;
});
function goBack() {
  emits('back');
}

watch([() => forgeonConfig.currentProjectId, () => props.submitStreamID], async () => {
  if (!forgeonConfig.currentProjectId || !props.submitStreamID) {
    return;
  }
  await execute({ id: forgeonConfig.currentProjectId!, stream_id: props.submitStreamID }, {});
}, { immediate: true });
</script>
