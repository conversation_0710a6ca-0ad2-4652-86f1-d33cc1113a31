import type { MaybeVNode } from '@hg-tech/oasis-common';
import type { PagedQueryParam, PagedRes } from '../../../../../api/_common.ts';

export interface OrgStructureCellInfo<K extends string = string> {
  key: string;
  /**
   * 单元格标题
   */
  title?: MaybeVNode | ((kw?: string) => MaybeVNode);
  /**
   * 单元格副标题
   */
  subTitle?: MaybeVNode | ((kw?: string) => MaybeVNode);
  /**
   * 单元格图标
   */
  avatar?: string;
  /**
   * 项目类型，用于区分人员、部门和组
   */
  type?: K;
}

export interface OrgStructureSchemaBase<K extends string, T extends OrgStructureCellInfo<K> = OrgStructureCellInfo<K>> {
  type: K;
  /**
   * 渲染分类标题
   * @example 部门
   */
  title: string;
  /**
   * 统计信息渲染
   * @example n=> `${n}部门`
   */
  summary: (total: number) => string;
  /**
   * 统计合并目标类型
   * 当设置此项时，当前schema的统计数量会合并到指定类型的统计中
   * @example summaryMergeWith: 'org' 表示合并到org类型的统计中
   */
  summaryMergeWith?: K;
  /**
   * 获取初始化数据函数
   */
  initFunc?: (pageInfo: PagedQueryParam) => Promise<PagedRes<T>>;
  /**
   * 搜索函数
   */
  searchFunc: (pageInfo: PagedQueryParam, keyword?: string) => Promise<PagedRes<T>>;
}

export interface OrgStructureSchemaTree<K extends string, T extends OrgStructureCellInfo<K> = OrgStructureCellInfo<K>> extends OrgStructureSchemaBase<K, T> {
  /**
   * 可以下钻的节点类型列表
   * 只有在此列表中的节点类型才能进入下一层级
   */
  treeTypeList?: K[];
  initFunc?: (pageInfo: PagedQueryParam, parentId?: T['key']) => Promise<PagedRes<T>>;
  breadcrumbRender: (item: T) => MaybeVNode;
}

export type OrgStructureSchema<K extends string, T extends OrgStructureCellInfo<K> = OrgStructureCellInfo<K>> =
  | OrgStructureSchemaBase<K, T>
  | OrgStructureSchemaTree<K, T>;

export type OrgStructureValue<K extends string> = Record<K, OrgStructureCellInfo<K>['key'][]>;
export interface OrgStructurePayload<K extends string, T extends OrgStructureCellInfo<K> = OrgStructureCellInfo<K>> {
  key: K;
  data: T;
}
