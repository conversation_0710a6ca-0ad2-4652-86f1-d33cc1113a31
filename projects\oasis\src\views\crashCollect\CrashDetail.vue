<template>
  <div :class="prefixCls" @click="pageClick">
    <div class="relative h-80px flex items-center bg-FO-Container-Fill1 px-24px py-20px text-center font-size-20px font-bold">
      <div class="absolute flex cursor-pointer" @click="goBack()">
        <Icon icon="icon-park-outline:left" :size="24" />
        <div class="ml-2">
          返回
        </div>
      </div>
      <div class="flex-1">
        Crash详情
      </div>
    </div>
    <div class="m-4 rounded-md bg-FO-Container-Fill1 p-4">
      <div class="flex items-center justify-between">
        <div>
          <div class="flex items-center">
            <div class="font-size-20px font-bold">
              Crash-{{ crashDetail?.ID }}
            </div>
            <a-button
              v-if="!crashDetail?.bugID" shape="round" :loading="btnLoading"
              class="ml-3 border-0 line-height-1 h-20px! bg-#E85300! px-3! py-2px! color-white!" @click="lading()"
            >
              提单
            </a-button>
            <div
              v-if="crashDetail?.bugID" class="ml-3 cursor-pointer rounded-6 bg-#0071a1 px-3 py-2px color-white"
              @click="openWindow(crashDetail?.bugID!)"
            >
              已提单：ID{{
                crashDetail?.bugID.replace(
                  'https://project.feishu.cn/test-techcenter/issue/detail/',
                  '',
                )
              }}
            </div>
          </div>
          <CrashLabel :labelList="labelList" :item="crashDetail" :clickLabelId="clickLabelId" @clickAddLabel="clickAddLabel" />
        </div>

        <div
          v-if="crashDetail"
          class="relative h-32px flex cursor-pointer items-center justify-between font-bold"
          :style="`background: ${getBackgroundColor(crashDetail?.category?.name)};`"
          @click="openClassDetail(crashDetail?.category?.ID)"
        >
          <div :class="`${prefixCls}__rhombus`" />
          <div class="m-2 ml-5 color-#252525">
            {{ categoryName(crashDetail) }}
          </div>
        </div>
      </div>
      <div :class="`${prefixCls}__info py-4 px-6`">
        <div class="flex">
          <div class="flex-1">
            <div>触发人：{{ crashDetail?.equipment?.user }}</div>
            <div class="mt-2">
              设备名称：{{ crashDetail?.equipment?.deviceName }}
            </div>
            <div class="mt-2">
              引擎CL号：{{ crashDetail?.engineVersion?.clVersion }}
            </div>
            <div class="mt-2">
              工程CL号：{{ crashDetail?.streamClVersion ? crashDetail.streamClVersion : '' }}
            </div>
            <div class="mt-2">
              CPU型号：{{ crashDetail?.equipment?.equipmentHardware?.cpuName }}
            </div>
            <div class="mt-2">
              内存使用：{{ crashDetail?.software?.memoryUsed }}
            </div>
            <div class="mt-2">
              GPU型号：{{ crashDetail?.equipment?.equipmentHardware?.gpuName }}
            </div>
            <div class="mt-2">
              GPU驱动：{{ crashDetail?.software?.gpuDriverVersion }}
            </div>
          </div>

          <div class="flex-1">
            <div>触发时间：{{ crashDetail?.time }}</div>
            <div class="mt-2">
              操作系统：{{ crashDetail?.equipment?.equipmentHardware?.os }}
            </div>
            <div class="mt-2">
              引擎版本：{{ crashDetail?.engineVersion?.engineVersion }}
            </div>

            <div class="mt-2">
              引擎已上线：{{
                engineStateList.find((item) => item.ID === crashDetail?.engineStatus)?.status
              }}
            </div>
            <div class="mt-2">
              <div v-if="crashDetail?.args">
                引擎参数：{{ crashDetail?.args }}
              </div>
              <div v-else style="height: 20px" />
            </div>
            <div class="mt-2">
              内存总量：{{ crashDetail?.equipment?.equipmentHardware?.memory }}
            </div>
            <div class="mt-2">
              显存使用：{{ crashDetail?.software?.gpuDedicatedUsed }}
            </div>
            <div class="mt-2">
              虚拟显存使用：{{ crashDetail?.software?.gpuSharedUsed }}
            </div>
          </div>
        </div>
      </div>

      <div class="mt-8 flex items-center">
        <div class="font-size-16px font-bold">
          崩溃描述:
        </div>
      </div>
      <div :class="`${prefixCls}__info p-2 break-all`">
        {{ crashDetail?.description ? crashDetail?.description : '用户未填写' }}
      </div>
      <div class="mt-8 flex items-center">
        <div class="font-size-16px font-bold">
          出错堆栈:
        </div>
      </div>
      <template v-if="crashDetail?.errorCrashStack?.recordId">
        <ACollapse v-if="crashDetail?.errorCrashStack" v-model:activeKey="errorStackActiveKey" class="mt-4">
          <StackCollapse key="errorCrashStack" :stackInfo="crashDetail.errorCrashStack" isError />
        </ACollapse>
        <template v-if="crashDetail?.otherCrashStack?.length">
          <div class="text-center">
            <a-button type="link" class="my-4" @click="isStackExpand = !isStackExpand">
              {{ !isStackExpand ? '展开' : '收起' }}其他线程
            </a-button>
          </div>
          <ACollapse v-show="isStackExpand" v-model:activeKey="otherStackActiveKey">
            <StackCollapse v-for="stack in crashDetail?.otherCrashStack" :key="stack.Id" :stackInfo="stack" />
          </ACollapse>
        </template>
      </template>
      <div v-else :class="`${prefixCls}__info p-2`">
        <template v-if="crashDetail?.categoryID === 0">
          正在获取出错堆栈信息
        </template>
        <template v-else>
          无法获取出错堆栈信息
        </template>
      </div>
      <div class="mt-8 flex items-center">
        <div class="font-size-16px font-bold">
          关键log:
        </div>
      </div>
      <div :class="`${prefixCls}__info p-2 whitespace-pre-line overflow-auto text-nowrap`">
        <pre v-if="crashDetail?.detail" class="detail-log mb-0 mt-0 max-h-35vh">{{
           crashDetail?.detail
        }}</pre>
        <template v-else>
          无法获取关键log，请查看全部log
        </template>
      </div>
      <div class="mt-8 flex items-center">
        <div class="mb font-size-16px font-bold">
          全部文件 <span class="font-size-14px">(双击下载)</span>
        </div>
      </div>
      <VueFinder
        v-if="crashDetail?.logUrl"
        :id="`${prefixCls}-finder`"
        :class="{ dark: isDark /*兼容 VueFinder 暗黑模式 */ }"
        :url="getUrl(crashDetail?.logUrl)"
        locale="zh-cn"
        api="/api/api/v1/crash/path"
        :token="token"
        :showToolbar="false"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Collapse as ACollapse } from 'ant-design-vue';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { engineStateList } from './crashCollect.data';
import { useCrashCollect, useCrashDetail } from './crashCollectHook';
import { getCrashLabels, submitBug } from '/@/api/page/crashCollect';
import Icon from '/@/components/Icon';
import { useDesign } from '/@/hooks/web/useDesign';
import { useGo } from '/@/hooks/web/usePage';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { openWindow } from '/@/utils';
import StackCollapse from './components/StackCollapse.vue';
import { getAllPaginationList } from '/@/hooks/web/usePagination';
import CrashLabel from './components/CrashLabel.vue';
import type { CrashLabelsListItem } from '/@/api/page/model/crashCollectModel';
import { useDarkModeTheme } from '../../hooks/setting/useDarkModeTheme.ts';

defineOptions({
  name: 'CrashDetail',
});

const { prefixCls } = useDesign('crash-detail');
const userStore = useUserStoreWithOut();
const { getBackgroundColor } = useCrashCollect();
const { getCrashRecordDetail, crashDetail } = useCrashDetail();
const go = useGo();
const btnLoading = ref(false);
const router = useRouter();
const clickLabelId = ref<number>(0);
const recordID = Number(router.currentRoute.value.params.recordID);
const labelList = ref<CrashLabelsListItem[]>([]);
const isStackExpand = ref(false);
const errorStackActiveKey = ref(['errorCrashStack']);
const otherStackActiveKey = ref<string[]>([]);
const { isDark } = useDarkModeTheme();

const token = userStore.getToken;

function openClassDetail(ID?: number) {
  if (!ID) {
    return;
  }

  go({
    name: 'CrashClassDetail',
    params: {
      categoryID: ID,
    },
  });
}

function categoryName(item) {
  if (item?.category && item?.category?.ID) {
    return `${item.category?.ID}.${item.category?.name}`;
  } else {
    return '解析中...';
  }
}

function clickAddLabel(id) {
  clickLabelId.value = id;
}

function getUrl(logUrl: string) {
  if (logUrl) {
    const urlStr = logUrl.split('?')[1];
    const urlSearchParams = new URLSearchParams(urlStr);
    const result = Object.fromEntries(urlSearchParams.entries());
    const path = result.path;

    return path.startsWith('/') ? path.slice(1) : path;
  } else {
    return '';
  }
}
async function getLabelList() {
  const { code, list } = await getAllPaginationList((p) => getCrashLabels(userStore.getProjectId, p));

  if (code !== 7) {
    labelList.value = list;
  }
}
async function lading() {
  btnLoading.value = true;
  await submitBug(userStore.getProjectId, crashDetail.value!.ID!);
  btnLoading.value = false;
  go({ query: { recordID: crashDetail.value!.ID! } });
  getCrashRecordDetail(recordID);
}

function goBack() {
  if (window.history.state.back?.includes('crashCollect')) {
    router.back();
  } else {
    go({ name: 'CrashCollect' });
  }
}
function pageClick() {
  clickLabelId.value = -1;
}
onMounted(async () => {
  await getCrashRecordDetail(recordID);
  otherStackActiveKey.value = crashDetail.value?.otherCrashStack?.[0]?.Id ? [crashDetail.value?.otherCrashStack?.[0]?.Id] : [];
  await getLabelList();
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-crash-detail';
.@{prefix-cls} {
  .detail-log {
    font-family:
      Consolas,
      Menlo,
      Monaco,
      “DejaVu Sans Mono”,
      “Courier New”,
      monospace;
    font-size: 14px !important;
    line-height: 20px !important;
  }

  &__rhombus {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;

    &::before {
      content: ' ';
      position: absolute;
      top: 0;
      left: 0;
      width: 0;
      height: 0;
      border-top: 16px solid @FO-Container-Fill1;
      border-bottom: 16px solid @FO-Container-Fill1;
      border-right: 16px solid transparent;
      z-index: 2;
    }
  }

  &__info {
    @apply mt-4 rounded-md text-sm;

    background-color: @report-card-background;
  }

  &__all-log {
    @apply flex items-center ml-4 px-2 py-1 cursor-pointer;

    color: white;
    background-color: #595959;
    border-radius: 12px;
  }

  .ant-collapse-content-box {
    padding: 0 !important;
  }
}
</style>
