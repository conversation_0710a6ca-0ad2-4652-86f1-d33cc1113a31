<?xml version="1.0" encoding="UTF-8"?>
<svg id="_图层_2" data-name="图层 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 281.58 281.58">
  <defs>
    <style>
      .cls-1 {
        fill: #333;
      }

      .cls-2 {
        fill: none;
      }

      .cls-3 {
        fill: #666;
      }
    </style>
  </defs>
  <g id="_图层_1-2" data-name="图层 1">
    <rect class="cls-2" width="281.58" height="281.58"/>
    <rect class="cls-3" x="10.64" y="123.79" width="260.3" height="33.99" rx="9.36" ry="9.36" transform="translate(281.58 281.58) rotate(180)"/>
    <circle class="cls-1" cx="140.79" cy="140.79" r="61.34"/>
  </g>
</svg>