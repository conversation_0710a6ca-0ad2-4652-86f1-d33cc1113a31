<template>
  <div class="flex flex-wrap">
    <div
      v-for="stage in getPostOrderTraversal" :key="stage.key" class="flex items-center c-FO-Content-Text3"
    >
      <div class="flex items-center text-right" :class="{ 'c-FO-Content-Text1': passProcessItem.includes(stage.key) }">
        <span :class="{ 'font-bold c-FO-Content-Text1': nowProcessItem.includes(stage.key) }">{{ stage.label }}</span>
        <Icon
          v-if="stage.icon === IconType.Line || stage.icon === IconType.slash" :icon="LineIcon"
          :class="{ 'rotate-45': stage.icon === IconType.slash, ' c-FO-Datavis-Lightgreen2': passProcessItem.includes(stage.key), ' c-FO-Datavis-Yellow2 FO-Font-R16': nowProcessItem.includes(stage.key) }"
        />
        <Icon
          v-if="stage.icon === IconType.Branch || stage.icon === IconType.ReverseBranch" :icon="BranchIcon"
          :class="{ 'rotate-180': stage.icon === IconType.ReverseBranch, ' c-FO-Datavis-Lightgreen2': passProcessItem.includes(stage.key), ' c-FO-Datavis-Yellow2 ': nowProcessItem.includes(stage.key) }"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Icon } from '@iconify/vue';
import LineIcon from '@iconify-icons/icon-park-outline/minus';
import BranchIcon from '@iconify-icons/icon-park-outline/right-branch-one';
import { CheckStateType, ResCheckStateType, ReviewStateType, SubmitStateType } from './type.data';
import { cloneDeep } from 'lodash';

type ItemRow = any;
const props = defineProps<{
  item: ItemRow;
}>();

interface Stage {
  key: StageKey;
  label: string;
  next: StageKey[];
}
enum StageKey {
  RequestSubmission = 0,
  GeneralInspection = 1,
  InstanceCheck = 2,
  CheckCompleted = 3,
  CreateCommit = 4,
  CreateReview = 5,
  AwaitingReview = 6,
  ReviewResults = 7,
  CreateApproval = 8,
  AwaitingApproval = 9,
  ApprovalResults = 10,
  Submitting = 11,
  SubmissionCompleted = 12,
}
enum IconType {
  Line = 1,
  Branch = 2,
  slash = 3,
  ReverseBranch = 4,
  None = 5,
}

// 根据resCheckState，submitState，reviewState，checkState四个状态值列出其对应的 stages
const stateStageMap = {
  resCheckState: {
    [ResCheckStateType.GeneralInspection]: [StageKey.RequestSubmission],
    [ResCheckStateType.InstanceCheck]: [StageKey.RequestSubmission, StageKey.GeneralInspection],
    [ResCheckStateType.CheckCompleted]: [StageKey.RequestSubmission, StageKey.GeneralInspection, StageKey.InstanceCheck],
  },
  submitState: {
    [SubmitStateType.CreateCommit]: [StageKey.CheckCompleted],
    [SubmitStateType.Submitting]: [StageKey.CheckCompleted, StageKey.CreateCommit, StageKey.ReviewResults, StageKey.ApprovalResults],
    [SubmitStateType.SubmittingSucceeded]: [StageKey.CheckCompleted, StageKey.CreateCommit, StageKey.ReviewResults, StageKey.ApprovalResults, StageKey.Submitting],
    [SubmitStateType.SubmittingFailed]: [StageKey.CheckCompleted, StageKey.CreateCommit, StageKey.ReviewResults, StageKey.ApprovalResults, StageKey.Submitting],
  },
  reviewState: {
    [ReviewStateType.CreateReview]: props.item.submitState ? [StageKey.CreateCommit] : [],
    [ReviewStateType.AwaitingReview]: [StageKey.CreateCommit, StageKey.CreateReview],
    [ReviewStateType.ReviewResultsSucceeded]: [StageKey.CreateCommit, StageKey.CreateReview, StageKey.AwaitingReview],
    [ReviewStateType.ReviewResultsFailed]: [StageKey.CreateCommit, StageKey.CreateReview, StageKey.AwaitingReview],
  },
  checkState: {
    [CheckStateType.CreateApproval]: props.item.submitState ? [StageKey.CreateCommit] : [],
    [CheckStateType.AwaitingApproval]: [StageKey.CreateCommit, StageKey.CreateApproval],
    [CheckStateType.ApprovalResultsSucceeded]: [StageKey.CreateCommit, StageKey.CreateApproval, StageKey.AwaitingApproval],
    [CheckStateType.ApprovalResultsFailed]: [StageKey.CreateCommit, StageKey.CreateApproval, StageKey.AwaitingApproval],
  },
};
const showProcessItem = computed(() => getActiveStages(props.item));
const passProcessItem = computed(() => getPassedStages(props.item));
const nowProcessItem = computed(() => getNowStage(props.item));
// 全部链表
const stages = computed(() => [
  { key: StageKey.RequestSubmission, label: '请求检查', next: [StageKey.GeneralInspection] },
  { key: StageKey.GeneralInspection, label: '执行通用检查', next: [StageKey.InstanceCheck] },
  { key: StageKey.InstanceCheck, label: '执行实例检查', next: [StageKey.CheckCompleted] },
  { key: StageKey.CheckCompleted, label: '检查完成', next: [StageKey.CreateCommit] },
  { key: StageKey.CreateCommit, label: '创建提交', next: !props.item.reviewState && !props.item.checkState ? [StageKey.Submitting] : [StageKey.CreateReview, StageKey.CreateApproval] },
  { key: StageKey.CreateReview, label: '创建审查', next: [StageKey.AwaitingReview] },
  { key: StageKey.AwaitingReview, label: '等待审查', next: [StageKey.ReviewResults] },
  { key: StageKey.ReviewResults, label: props.item.reviewState === 4 ? '审查拒绝' : '审查通过', next: [StageKey.Submitting] },
  { key: StageKey.CreateApproval, label: '创建审批', next: [StageKey.AwaitingApproval] },
  { key: StageKey.AwaitingApproval, label: '等待审批', next: [StageKey.ApprovalResults] },
  { key: StageKey.ApprovalResults, label: props.item.checkState === 4 ? '审批拒绝' : '审批通过', next: [StageKey.Submitting] },
  { key: StageKey.Submitting, label: '正在提交', next: [StageKey.SubmissionCompleted] },
  { key: StageKey.SubmissionCompleted, label: '提交完成', next: [] },
]);

// 根据 passProcessItem 筛选的链表
const newStageMap = computed(() => {
  const showStageKeys = showProcessItem.value;
  const newStages = cloneDeep(stages.value).filter((stage) => showStageKeys.includes(stage.key));
  newStages.forEach((stage) => {
    stage.next = stage.next.filter((nextStage) => showStageKeys.includes(nextStage));
  });
  return newStages.reduce((acc, cur) => ({ ...acc, [cur.key]: cur }), {} as Record<StageKey, Stage>);
});

// 根据 passProcessItem 筛选的来源链表
const newStageFromMap = computed(() =>
  Object.values(newStageMap.value).reduce((acc, cur) => {
    for (const i of cur.next) {
      if (Array.isArray(acc[i])) {
        acc[i].push(cur.key);
      } else {
        acc[i] = [cur.key];
      }
    }
    return acc;
  }, {} as Record<StageKey, StageKey[]>));
const getPostOrderTraversal = computed(() => dfsPostOrder(props.item.resCheckState ? StageKey.RequestSubmission : StageKey.CreateCommit, null, []));

function dfsPostOrder(key: StageKey, lastNode: Stage | null, nodeList: (Stage & { icon?: IconType })[]) {
  const node = newStageMap.value[key];

  // 上一个值是否是会合点的最后一个（不是的就不执行下文）
  if (newStageFromMap.value[key] && newStageFromMap.value[key].length > 1 && lastNode && lastNode.key !== newStageFromMap.value[key][newStageFromMap.value[key].length - 1]) {
    return nodeList;
  }
  if (node) {
    // 下一个节点是否是会合点（赋予节点和斜线的图标）
    if (newStageFromMap.value[node.next[0]]?.length > 1) {
      if (node.key) {
        // 当前节点是否是会合点的最后一个
        if (newStageFromMap.value[node.next[0]][newStageFromMap.value[node.next[0]].length - 1] !== node.key) {
          nodeList.push({ ...node, icon: IconType.slash });
        } else {
          nodeList.push({ ...node, icon: IconType.ReverseBranch });
        }
      }
    } else {
      if (!newStageFromMap.value[node.next[0]]) {
        nodeList.push({ ...node, icon: IconType.None });
      } else {
        nodeList.push({ ...node, icon: node.next.length === 2 ? IconType.Branch : IconType.Line });
      }
    }

    node.next.forEach((nextStage) => {
      dfsPostOrder(nextStage, node, nodeList);
    });
  }
  return nodeList;
}

/**
 * 用于获取可用的 stages
 */
function getActiveStages(item: ItemRow): StageKey[] {
  const res: StageKey[] = [];
  if (item.resCheckState) {
    res.push(StageKey.RequestSubmission);
    res.push(StageKey.GeneralInspection);
    res.push(StageKey.InstanceCheck);
    res.push(StageKey.CheckCompleted);
  }
  res.push(StageKey.CreateCommit);
  if (item.reviewState) {
    res.push(StageKey.CreateReview);
    res.push(StageKey.AwaitingReview);
    res.push(StageKey.ReviewResults);
  }
  if (item.checkState) {
    res.push(StageKey.CreateApproval);
    res.push(StageKey.AwaitingApproval);
    res.push(StageKey.ApprovalResults);
  }
  if (!(item.reviewState === 4 && item.checkState === 0)) {
    res.push(StageKey.Submitting);
    res.push(StageKey.SubmissionCompleted);
  }

  return res;
}
/**
 * 获取已经通过的 stages
 */
function getPassedStages(item: ItemRow): StageKey[] {
  const passedStages = new Set<StageKey>();
  Object.entries(stateStageMap).forEach(([stateType, stateMap]) => {
    const stateValue = item[stateType as keyof typeof item];
    const stages = stateMap[stateValue as keyof typeof stateMap] as StageKey[];
    if (stages) {
      stages.forEach((stage: StageKey) => passedStages.add(stage));
    }
  });
  return Array.from(passedStages);
}

/**
 * 获取当前正在执行的 stages
 */
function getNowStage(item: ItemRow): StageKey[] {
  // 处理资源检查状态
  if (item.resCheckState === ResCheckStateType.RequestSubmission) {
    return [StageKey.RequestSubmission];
  }
  // 处理提交状态的初始阶段
  if (item.resCheckState === ResCheckStateType.GeneralInspection) {
    return [StageKey.GeneralInspection];
  }
  if (item.resCheckState === ResCheckStateType.InstanceCheck) {
    return [StageKey.InstanceCheck];
  }
  if (item.resCheckState === ResCheckStateType.CheckCompleted && item.submitState === SubmitStateType.Initial) {
    return [StageKey.CheckCompleted];
  }

  // 处理提交流程的后续阶段
  if (!item.reviewState && !item.checkState && item.submitState === SubmitStateType.CreateCommit) {
    return [StageKey.CreateCommit];
  }
  if (item.submitState === SubmitStateType.Submitting) {
    return [StageKey.Submitting];
  }
  if (item.submitState === SubmitStateType.SubmittingSucceeded || item.submitState === SubmitStateType.SubmittingFailed) {
    return [StageKey.SubmissionCompleted];
  }

  // 处理审批状态
  const res: StageKey[] = [];
  if (item.checkState === CheckStateType.CreateApproval) {
    res.push(StageKey.CreateApproval);
  } else if (item.checkState === CheckStateType.AwaitingApproval) {
    res.push(StageKey.AwaitingApproval);
  } else if (item.checkState === CheckStateType.ApprovalResultsSucceeded || item.checkState === CheckStateType.ApprovalResultsFailed) {
    res.push(StageKey.ApprovalResults);
  }
  // 处理审查状态
  if (item.reviewState === ReviewStateType.CreateReview) {
    res.push(StageKey.CreateReview);
  }
  if (item.reviewState === ReviewStateType.AwaitingReview) {
    res.push(StageKey.AwaitingReview);
  }
  if (item.reviewState === ReviewStateType.ReviewResultsSucceeded || item.reviewState === ReviewStateType.ReviewResultsFailed) {
    res.push(StageKey.ReviewResults);
  }

  return res;
}
</script>
