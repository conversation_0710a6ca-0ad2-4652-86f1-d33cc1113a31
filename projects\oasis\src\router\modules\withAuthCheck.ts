import type { Router } from 'vue-router';
import { useUserStoreWithOut } from '../../store/modules/user.ts';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';

/**
 * 权限检查相关
 */
export function withAuthCheck(router: Router) {
  const userStore = useUserStoreWithOut();

  router.beforeEach(async (to, _, next) => {
    if (to.query.token) {
      // 如果有token参数,则设置token
      // 此处设置并非给页面使用，而是为了给全局请求使用
      userStore.setTokenFromRouteQuery(to.query.token as string);
    }

    if (to.query.accessToken) {
      /**
       * 服务于 views/test/gamePackage/list/simpleCard/index.vue
       * @deprecated：后续不可再增加其他全局 query
       */
      userStore.setAccessToken(to.query.accessToken as string);
    }

    if (to.meta.permissionDeclare == null) {
      // 没有声明权限的路由直接放行
      return next();
    }

    if (!userStore.getToken && !userStore.getAccessToken && to.name !== PlatformEnterPoint.Login) {
      // 没有登录信息
      return next({
        name: PlatformEnterPoint.Login,
        query: {
          redirect: to.fullPath,
        },
      });
    }
    next();
  });
}
