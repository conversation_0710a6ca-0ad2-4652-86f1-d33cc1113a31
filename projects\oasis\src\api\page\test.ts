import type { BasicAddResult, BasicBatchSortParams, BasicPageParams, NullableBasicResult } from './../model/baseModel';
import type {
  BatchDeleteGameArchivesParams,
  BatchGameLabelSortParams,
  CopyGameArchivesParams,
  GameArchivesItemParams,
  GameArchivesListGetResultModel,
  GameArchivesListItem,
  GameArchivesPageParams,
  GameAttachmentsItemParams,
  GameAttachmentsListGetResultModel,
  GameAttachmentsListItem,
  GameAttachmentsPageParams,
  GameBranchesItemParams,
  GameBranchesListGetResultModel,
  GameBranchesListItem,
  GameBranchesPageParams,
  GameCleanRulesItemParams,
  GameCleanRulesListGetResultModel,
  GameCleanRulesListItem,
  GameCleanRulesPageParams,
  GameLabelsItemParams,
  GameLabelsListGetResultModel,
  GameLabelsListItem,
  GameLabelsPageParams,
  GameLabelValuesItemParams,
  GameLabelValuesListGetResultModel,
  GameLabelValuesListItem,
  GameLabelValuesPageParams,
  GamePackageDoctorListPageParams,
  GamePackageFilterItemParams,
  GamePackageItemParams,
  GamePackagesDoctorItemGetResultModel,
  GamePackagesDoctorListGetResultModel,
  GamePackagesDoctorTrendGetResultModel,
  GamePackagesDoctorTrendParams,
  GamePackagesFiltersListGetResultModel,
  GamePackagesFiltersListItem,
  GamePackagesFiltersPageParams,
  GamePackagesListGetResultModel,
  GamePackagesListItem,
  GamePackagesPageParams,
  GamePackagesVersionsListGetResultModel,
  GamePackagesVersionsListItem,
  GamePackagesVersionsPageParams,
  GamePackageVersionItemParams,
  GameTestConfigItem,
  LogSource,
  PkgClassListPageParams,
  PkgClassWithpkgListPageParams,
  ProjectPkgBuildInfoCLDetailItem,
  ProjectPkgBuildInfoCLDetailParams,
  ProjectPkgBuildInfoListGetResultModel,
  ProjectPkgBuildInfoListItem,
  ProjectPkgBuildInfoListPageParams,
  SceneBranchListGetResultModel,
  setPkgClassListResultModel,
} from './model/testModel';
import type { ErrorMessageMode, SuccessMessageMode } from '/#/axios';
import { defHttp } from '/@/utils/http/axios';

enum Api {
  Projects = '/api/v1/projects',
  GameArchives = '/game/archives',
  GameBranches = '/game/branches',
  GamePackages = '/appstore/game_pkgs',
  GameCleanRules = '/appstore/clean_rules',
}

/**
 * 获取项目游戏存档列表
 * @param projectID 项目id
 * @param params 筛选条件
 */
export function getGameArchivesListByPage(projectID: number, params?: GameArchivesPageParams) {
  return defHttp.get<GameArchivesListGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.GameArchives}`,
    params,
  });
}

/**
 * 根据id获取项目游戏存档信息
 * @param projectID 项目id
 * @param ID 游戏存档id
 */
export function getGameArchiveByID(projectID: number, ID: string) {
  return defHttp.get<GameArchivesItemParams>({
    url: `${Api.Projects}/${projectID}${Api.GameArchives}/${ID}`,
  });
}

/**
 * 新增项目游戏存档
 * @param projectID 项目id
 * @param data 游戏存档数据
 */
export function addGameArchive(projectID: number, data: GameArchivesListItem) {
  return defHttp.post<null>({ url: `${Api.Projects}/${projectID}${Api.GameArchives}`, data });
}

/**
 * 编辑项目游戏存档
 * @param projectID 项目id
 * @param data 游戏存档数据
 * @param editId 游戏存档id
 */
export function editGameArchive(projectID: number, data: GameArchivesListItem, editId: number) {
  return defHttp.put<null>({ url: `${Api.Projects}/${projectID}${Api.GameArchives}/${editId}`, data });
}

/**
 * 删除项目游戏存档
 * @param projectID 项目id
 * @param editId 游戏存档id
 */
export function deleteGameArchive(projectID: number, editId: number) {
  return defHttp.delete<null>({ url: `${Api.Projects}/${projectID}${Api.GameArchives}/${editId}` });
}

/**
 * 复制分支游戏存档
 * @param projectID 项目id
 * @param params 复制参数
 */
export function copyGameArchive(projectID: number, params: CopyGameArchivesParams) {
  return defHttp.post<NullableBasicResult>({
    url: `${Api.Projects}/${projectID}${Api.GameArchives}/copy`,
    params,
  });
}

/**
 * 批量删除P4干员组干员
 * @param projectID 项目id
 * @param data 数据
 */
export function batchDeleteGameArchive(projectID: number, data: BatchDeleteGameArchivesParams) {
  return defHttp.delete<null>({
    url: `${Api.Projects}/${projectID}${Api.GameArchives}/deleteArchiveByIds`,
    data,
  });
}

/**
 * 获取项目游戏分支列表
 * @param projectID 项目id
 * @param params 筛选条件
 */
export function getGameBranchesListByPage(projectID: number, params?: GameBranchesPageParams) {
  return defHttp.get<GameBranchesListGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.GameBranches}`,
    params,
  });
}

/**
 * 根据id获取项目游戏分支信息
 * @param projectID 项目id
 * @param ID 游戏分支id
 */
export function getGameBranchByID(projectID: number, ID: string) {
  return defHttp.get<GameBranchesItemParams>({
    url: `${Api.Projects}/${projectID}${Api.GameBranches}/${ID}`,
  });
}

/**
 * 新增项目游戏分支
 * @param projectID 项目id
 * @param data 游戏分支数据
 */
export function addGameBranch(projectID: number, data: GameBranchesListItem) {
  return defHttp.post<null>({ url: `${Api.Projects}/${projectID}${Api.GameBranches}`, data });
}

/**
 * 编辑项目游戏分支
 * @param projectID 项目id
 * @param data 游戏分支数据
 * @param editId 游戏分支id
 */
export function editGameBranch(projectID: number, data: GameBranchesListItem, editId: number) {
  return defHttp.put<null>({ url: `${Api.Projects}/${projectID}${Api.GameBranches}/${editId}`, data });
}

/**
 * 删除项目游戏分支
 * @param projectID 项目id
 * @param editId 游戏分支id
 */
export function deleteGameBranch(projectID: number, editId: number) {
  return defHttp.delete<null>({ url: `${Api.Projects}/${projectID}${Api.GameBranches}/${editId}` });
}

/**
 * 获取项目游戏包列表
 * @param projectID 项目id
 * @param params 筛选条件
 */
export function getGamePackagesListByPage(projectID: number, params?: GamePackagesPageParams) {
  return defHttp.get<GamePackagesListGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.GamePackages}`,
    params,
  });
}

/**
 * 根据id获取项目游戏包信息
 * @param projectID 项目id
 * @param ID 游戏包id
 * @param errorMessageMode 错误信息模式
 */
export function getGamePackageByID(projectID: number, ID: number, errorMessageMode: ErrorMessageMode = 'message') {
  return defHttp.get<GamePackageItemParams>({
    url: `${Api.Projects}/${projectID}${Api.GamePackages}/${ID}`,
  }, {
    errorMessageMode,
  });
}

/**
 * 新增项目游戏包
 * @param projectID 项目id
 * @param data 游戏包数据
 */
export function addGamePackage(projectID: number, data: GamePackagesListItem) {
  return defHttp.post<NullableBasicResult>({ url: `${Api.Projects}/${projectID}${Api.GamePackages}`, data });
}

/**
 * 编辑项目游戏包
 * @param projectID 项目id
 * @param data 游戏包数据
 * @param editId 游戏包id
 */
export function editGamePackage(projectID: number, data: GamePackagesListItem, editId: number) {
  return defHttp.put<NullableBasicResult>({ url: `${Api.Projects}/${projectID}${Api.GamePackages}/${editId}`, data });
}

/**
 * 删除项目游戏包
 * @param projectID 项目id
 * @param editId 游戏包id
 */
export function deleteGamePackage(projectID: number, editId: number) {
  return defHttp.delete<null>({ url: `${Api.Projects}/${projectID}${Api.GamePackages}/${editId}` });
}

/**
 * 排序项目游戏包
 * @param projectID 项目id
 * @param data 排序ids
 */
export function batchGamePackageSort(projectID: number, data: BasicBatchSortParams) {
  return defHttp.put<null>({ url: `${Api.Projects}/${projectID}${Api.GamePackages}/sort`, data });
}

/**
 * 获取项目游戏包版本列表
 * @param projectID 项目id
 * @param pkgID 游戏包id
 * @param params 筛选条件
 */
export function getGamePackagesVersionsListByPage(projectID: number, pkgID: number, params?: GamePackagesVersionsPageParams, lbs?: string[], colors?: number[]) {
  return defHttp.get<GamePackagesVersionsListGetResultModel>({
    url:
      `${Api.Projects}/${projectID}${Api.GamePackages}/${pkgID}/versions${lbs?.length ? `?${lbs?.map((item) => `lbs=${item}`).join('&')}` : ''}${colors?.length ? `${lbs?.length ? '&' : '?'}${colors?.map((item) => `colors=${item}`).join('&')}` : ''
      }`,
    params,
  });
}

/**
 * 通过ID获取项目游戏包版本
 * @param projectID 项目id
 * @param pkgID 游戏包id
 * @param versionID 版本id
 * @param errorMessageMode 错误信息模式
 */
export function getGamePackagesVersionByID(projectID: number, pkgID: number, versionID: number, errorMessageMode?: ErrorMessageMode) {
  return defHttp.get<GamePackageVersionItemParams>(
    {
      url: `${Api.Projects}/${projectID}${Api.GamePackages}/${pkgID}/versions/${versionID}`,
    },
    {
      errorMessageMode,
    },
  );
}

/**
 * 新增项目游戏包版本
 * @param projectID 项目id
 * @param pkgID 游戏包id
 * @param data 游戏包数据
 */
export function addGamePackagesVersion(projectID: number, pkgID: number, data: GamePackagesVersionsListItem) {
  return defHttp.post<BasicAddResult>({
    url: `${Api.Projects}/${projectID}${Api.GamePackages}/${pkgID}/versions`,
    data,
  });
}

/**
 * 完成游戏包版本创建
 * @param projectID 项目id
 * @param pkgID 游戏包id
 * @param versionID 版本id
 */
export function editGamePackagesVersionFinish(projectID: number, pkgID: number, versionID: number) {
  return defHttp.put<NullableBasicResult>(
    {
      url: `${Api.Projects}/${projectID}${Api.GamePackages}/${pkgID}/versions/${versionID}/finish`,
    },
    {
      successMessageMode: 'none',
    },
  );
}

/**
 * 编辑项目游戏包版本
 * @param projectID 项目id
 * @param pkgID 游戏包id
 * @param data 版本数据
 * @param editId 版本id
 */
export function editGamePackagesVersion(projectID: number, pkgID: number, data: GamePackagesVersionsListItem, editId: number) {
  return defHttp.put<NullableBasicResult>({
    url: `${Api.Projects}/${projectID}${Api.GamePackages}/${pkgID}/versions/${editId}`,
    data,
  });
}

/**
 * 删除项目游戏包版本
 * @param projectID 项目id
 * @param pkgID 游戏包id
 * @param editId 版本id
 */
export function deleteGamePackagesVersion(projectID: number, pkgID: number, editId: number) {
  return defHttp.delete<null>({
    url: `${Api.Projects}/${projectID}${Api.GamePackages}/${pkgID}/versions/${editId}`,
  });
}

/**
 * 批量删除项目游戏包版本
 * @param projectID 项目id
 * @param pkgID 游戏包id
 * @param params 版本ids
 */
export function batchDeleteGamePackagesVersion(projectID: number, pkgID: number, params: BatchDeleteGameArchivesParams) {
  return defHttp.delete<NullableBasicResult>(
    {
      url: `${Api.Projects}/${projectID}${Api.GamePackages}/${pkgID}/versions/deletePkgVersionByIds`,
      params,
      timeout: 1000 * 60 * 5,
    },
    {
      successMessageMode: 'none',
    },
  );
}

/**
 * 获取项目打包信息列表
 * @param projectID 项目id
 * @param params 接口参数
 */
export function getProjectPkgBuildInfoListByPage(projectID: number, params?: ProjectPkgBuildInfoListPageParams) {
  return defHttp.get<ProjectPkgBuildInfoListGetResultModel>({
    url: `${Api.Projects}/${projectID}/appstore/buildInfos`,
    params,
  });
}

/**
 * 获取项目打包信息CL详情
 * @param projectID 项目id
 * @param buildInfoID 打包信息id
 * @param params 接口参数
 */
export function getProjectPkgBuildInfoCLDetail(projectID: number, buildInfoID: number, params?: ProjectPkgBuildInfoCLDetailParams) {
  return defHttp.get<{ clInfo: ProjectPkgBuildInfoCLDetailItem }>({
    url: `${Api.Projects}/${projectID}/appstore/buildInfos/${buildInfoID}/CL`,
    params,
  });
}

/**
 * 新增项目打包信息列表
 * @param projectID 项目id
 * @param data 游戏标签数据
 */
export function addProjectPkgBuildInfo(projectID: number, data: ProjectPkgBuildInfoListItem) {
  return defHttp.post<BasicAddResult>({ url: `${Api.Projects}/${projectID}/appstore/buildInfos`, data });
}

/**
 * 编辑项目打包信息列表
 * @param projectID 项目id
 * @param data 游戏标签数据
 * @param editId 游戏标签id
 */
export function editProjectPkgBuildInfo(projectID: number, data: ProjectPkgBuildInfoListItem, editId: number) {
  return defHttp.put<NullableBasicResult>({
    url: `${Api.Projects}/${projectID}/appstore/buildInfos/${editId}`,
    data,
  });
}

/**
 * 删除项目打包信息列表
 * @param projectID 项目id
 * @param editId 游戏标签id
 */
export function deleteProjectPkgBuildInfo(projectID: number, editId: number) {
  return defHttp.delete<NullableBasicResult>({
    url: `${Api.Projects}/${projectID}/appstore/buildInfos/${editId}`,
  });
}

/**
 * 获取项目游戏包包体检测列表
 * @param projectID 项目id
 * @param pkgID 游戏包id
 * @param params 接口参数
 */
export function getGamePackagesDoctorListByPage(projectID: number, pkgID: number, params?: GamePackageDoctorListPageParams) {
  return defHttp.get<GamePackagesDoctorListGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.GamePackages}/${pkgID}/doctors`,
    params,
  });
}

/**
 * 通过ID获取项目游戏包包体检测
 * @param projectID 项目id
 * @param pkgID 游戏包id
 * @param doctorID 包体检测id
 */
export function getGamePackagesDoctorByID(projectID: number, pkgID: number, doctorID: number) {
  return defHttp.get<GamePackagesDoctorItemGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.GamePackages}/${pkgID}/doctors/${doctorID}`,
  });
}

/**
 * 通过ID获取项目游戏包包体检测(免登录)
 * @param projectID 项目id
 * @param pkgID 游戏包id
 * @param doctorID 包体检测id
 */
export function getGamePackagesDoctorByIDWithoutLogin(projectID: number, pkgID: number, doctorID: number) {
  return defHttp.get<GamePackagesDoctorItemGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.GamePackages}/${pkgID}/doctors/${doctorID}/simple`,
  });
}

/**
 * 获取项目游戏包包体检测趋势(免登录)
 * @param projectID 项目id
 * @param pkgID 游戏包id
 * @param params 参数
 */
export function getGamePackagesDoctorTrend(projectID: number, pkgID: number, params?: GamePackagesDoctorTrendParams) {
  return defHttp.post<GamePackagesDoctorTrendGetResultModel>(
    {
      url: `${Api.Projects}/${projectID}${Api.GamePackages}/${pkgID}/doctors/trend`,
      params,
    },
    {
      successMessageMode: 'none',
    },
  );
}

/**
 * 获取项目游戏标签列表
 * @param projectID 项目id
 * @param params 筛选条件
 */
export function getGameLabelsListByPage(projectID: number, params?: GameLabelsPageParams) {
  return defHttp.get<GameLabelsListGetResultModel>({
    url: `${Api.Projects}/${projectID}/appstore/labels`,
    params,
  });
}

/**
 * 根据id获取项目游戏标签信息
 * @param projectID 项目id
 * @param ID 游戏标签id
 */
export function getGameLabelByID(projectID: number, ID: number) {
  return defHttp.get<GameLabelsItemParams>({
    url: `${Api.Projects}/${projectID}/appstore/labels/${ID}`,
  });
}

/**
 * 新增项目游戏标签
 * @param projectID 项目id
 * @param data 游戏标签数据
 */
export function addGameLabel(projectID: number, data: GameLabelsListItem) {
  return defHttp.post<BasicAddResult>({ url: `${Api.Projects}/${projectID}/appstore/labels`, data });
}

/**
 * 编辑项目游戏标签
 * @param projectID 项目id
 * @param data 游戏标签数据
 * @param editId 游戏标签id
 */
export function editGameLabel(projectID: number, data: GameLabelsListItem, editId: number) {
  return defHttp.put<NullableBasicResult>({
    url: `${Api.Projects}/${projectID}/appstore/labels/${editId}`,
    data,
  });
}

/**
 * 删除项目游戏标签
 * @param projectID 项目id
 * @param editId 游戏标签id
 */
export function deleteGameLabel(projectID: number, editId: number) {
  return defHttp.delete<NullableBasicResult>({
    url: `${Api.Projects}/${projectID}/appstore/labels/${editId}`,
  });
}

/**
 * 获取项目游戏标签取值列表
 * @param projectID 项目id
 * @param params 筛选条件
 */
export function getGameLabelValuesListByPage(projectID: number, params?: GameLabelValuesPageParams) {
  return defHttp.get<GameLabelValuesListGetResultModel>({
    url: `${Api.Projects}/${projectID}/appstore/labelValues`,
    params,
  });
}

/**
 * 根据id获取项目游戏标签取值信息
 * @param projectID 项目id
 * @param ID 游戏标签取值id
 */
export function getGameLabelValueByID(projectID: number, ID: number) {
  return defHttp.get<GameLabelValuesItemParams>({
    url: `${Api.Projects}/${projectID}/appstore/labelValues/${ID}`,
  });
}

/**
 * 新增项目游戏标签取值
 * @param projectID 项目id
 * @param data 游戏标签取值数据
 */
export function addGameLabelValue(projectID: number, data: GameLabelValuesListItem) {
  return defHttp.post<BasicAddResult>({ url: `${Api.Projects}/${projectID}/appstore/labelValues`, data });
}

/**
 * 编辑项目游戏标签取值
 * @param projectID 项目id
 * @param data 游戏标签取值数据
 * @param editId 游戏标签取值id
 */
export function editGameLabelValue(projectID: number, data: GameLabelValuesListItem, editId: number) {
  return defHttp.put<NullableBasicResult>({
    url: `${Api.Projects}/${projectID}/appstore/labelValues/${editId}`,
    data,
  });
}

/**
 * 删除项目游戏标签取值
 * @param projectID 项目id
 * @param editId 游戏标签取值id
 */
export function deleteGameLabelValue(projectID: number, editId: number) {
  return defHttp.delete<null>({ url: `${Api.Projects}/${projectID}/appstore/labelValues/${editId}` });
}

/**
 * 批量给包体标签排序
 * @param projectID 项目id
 * @param data 新干员组id列表
 * @param successMessageMode 成功提示模式
 */
export function batchGameLabelSort(projectID: number, data: BatchGameLabelSortParams, successMessageMode: SuccessMessageMode = 'message') {
  return defHttp.put<null>(
    { url: `${Api.Projects}/${projectID}/appstore/labels/sort`, data },
    {
      successMessageMode,
    },
  );
}

/**
 * 给项目游戏包版本上标签
 * @param projectID 项目id
 * @param pkgID 游戏包id
 * @param versionID 版本id
 * @param data 游戏包数据
 */
export function newGamePackagesVersionTag(projectID: number, pkgID: number, versionID: number, data: GameLabelValuesListItem) {
  return defHttp.post<NullableBasicResult>(
    {
      url: `${Api.Projects}/${projectID}${Api.GamePackages}/${pkgID}/versions/${versionID}/labels`,
      data,
    },
    {
      successMessageMode: 'none',
    },
  );
}

/**
 * 删除项目游戏包版本上的标签
 * @param projectID 项目id
 * @param pkgID 游戏包id
 * @param versionID 版本id
 * @param labelID 标签id
 * @param data 游戏包数据
 */
export function deleteGamePackagesVersionTag(projectID: number, pkgID: number, versionID: number, labelID: number, data: GameLabelValuesListItem) {
  return defHttp.delete<NullableBasicResult>(
    {
      url: `${Api.Projects}/${projectID}${Api.GamePackages}/${pkgID}/versions/${versionID}/labels/${labelID}`,
      data,
    },
    {
      successMessageMode: 'none',
    },
  );
}

/**
 * 获取项目游戏包体附件列表
 * @param projectID 项目id
 * @param params 筛选条件
 */
export function getGameAttachmentsListByPage(projectID: number, params?: GameAttachmentsPageParams) {
  return defHttp.get<GameAttachmentsListGetResultModel>({
    url: `${Api.Projects}/${projectID}/appstore/attachments`,
    params,
  });
}

/**
 * 根据id获取项目游戏包体附件信息
 * @param projectID 项目id
 * @param ID 游戏包体附件id
 */
export function getGameAttachmentByID(projectID: number, ID: number) {
  return defHttp.get<GameAttachmentsItemParams>({
    url: `${Api.Projects}/${projectID}/appstore/attachments/${ID}`,
  });
}

/**
 * 新增项目游戏包体附件
 * @param projectID 项目id
 * @param data 游戏包体附件数据
 */
export function addGameAttachment(projectID: number, data: GameAttachmentsListItem) {
  return defHttp.post<BasicAddResult>(
    { url: `${Api.Projects}/${projectID}/appstore/attachments`, data },
    { successMessageMode: 'none' },
  );
}

/**
 * 编辑项目游戏包体附件
 * @param projectID 项目id
 * @param data 游戏包体附件数据
 * @param editId 游戏包体附件id
 */
export function editGameAttachment(projectID: number, data: GameAttachmentsListItem, editId: number) {
  return defHttp.put<NullableBasicResult>({
    url: `${Api.Projects}/${projectID}/appstore/attachments/${editId}`,
    data,
  });
}

/**
 * 删除项目游戏包体附件
 * @param projectID 项目id
 * @param editId 游戏包体附件id
 */
export function deleteGameAttachment(projectID: number, editId: number) {
  return defHttp.delete<null>({ url: `${Api.Projects}/${projectID}/appstore/attachments/${editId}` });
}

/**
 * 获取项目游戏包清理规则列表
 * @param projectID 项目id
 * @param params 筛选条件
 */
export function getGameCleanRulesListByPage(projectID: number, params?: GameCleanRulesPageParams) {
  return defHttp.get<GameCleanRulesListGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.GameCleanRules}`,
    params,
  });
}

/**
 * 根据id获取项目游戏包清理规则信息
 * @param projectID 项目id
 * @param ID 游戏包清理规则id
 */
export function getGameCleanRuleByID(projectID: number, ID: string) {
  return defHttp.get<GameCleanRulesItemParams>({
    url: `${Api.Projects}/${projectID}${Api.GameCleanRules}/${ID}`,
  });
}

/**
 * 新增项目游戏包清理规则
 * @param projectID 项目id
 * @param data 游戏包清理规则数据
 */
export function addGameCleanRule(projectID: number, data: GameCleanRulesListItem) {
  return defHttp.post<BasicAddResult>(
    { url: `${Api.Projects}/${projectID}${Api.GameCleanRules}`, data },
    {
      successMessageMode: 'none',
    },
  );
}

/**
 * 编辑项目游戏包清理规则
 * @param projectID 项目id
 * @param data 游戏包清理规则数据
 * @param editId 游戏包清理规则id
 */
export function editGameCleanRule(projectID: number, data: GameCleanRulesListItem, editId: number) {
  return defHttp.put<NullableBasicResult>(
    {
      url: `${Api.Projects}/${projectID}${Api.GameCleanRules}/${editId}`,
      data,
    },
    {
      successMessageMode: 'none',
    },
  );
}

/**
 * 删除项目游戏包清理规则
 * @param projectID 项目id
 * @param editId 游戏包清理规则id
 */
export function deleteGameCleanRule(projectID: number, editId: number) {
  return defHttp.delete<null>({ url: `${Api.Projects}/${projectID}${Api.GameCleanRules}/${editId}` });
}

/**
 * 获取项目游戏包默认筛选列表
 * @param projectID 项目id
 * @param pkgID 游戏包id
 * @param params 筛选条件
 */
export function getGamePackagesFiltersListByPage(projectID: number, pkgID: number, params?: GamePackagesFiltersPageParams) {
  return defHttp.get<GamePackagesFiltersListGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.GamePackages}/${pkgID}/filters`,
    params,
  });
}

/**
 * 通过ID获取项目游戏包默认筛选
 * @param projectID 项目id
 * @param pkgID 游戏包id
 * @param filterID 默认筛选id
 */
export function getGamePackagesFilterByID(projectID: number, pkgID: number, filterID: number) {
  return defHttp.get<GamePackageFilterItemParams>({
    url: `${Api.Projects}/${projectID}${Api.GamePackages}/${pkgID}/filters/${filterID}`,
  });
}

/**
 * 新增项目游戏包默认筛选
 * @param projectID 项目id
 * @param pkgID 游戏包id
 * @param data 默认筛选数据
 */
export function addGamePackagesFilter(projectID: number, pkgID: number, data: GamePackagesFiltersListItem) {
  return defHttp.post<BasicAddResult>(
    {
      url: `${Api.Projects}/${projectID}${Api.GamePackages}/${pkgID}/filters`,
      data,
    },
    {
      successMessageMode: 'none',
    },
  );
}

/**
 * 编辑项目游戏包默认筛选
 * @param projectID 项目id
 * @param pkgID 游戏包id
 * @param data 默认筛选数据
 * @param editId 默认筛选id
 */
export function editGamePackagesFilter(projectID: number, pkgID: number, data: GamePackagesFiltersListItem, editId: number) {
  return defHttp.put<NullableBasicResult>(
    {
      url: `${Api.Projects}/${projectID}${Api.GamePackages}/${pkgID}/filters/${editId}`,
      data,
    },
    {
      successMessageMode: 'none',
    },
  );
}

/**
 * 删除项目游戏包默认筛选
 * @param projectID 项目id
 * @param pkgID 默认筛选id
 * @param editId 默认筛选id
 */
export function deleteGamePackagesFilter(projectID: number, pkgID: number, editId: number) {
  return defHttp.delete<null>({
    url: `${Api.Projects}/${projectID}${Api.GamePackages}/${pkgID}/filters/${editId}`,
  });
}

/**
 * 获取项目回收站游戏包版本列表
 * @param projectID 项目id
 * @param params 筛选条件
 */
export function getRecycleGamePackagesListByPage(projectID: number, params?: GamePackagesVersionsPageParams) {
  return defHttp.get<GamePackagesVersionsListGetResultModel>({
    url:
      `${Api.Projects}/${projectID}/appstore/recycle_pkgs`,
    params,
  });
}

/**
 * 获取项目对应分支回收站游戏包版本列表
 * @param projectID 项目id
 * @param pkgID 游戏包id
 * @param params 筛选条件
 */
export function getGamePackagesRecycleVersionsListByPage(projectID: number, pkgID: number, params?: GamePackagesVersionsPageParams) {
  return defHttp.get<GamePackagesVersionsListGetResultModel>({
    url:
      `${Api.Projects}/${projectID}/appstore/recycle_pkgs/${pkgID}/versions`,
    params,
  });
}

/**
 * 通过ID获取项目回收站游戏包版本
 * @param projectID 项目id
 * @param pkgID 游戏包id
 * @param versionID 版本id
 */
export function getGamePackagesRecycleVersionByID(projectID: number, pkgID: number, versionID: number) {
  return defHttp.get<GamePackageVersionItemParams>({
    url: `${Api.Projects}/${projectID}/appstore/recycle_pkgs/${pkgID}/versions/${versionID}`,
  });
}

/**
 * 通过ID恢复项目回收站游戏包版本
 * @param projectID 项目id
 * @param pkgID 游戏包id
 * @param versionID 版本id
 */
export function recoverGamePackagesRecycleVersionByID(projectID: number, pkgID: number, versionID: number) {
  return defHttp.post<NullableBasicResult>({
    url: `${Api.Projects}/${projectID}/appstore/recycle_pkgs/${pkgID}/versions/${versionID}`,
  });
}

/**
 * 获取游戏包版本所有颜色
 * @param projectID 项目id
 * @param pkgID 游戏包id
 */
export function getGameStoreColorList(projectID: number, pkgID: number) {
  return defHttp.get<{ colors: number[] }>({
    url: `${Api.Projects}/${projectID}/appstore/game_pkgs/${pkgID}/versions/color`,
  });
}

/**
 * 获取分类列表
 * @param projectID 项目id
 */
export function getPkgClassList(projectID: number, params?: { page: number; pageSize: number; showAll: boolean }) {
  return defHttp.get<PkgClassListPageParams>({
    url: `${Api.Projects}/${projectID}/appstore/pkg_class`,
    params,
  });
}

/**
 *创建分类-全量更新
 * @param projectID 项目id
 */
export function setPkgClassList(projectID: number, params?: { pkgClasses: { id?: number; name: string }[] }) {
  return defHttp.post<setPkgClassListResultModel>({
    url: `${Api.Projects}/${projectID}/appstore/pkg_class/full_update`,
    params,
  }, {
    errorMessageMode: 'none',
    successMessageMode: 'none',
  });
}

/**
 * 获取分类列表-带分支信息
 * @param projectID 项目id
 */
export function getPkgClassListWithPkg(projectID: number, params?: { page: number; pageSize: number; showAll: boolean }) {
  return defHttp.get<PkgClassWithpkgListPageParams>({
    url: `${Api.Projects}/${projectID}/appstore/pkg_class/with_pkg`,
    params,
  });
}

/**
 * 游戏包批量排序
 * @param projectID 项目id
 */
export function sortGamePkgs(projectID: number, params?: { idList: number[]; classID: number }) {
  return defHttp.put<PkgClassWithpkgListPageParams>({
    url: `${Api.Projects}/${projectID}/appstore/game_pkgs/sort`,
    params,
  });
}

/**
 * 获取回收站Pkgs列表
 * @param projectID 项目id
 * @param params 筛选条件
 */
export function getGamePackagesRecyclePkgsVersionsListByPage(projectID: number, params?: BasicPageParams) {
  return defHttp.get<GamePackagesVersionsListGetResultModel>({
    url:
      `${Api.Projects}/${projectID}/appstore/recycle_pkgs`,
    params,
  });
}

/**
 * 新增附加链接
 */
export function addExtLinks(projectID: number, params?: { pkgID?: number; versionID?: number; title: string; URL: string }) {
  return defHttp.post<NullableBasicResult>({
    url: `${Api.Projects}/${projectID}/appstore/extLinks`,
    params,
  });
}

/**
 * 删除附加链接
 */
export function deleteExtLinks(projectID: number, extLinkID: number) {
  return defHttp.delete<NullableBasicResult>({
    url: `${Api.Projects}/${projectID}/appstore/extLinks/${extLinkID}`,
  });
}

/**
 * 更新附加链接
 */
export function updateExtLinks(projectID: number, extLinkID: number, params?: { title: string; URL: string; versionID?: number; pkgID?: number }) {
  return defHttp.put<NullableBasicResult>({
    url: `${Api.Projects}/${projectID}/appstore/extLinks/${extLinkID}`,
    params,
  });
}

/**
 * 复制场景（含标准值配置）
 */
export function copyScene(projectID: number, sceneID: number, params?: { branchIds: number[]; copyStandard: boolean }) {
  return defHttp.post<NullableBasicResult>({ url: `${Api.Projects}/${projectID}/perf_map/scenes/${sceneID}/branch`, params });
}

/**
 * 获取场景已配置的分支列表
 */
export function getSceneBranchList(projectID: number, sceneID: number) {
  return defHttp.get<SceneBranchListGetResultModel>({ url: `${Api.Projects}/${projectID}/perf_map/scenes/${sceneID}/branch` });
}

/**
 * 获取游戏包版本日志源
 * @param projectID 项目id
 * @param pkgID 游戏包id
 * @param versionID 版本id
 * @param source 日志源 1 - web前端；2 - Oasis
 */
export function getGamePackagesVersionLogSource(projectID: number, pkgID: number, versionID: number, source: LogSource) {
  return defHttp.get<NullableBasicResult>({ url: `/download/appstore/projects/${projectID}/pkgs/${pkgID}/versions/${versionID}/log_source`, params: { source } });
}
/**
 * 导出包体分析为飞书表格
 * @param projectID 项目id
 * @param doctorId 包体检测id
 */
export function exportDoctorAnalysesApi(projectID: number, doctorId: number) {
  return defHttp.post<{
    url: string;
  }>({ url: `/api/v1/projects/${projectID}/appstore/doctor/analyses/export/${doctorId}` }, {
    successMessageMode: 'none',
  });
}

/**
 * 获取包体检测的全部白名单
 * @param projectID 项目id
 * @param pkgID 游戏包id
 */
export function getGamePackagesWhiteList(projectID: number, pkgID: number) {
  return defHttp.get<GameTestConfigItem[]>({ url: `/api/v1/projects/${projectID}/appstore/pkg/${pkgID}/list` });
}
/**
 * 更新包体检测白名单
 * @param projectID 项目id
 * @param pkgID 游戏包id
 * @param ruleType 规则类型
 * @param params
 * @param params.path 路径
 */
export function updateGamePackagesWhiteList(projectID: number, pkgID: number, ruleType: number, params: { path: string[] }) {
  return defHttp.put<NullableBasicResult>({ url: `/api/v1/projects/${projectID}/appstore/pkg/${pkgID}/whitelist/${ruleType}`, params }, {
    successMessageMode: 'none',
  });
}
