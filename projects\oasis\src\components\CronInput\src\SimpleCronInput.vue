<template>
  <div :class="prefixCls">
    <ARadioGroup
      v-model:value="timerType"
      class="h-32px !mb-5 !flex !items-center"
      :options="[
        { label: '周期触发', value: 1 },
        { label: '间隔触发', value: 2 },
        { label: '仅手动执行', value: 0 },
      ]"
      @change="handleTypeChange()"
    />
    <AFormItemRest>
      <template v-if="timerType === 1">
        <div class="flex flex-wrap items-center gap-y-2">
          每<ATreeSelect
            v-model:value="valueObject.periodWeek"
            placeholder="请选择星期"
            class="!mx-1 !min-w-120px !w-auto"
            :disabled="!isTimer"
            mode="multiple"
            treeCheckable
            :maxTagCount="3"
            showCheckedStrategy="SHOW_PARENT"
            :treeData="daysOfWeek"
            allowClear
            @change="handleChange()"
          />
          <ATreeSelect
            v-model:value="valueObject.periodHour"
            placeholder="请选择小时"
            class="!mx-1 !min-w-100px !w-auto"
            :disabled="!isTimer"
            mode="multiple"
            treeCheckable
            :maxTagCount="3"
            showCheckedStrategy="SHOW_PARENT"
            :treeData="hourOfDays"
            allowClear
            @change="handleChange()"
          />时
          <AInputNumber
            v-model:value="valueObject.periodMinute"
            class="!ml-1 !w-50px"
            :disabled="!isTimer"
            :max="59"
            :min="0"
            :precision="0"
            @change="handleChange()"
          />分
          <AInputNumber
            v-model:value="valueObject.periodSecond"
            class="!ml-1 !w-50px"
            :disabled="!isTimer"
            :max="59"
            :min="0"
            :precision="0"
            @change="handleChange()"
          />秒
          <span class="ml-3">启动执行</span>
        </div>
      </template>
      <template v-else>
        <div class="mb-5">
          从<ADatePicker
            v-model:value="valueObject.startTime"
            class="!mx-1 !w-220px"
            :disabled="!isTimer"
            :showTime="{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }"
            format="YYYY年M月DD日 HH:mm:ss"
            :disabledDate="(current: Dayjs) => current < dayjs().startOf('day')"
            valueFormat="YYYY-MM-DDTHH:mm:ss.SSSZ"
            ,
            @change="handleChange()"
          />开始计时
        </div>
        <div class="flex items-center">
          每隔<AInputNumber
            v-model:value="valueObject.intervalWeek"
            class="!mx-1 !w-50px"
            :disabled="!isTimer"
            :min="0"
            :precision="0"
            @change="handleChange()"
          />周
          <AInputNumber
            v-model:value="valueObject.intervalDay"
            class="!mx-1 !w-50px"
            :disabled="!isTimer"
            :max="6"
            :min="0"
            :precision="0"
            @change="handleChange()"
          />天
          <AInputNumber
            v-model:value="valueObject.intervalHour"
            class="!ml-1 !w-50px"
            :disabled="!isTimer"
            :max="23"
            :min="0"
            :precision="0"
            @change="handleChange()"
          />时
          <AInputNumber
            v-model:value="valueObject.intervalMinute"
            class="!ml-1 !w-50px"
            :disabled="!isTimer"
            :max="59"
            :min="0"
            :precision="0"
            @change="handleChange()"
          />分
          <AInputNumber
            v-model:value="valueObject.intervalSecond"
            class="!ml-1 !w-50px"
            :disabled="!isTimer"
            :max="59"
            :min="0"
            :precision="0"
            @change="handleChange()"
          />秒
          <span class="ml-3">启动执行</span>
        </div>
      </template>
    </AFormItemRest>
  </div>
</template>

<script lang="ts" setup name="SimpleCronInput">
import {
  DatePicker as ADatePicker,
  Form as AForm,
  InputNumber as AInputNumber,
  Radio as ARadio,
  TreeSelect as ATreeSelect,
} from 'ant-design-vue';
import dayjs, { type Dayjs } from 'dayjs';
import { onMounted, reactive, ref } from 'vue';
import { daysOfWeek, hourOfDays } from './setting/setting.data';
import { useDesign } from '/@/hooks/web/useDesign';

const props = defineProps({
  timer: { type: Boolean, default: true },
  crontab: { type: String, default: '' },
  startTime: { type: String, default: '' },
  spec: { type: String, default: '' },
});
const emit = defineEmits(['change']);
const AFormItemRest = AForm.ItemRest;
const ARadioGroup = ARadio.Group;
const { prefixCls } = useDesign('simple-cron-input');

interface valueObjectType {
  periodWeek: number[] | undefined;
  periodHour: number[] | undefined;
  periodMinute: number | undefined;
  periodSecond: number | undefined;
  intervalWeek: number | undefined;
  intervalDay: number | undefined;
  intervalHour: number | undefined;
  intervalMinute: number | undefined;
  intervalSecond: number | undefined;
  startTime: string;
}

const isTimer = ref<boolean>(true);
const timerType = ref<number>(1);
const valueObject = reactive<valueObjectType>({
  periodWeek: undefined,
  periodHour: undefined,
  periodMinute: undefined,
  periodSecond: undefined,
  intervalWeek: undefined,
  intervalDay: undefined,
  intervalHour: undefined,
  intervalMinute: undefined,
  intervalSecond: undefined,
  startTime: '',
});

onMounted(() => {
  isTimer.value = props.timer;
  timerType.value = !props.timer ? 0 : props.spec && props.startTime ? 2 : 1;
  if (timerType.value !== 2) {
    const tempCron = props.crontab ? props.crontab.split(' ') : ['0', '*', '*', '*', '*', '?'];
    valueObject.periodWeek
        = tempCron[5] === '?' ? undefined : tempCron[5].split(',').map((item) => Number(item));
    valueObject.periodHour
        = tempCron[2] === '*' ? undefined : tempCron[2].split(',').map((item) => Number(item));
    valueObject.periodMinute
        = tempCron[1] === '*' ? undefined : Number(props.crontab.split(' ')[1]);
    valueObject.periodSecond
        = tempCron[0] === '*' ? undefined : Number(props.crontab.split(' ')[0]);
  } else {
    const tempSpec = props.spec ? props.spec.replace('@every ', '') : '0h0m0s';
    valueObject.intervalWeek = Math.floor(Number(tempSpec.split('h')[0]) / (7 * 24)) || undefined;
    valueObject.intervalDay
        = Math.floor((Number(tempSpec.split('h')[0]) % (7 * 24)) / 24) || undefined;
    valueObject.intervalHour = Number(tempSpec.split('h')[0]) % 24 || undefined;
    valueObject.intervalMinute = Number(tempSpec.split('h')[1].split('m')[0]);
    valueObject.intervalSecond = Number(tempSpec.split('h')[1].split('m')[1].split('s')[0]);
    valueObject.startTime = props.startTime || '';
  }
});

function handleChange() {
  if (timerType.value === 1) {
    const second
        = valueObject.periodSecond
        || (valueObject.periodWeek?.length
          || valueObject.periodHour?.length
          || valueObject.periodMinute
          ? '0'
          : '*');
    const minute
        = valueObject.periodMinute
        || (valueObject.periodWeek?.length || valueObject.periodHour?.length ? '0' : '*');
    const hour = valueObject.periodHour?.length
      ? valueObject.periodHour.join(',')
      : valueObject.periodWeek?.length
        ? '0'
        : '*';
    const week = valueObject.periodWeek?.length ? valueObject.periodWeek.join(',') : '?';
    const crontab = `${second} ${minute} ${hour} * * ${week}`;
    emit('change', { timer: true, crontab });
  } else {
    emit('change', {
      timer: true,
      spec: `@every ${
        (valueObject.intervalWeek || 0) * 7 * 24
        + (valueObject.intervalDay || 0) * 24
        + (valueObject.intervalHour || 0)
      }h${valueObject.intervalMinute || 0}m${valueObject.intervalSecond || 0}s`,
      startTime: valueObject.startTime,
    });
  }
}

function handleTypeChange() {
  if (timerType.value === 0) {
    isTimer.value = false;
    emit('change', { timer: false });
  } else {
    isTimer.value = true;
    if (timerType.value === 1) {
      emit('change', { timer: true, crontab: '' });
    } else {
      emit('change', { timer: true, startTime: '', spec: '' });
    }
  }
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-simple-cron-input';

// .@{prefix-cls} {

// }
</style>
