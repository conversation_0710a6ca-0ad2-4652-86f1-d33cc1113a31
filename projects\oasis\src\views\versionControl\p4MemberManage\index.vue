<template>
  <div v-track:v="'t1sjzx43yi'" class="p-4-member-management">
    <div class="flex p-4" :class="prefixCls">
      <ScrollContainer class="w-[400px]!">
        <div class="max-h-[calc(100vh_-_210px)]">
          <!-- 项目成员分组 -->
          <div class="h-fit w-[400px] rounded-md bg-FO-Container-Fill1 p-4">
            <div class="h-[24px] flex items-center justify-between">
              <div class="flex items-center">
                <div class="flex items-center font-bold">
                  项目成员分组
                </div>
                <a-select
                  v-model:value="curServerID" showSearch size="small" class="ml-4 w-[180px]"
                  :dropdownMatchSelectWidth="false" optionFilterProp="description" :options="perforceServersList"
                  placeholder="请选择指定服务器" :fieldNames="{
                    label: 'description',
                    value: 'ID',
                  }" @change="handleChangeServer()"
                >
                  <template #option="{ address, description }">
                    <div>{{ description || address }}</div>
                    <div v-if="description" class="ml-5 c-FO-Content-Text2">
                      {{ address }}
                    </div>
                  </template>
                </a-select>
              </div>
              <a-button
                v-if="isPreview && isSuperAdminOrProjectAdmin" v-track="'zre9fwhieu'" shape="round"
                size="small" class="custom-rounded-btn" @click="handleCreate()"
              >
                <Icon icon="carbon:add" size="16" />
                添加
              </a-button>
            </div>
            <div class="my-3 text-xs c-FO-Content-Text2">
              配置项目成员，应用于 <strong>每一个</strong> 分支的提交权限管理
            </div>
            <div :class="`${prefixCls}__tab`">
              <div
                v-for="item in showProjectGroupList" :key="item.ID" :class="`${prefixCls}__tab-item`"
                :active="!isSpecialUser && (item.UUID === activeGroupID || item.ID === activeGroupID)"
                :isNew="!!item.UUID" @click="handleGroupChange(item.UUID || item.ID!)"
              >
                <div class="w-[250px] flex items-center">
                  <Icon icon="icon-park-solid:peoples" />
                  <EllipsisText class="ml-2">
                    {{ item.name }}
                  </EllipsisText>
                </div>
                <div>
                  <APopconfirm
                    v-if="isSuperAdminOrProjectAdmin" title="确定要删除该项目成员分组吗"
                    @confirm.stop="handleDelete(item, 1)"
                  >
                    <a-button type="text" size="small" @click.stop>
                      <Icon class="!c-FO-Functional-Error1-Default" icon="ant-design:delete-outlined" />
                    </a-button>
                  </APopconfirm>
                </div>
              </div>
            </div>
          </div>
          <!-- 特殊干员 -->
          <div class="mt-4 h-fit w-[400px] rounded-md bg-FO-Container-Fill1 p-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center font-bold">
                特殊干员
              </div>
              <a-button
                v-if="isSuperAdminOrProjectAdmin" v-track="'qew8ku1mam'" shape="round" size="small"
                class="custom-rounded-btn" @click="handleCreateSpecialUser()"
              >
                <Icon icon="carbon:add" />
                添加
              </a-button>
            </div>
            <div class="my-3 text-xs c-FO-Content-Text2">
              添加特殊干员，用于单独为特殊干员配置权限
            </div>
            <div v-if="showSpecialUserList.length !== 0" :class="`${prefixCls}__tab`">
              <div v-for="item in showSpecialUserList" :key="item.ID" :class="`${prefixCls}__tab-special-item`">
                <div class="w-[250px] flex items-center">
                  <Icon icon="icon-park-solid:people" />
                  <ATypographyText class="ml-2" :ellipsis="{ tooltip: true }" :content="formatNickName(item.user)" />
                </div>
                <div>
                  <APopconfirm
                    v-if="isSuperAdminOrProjectAdmin" title="确定要删除该特殊干员吗"
                    @confirm.stop="handleDelete(item, 2)"
                  >
                    <a-button type="text" size="small" @click.stop>
                      <Icon class="!c-FO-Functional-Error1-Default" icon="ant-design:delete-outlined" />
                    </a-button>
                  </APopconfirm>
                </div>
              </div>
            </div>
          </div>
          <!-- 特殊干员 -->
          <div class="mt-4 h-fit w-[400px] rounded-md bg-FO-Container-Fill1 p-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center font-bold">
                <span>限制提交干员</span>
                <span
                  class="ml-2 flex cursor-pointer flex-items-center border rounded-[30px] pb-1 pl-2 pr-2 pt-1"
                  @click="openAutoAddNewPeopleModal(true)"
                >
                  <Icon icon="icon-park-outline:dot" class="pr-1 c-[#A7A7A7]" :class="{ ' !c-FO-Functional-Success1-Default': isLimit }" />
                  自动将新人加入
                </span>
              </div>
              <a-button
                v-if="isSuperAdminOrProjectAdmin" shape="round" size="small" class="custom-rounded-btn"
                @click="openLimitSubmitModal(true)"
              >
                <Icon icon="carbon:add" />
                添加
              </a-button>
            </div>
            <div class="my-3 text-xs c-FO-Content-Text2">
              保留干员原本的拉取权限，限制干员的mark、checkout、提交权限
            </div>
            <div v-if="limitSubmitUserList.length !== 0" :class="`${prefixCls}__tab`">
              <div v-for="item in limitSubmitUserList" :key="item.ID" :class="`${prefixCls}__tab-special-item`">
                <div class="w-[250px] flex items-center">
                  <Icon icon="icon-park-solid:people" />
                  <ATypographyText class="ml-2" :ellipsis="{ tooltip: true }" :content="formatNickName(item.user)" />
                  <ATooltip placement="bottom">
                    <template #title>
                      已完成p4接触流程
                    </template>
                    <Icon v-if="item.finishP4Orientation" class="ml-2 font-bold" icon="icon-park-outline:check-small" />
                  </ATooltip>
                </div>
                <div>
                  <APopconfirm
                    v-if="isSuperAdminOrProjectAdmin" title="确定要删除该干员吗"
                    @confirm.stop="handleDelete(item, 3)"
                  >
                    <a-button type="text" size="small" @click.stop>
                      <Icon class="!c-FO-Functional-Error1-Default" icon="ant-design:delete-outlined" />
                    </a-button>
                  </APopconfirm>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ScrollContainer>
      <ScrollContainer class="flex-1">
        <div class="ml-4 max-h-[calc(100vh_-_210px)]">
          <ProjectGroupCrud
            v-if="activeGroupID" :key="activeGroupID" :curGroup="activeGroup" :serverID="curServerID"
            :isPreview="isPreview" :isUpdate="isUpdate" :isNew="isNew" :groupList="groupList"
            :showProjectGroupList="showProjectGroupList" :activeGroupID="activeGroupID"
            @edit-or-revert="handleEditOrRevert" @success="handleSuccess"
          />
        </div>
      </ScrollContainer>
    </div>
    <SpecialUserModal @register="registerModal" @success="getSpecialUserList" />
    <LimitSubmitUserModal @register="registerLimitSubmitModal" @success="getLimitSmitUserList" />
    <AutoAddNewPeopleModal :isLimit="isLimit" @register="autoAddNewPeopleModal" @success="getConfig" />
  </div>
</template>

<script lang="ts" setup name="p4-member">
import type {
  SelectProps,
} from 'ant-design-vue';
import {
  Popconfirm as APopconfirm,
  Tooltip as ATooltip,
  TypographyText as ATypographyText,
} from 'ant-design-vue';
import { onBeforeMount, ref, watch } from 'vue';
import AutoAddNewPeopleModal from './AutoAddNewPeopleModal.vue';
import LimitSubmitUserModal from './LimitSubmitUserModal.vue';
import ProjectGroupCrud from './ProjectGroupCrud.vue';
import SpecialUserModal from './SpecialUserModal.vue';
import type { P4GroupListItem, P4SpecialUserListItem } from '/@/api/page/model/p4Model';
import {
  deleteLimitSmitUser,
  deleteP4Group,
  deleteP4SpecialUser,
  getLimitSmitUserListByProjectID,
  getP4GroupListByPage,
  getP4SpecialUserListByPage,
  getProConfig,
} from '/@/api/page/p4';
import { getPerforceGroupsAndLDAPGroupListByPage } from '/@/api/page/perforce';
import { Icon } from '/@/components/Icon/index';
import { useModal } from '/@/components/Modal';
import { useTrack } from '/@/hooks/system/useTrack';
import { formatNickName } from '/@/hooks/system/useUserList';
import { useDesign } from '/@/hooks/web/useDesign';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { buildNumberUUID } from '/@/utils/uuid';
import { usePerforceServer } from '/@/views/versionControl/perforceManagement/hook';
import { ScrollContainer } from '/@/components/Container';
import { useAdmin } from '../../../hooks/useProjects.ts';

const { prefixCls } = useDesign('p4-member');
const userStore = useUserStoreWithOut();
const [registerModal, { openModal }] = useModal();
const [registerLimitSubmitModal, { openModal: openLimitSubmitModal }] = useModal();
const [autoAddNewPeopleModal, { openModal: openAutoAddNewPeopleModal }] = useModal();
const { getPerforceServerList, perforceServersList } = usePerforceServer();

const activeGroupID = ref<number>();
const activeGroup = ref<P4GroupListItem>();
const showProjectGroupList = ref<P4GroupListItem[]>([]);
const showSpecialUserList = ref<P4SpecialUserListItem[]>([]);
const limitSubmitUserList = ref<P4SpecialUserListItem[]>([]);
const groupList = ref<SelectProps['options']>([]);
const curServerID = ref<number>();
const isSpecialUser = ref<boolean>(false);
const isPreview = ref<boolean>(true);
const isUpdate = ref<boolean>(false);
const isNew = ref<boolean>(false);
const isLimit = ref<boolean>(false);
const { setTrack } = useTrack();
const { isSuperAdminOrProjectAdmin } = useAdmin();

// 获取分组列表
async function getProjectGroupList(id?: number) {
  if (!userStore.getProjectId) {
    return;
  }

  const { list } = await getP4GroupListByPage(userStore.getProjectId, {
    page: 1,
    pageSize: 999,
    serverID: curServerID.value,
  });

  if (list?.length) {
    showProjectGroupList.value = list;
    activeGroupID.value = id || list[0].ID;

    if (!isSpecialUser.value) {
      activeGroup.value = list.find((e) => e.ID === activeGroupID.value);
    }
  } else {
    showProjectGroupList.value = [];
    activeGroupID.value = undefined;
  }
}

// 获取特殊干员列表
async function getSpecialUserList() {
  if (!userStore.getProjectId) {
    return;
  }

  const { list } = await getP4SpecialUserListByPage(userStore.getProjectId, {
    page: 1,
    pageSize: 999,
  });

  if (list?.length) {
    showSpecialUserList.value = list;
  } else {
    showSpecialUserList.value = [];
  }
}

// 获取限制提交干员列表
async function getLimitSmitUserList() {
  if (!userStore.getProjectId) {
    return;
  }

  const { list } = await getLimitSmitUserListByProjectID(userStore.getProjectId);

  if (list?.length) {
    limitSubmitUserList.value = list;
  } else {
    limitSubmitUserList.value = [];
  }
}

async function getConfig() {
  const res = await getProConfig(+userStore.getProjectId, 'p4Restrict');

  if (!res.config) {
    return;
  }

  if (res.config.config) {
    isLimit.value = res.config.config === '1';
  } else {
    isLimit.value = false;
  }
}

// 获取所有组(包括LDAP)列表
async function getGroupList() {
  const { list } = await getPerforceGroupsAndLDAPGroupListByPage(userStore.getProjectId, {
    page: 1,
    pageSize: 999,
    serverID: curServerID.value,
  });

  if (list?.length > 0) {
    groupList.value = list.map((e) => ({ label: e.name!, value: e.name! }));
  } else {
    groupList.value = [];
  }
}

// 切换分组
function handleGroupChange(id: number) {
  if (id === activeGroupID.value) {
    return;
  }

  // 新增状态时切换组 则 删除临时新增的组
  if (!isPreview.value && !isUpdate.value) {
    showProjectGroupList.value = showProjectGroupList.value.filter((e) => !e.UUID);
  }

  isPreview.value = true;
  isNew.value = false;

  activeGroupID.value = id;
  activeGroup.value = showProjectGroupList.value.find((e) => e.ID === id);
}

// 编辑或撤销
function handleEditOrRevert(isEdit: true, id?: number) {
  if (isEdit) {
    handleEdit();
  } else {
    handleSuccess(id);
  }
}

function handleEdit() {
  isPreview.value = false;
  isUpdate.value = true;
  isNew.value = false;
}

// 添加项目分组
function handleCreate() {
  const newID = buildNumberUUID();
  const newGroup = {
    UUID: newID,
    name: '(新增中, 未保存)',
  };

  showProjectGroupList.value.push(newGroup);
  activeGroupID.value = newID;
  isPreview.value = false;
  isUpdate.value = false;
  isNew.value = true;
}

// 添加特殊干员
function handleCreateSpecialUser() {
  openModal(true);
}

// 删除项目分组或特殊干员
async function handleDelete(record: Recordable, specialUser: number) {
  if (record.UUID) {
    showProjectGroupList.value = showProjectGroupList.value.filter((e) => e.UUID !== record.UUID);
    isNew.value = false;
    isPreview.value = true;
    init();
  } else {
    if (specialUser === 1) {
      const res = await deleteP4Group(userStore.getProjectId, record.ID);

      if (res?.code !== 7) {
        getProjectGroupList();
        setTrack('w6gfxv6euk');
      }
    } else if (specialUser === 2) {
      const res = await deleteP4SpecialUser(userStore.getProjectId, record.ID);

      if (res?.code !== 7) {
        setTrack('btyafzdcrk');
        getSpecialUserList();
      }
    } else {
      const res = await deleteLimitSmitUser(userStore.getProjectId, { idList: [record.user.ID] });

      if (res?.code !== 7) {
        getLimitSmitUserList();
      }
    }

    isUpdate.value = false;
  }
}

async function handleSuccess(id?: number, specialUser = false) {
  isPreview.value = true;
  isNew.value = false;

  if (specialUser) {
    getSpecialUserList();
  } else {
    getProjectGroupList(id);
  }
}

async function handleChangeServer() {
  await getGroupList();
  getProjectGroupList();
}

// 刷新
async function init() {
  await getPerforceServerList(userStore.getProjectId);

  if (perforceServersList.value?.length) {
    curServerID.value = perforceServersList.value[0].ID;
    await getGroupList();
    getProjectGroupList();
  }

  getSpecialUserList();
  getLimitSmitUserList();
  getConfig();
}

onBeforeMount(() => {
  init();
});

watch(
  () => userStore.getProjectId,
  (v, oldValue) => {
    if (v && v !== oldValue) {
      init();
    }
  },
);
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-p4-member';
.@{prefix-cls} {
  &__tab {
    margin: 8px 0;

    &-item {
      padding: 8px;
      margin: 8px 0;
      border: 1px solid transparent;
      border-radius: 6px;
      display: flex;
      justify-content: space-between;
      cursor: pointer;

      &:hover {
        border-color: @FO-Container-Stroke1;
      }

      &[active='true'] {
        border-color: @FO-Brand-Primary-Default;
      }

      &[isNew='true'] {
        border-style: dashed;
        color: @FO-Functional-Error1-Default;
      }
    }

    &-special-item {
      padding: 8px;
      margin: 8px 0;
      border: 1px solid transparent;
      border-radius: 6px;
      display: flex;
      justify-content: space-between;
    }
  }
}
</style>
