import { uniqBy } from 'lodash';
import type { PagedQueryParam, PagedRes } from '../api/_common.ts';
import { type MaybeRefOrGetter, computed, reactive, readonly, shallowRef, toValue, watch } from 'vue';
import { useLatestPromise } from '@hg-tech/utils-vue';

interface PageInfo {
  page: number;
  pageSize: number;
  total?: number;
}

export type UseInfinityLoadReq<T> = (pageInfo: PagedQueryParam) => Promise<PagedRes<T> | undefined>;
export function useInfinityLoad<T>(
  req: MaybeRefOrGetter<UseInfinityLoadReq<T>>,
  options: {
    getKey: (item: T) => string | number | undefined;
    clearOnChange?: boolean; // 是否在请求函数变化时清空数据
    pageLimit?: number;
    immediate?: boolean;
  },
) {
  const currentList = shallowRef<T[]>([]);
  const pageInfo = reactive<PageInfo>({ page: 0, pageSize: options.pageLimit ?? 20, total: 0 });
  const isLoadingMore = shallowRef(false);
  const { execute, loading: isReloading, getPendingPromise } = useLatestPromise(((...args) => toValue(req)(...args)) as UseInfinityLoadReq<T>);
  const isEnd = computed(() => pageInfo.page > 0 && pageInfo.total != null && pageInfo.page * pageInfo.pageSize >= pageInfo.total);

  async function loadMore() {
    if (isEnd.value) {
      return;
    }
    isLoadingMore.value = true;
    toValue(req)({ page: pageInfo.page + 1, pageSize: pageInfo.pageSize }).then((res) => {
      currentList.value = uniqBy([...currentList.value, ...(res?.list ?? [])], options.getKey);
      pageInfo.page = res?.page ?? pageInfo.page + 1;
      pageInfo.pageSize = res?.pageSize ?? pageInfo.pageSize;
      pageInfo.total = res?.total ?? 0;
    }).finally(() => isLoadingMore.value = false);
  }

  async function reload() {
    execute({ page: 1, pageSize: pageInfo.pageSize });

    return getPendingPromise()?.then(
      (res) => {
        currentList.value = (res?.list ?? []);
        pageInfo.page = res?.page ?? 1;
        pageInfo.pageSize = res?.pageSize ?? pageInfo.pageSize;
        pageInfo.total = res?.total ?? 0;
      },
      () => {
        currentList.value = ([]);
        resetPageInfo();
      },
    );
  }

  watch(() => toValue(req), (f, prvF) => {
    if (f !== prvF) {
      if (options.clearOnChange) {
        currentList.value = [];
      }
      resetPageInfo();
      reload();
    }
  }, { immediate: options.immediate });

  function resetPageInfo() {
    pageInfo.page = 0;
    pageInfo.pageSize = options.pageLimit ?? 20;
    pageInfo.total = 0;
  }

  return {
    currentList: computed(() => currentList.value),
    pageInfo: computed(() => readonly(pageInfo)),
    isEnd,
    isReloading,
    isLoadingMore: computed(() => isLoadingMore.value),
    isLoading: computed(() => isReloading.value || isLoadingMore.value),
    reload,
    loadMore,
  };
}
