import type { PermissionDeclaration } from '@hg-tech/oasis-common';

declare module 'vue-router' {
  interface RouteMeta {
    /**
     * 跳过父应用的埋点上报
     */
    microApp?: {
      name: string;
      url: string;
      configs: MicroAppConfig;
      title?: string;
      selfTrack?: boolean;
    };
    /**
     * 路由权限限制
     */
    permissionDeclare?: PermissionDeclaration;
    /**
     * 在顶层 routeView 是否禁用 key 刷新
     * @deprecated 后续需要默认禁止 key 刷新
     */
    disableRouteViewKeyAttr?: boolean;
    /**
     * 页面标题
     * @default ForgeonTitleMap[name(PlatformEnterPoint)]
     */
    title?: string;
    /**
     * 分组标题
     */
    groupTitle?: string;
  }
}
