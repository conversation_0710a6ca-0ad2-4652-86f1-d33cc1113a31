<template>
  <Drawer
    :open="show"
    width="900px"
    :closable="false"
    placement="right"
    :afterClose="modalDestroy"
    @cancel="() => modalCancel()"
  >
    <Form :model="formState" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
      <div>
        <BorderBox subLabel="实例名称、IP地址、端口号为实例注册时指定" label="基本配置">
          <FormItem label="Workspace">
            <Input v-model:value="formState.workspace" disabled />
          </FormItem>
          <FormItem label="IP地址">
            <Input v-model:value="formState.ip" disabled />
          </FormItem>
          <FormItem label="端口号">
            <Input v-model:value="formState.port" disabled />
          </FormItem>
          <FormItem label="备注名称">
            <Input v-model:value="formState.name" />
          </FormItem>
        </BorderBox>
        <BorderBox subLabel="流水线拥有重启实例" label="流水线配置">
          <FormItem label="Jenkins url">
            <Input v-model:value="formState.piplineUrl " />
          </FormItem>
        </BorderBox>
        <BorderBox subLabel="由实例在注册时指定实例检查类型" label="匹配配置">
          <FormItem label="实例类型">
            <Input v-model:value="formState.instanceType" disabled />
          </FormItem>
        </BorderBox>
      </div>
    </Form>
    <template #title>
      <div class="relative flex items-center justify-center">
        <Icon :icon="LeftCloseIcon" class="absolute left-5px h-20px w-20px transform-rotate--180 cursor-pointer" @click="() => modalCancel()" />
        <div>配置实例</div>
      </div>
    </template>
    <template #footer>
      <div class="flex justify-end gap-10px">
        <Button type="primary" @click="save">
          保存
        </Button>
        <Button @click="() => modalCancel()">
          取消
        </Button>
      </div>
    </template>
  </Drawer>
</template>

<script lang="tsx" setup>
import type { ModalBaseProps } from '@hg-tech/utils-vue';
import { Button, Drawer, Form, FormItem, Input } from 'ant-design-vue';
import LeftCloseIcon from '@iconify-icons/line-md/arrow-close-left';
import BorderBox from '../../../components/BorderBox.vue';
import { reactive, watch } from 'vue';
import type { ExampleConfigItem } from '../../../api';
import { instanceTypeOptions } from '../type.data';
import { Icon } from '@iconify/vue';

const props = defineProps<ModalBaseProps<{ updatedItem?: undefined }> & {
  item?: ExampleConfigItem;
  sentReq: (name: string) => Promise<undefined>;
}>();

const formState = reactive({
  workspace: '',
  ip: '',
  port: '',
  name: '',
  instanceType: '',
  piplineUrl: '',
});

watch(() => props.item, (val) => {
  if (!val) {
    return;
  }
  formState.name = val?.name || '';
  formState.workspace = val?.workspace || '';
  formState.piplineUrl = val?.piplineUrl || '';
  const url = new URL(val?.ipAddress || '');
  formState.ip = url.hostname || '';
  formState.port = url.port || '';
  formState.instanceType = instanceTypeOptions.find((i) => i.value === val?.instanceType)?.label || '';
}, {
  immediate: true,
});

async function save() {
  const updatedItem = await props.sentReq?.(formState.name);
  return props.modalConfirm({ updatedItem });
}
</script>
