import { type PermissionInfo, PlatformEnterPoint } from '@hg-tech/oasis-common';
import { useCurrentProjectInfo } from '/@/hooks/useProjects.ts';
import { useUserStoreWithOut } from '/@/store/modules/user.ts';

/**
 * 获取模块权限点
 */
function getModulePermissions(moduleName: string): Promise<string[]> {
  const { projectInfo } = useCurrentProjectInfo();
  if (!projectInfo.value?.name) {
    return Promise.resolve([]);
  }
  return import('../../api/auth.ts').then(({ getAppPermissions }) => getAppPermissions({}, {
    appCode: moduleName,
    tenant: projectInfo.value?.name,
  }).then((r) => r.data.data?.permissions || []));
}

/**
 * 所有子应用的权限获取接口
 */
const scopeFetchers: Partial<Record<PlatformEnterPoint, (projectId?: number) => Promise<string[]>>> = {
  [PlatformEnterPoint.SysAdmin]: () => {
    return import('../../api/admin.ts').then(({ getAdminPermissions }) => getAdminPermissions({}, {}).then((r) => r.data.data?.permissions || []));
  },
  [PlatformEnterPoint.SysAigc]: () => {
    return import('../../api/aigc.ts').then(({ getAigcPermissions }) => getAigcPermissions({}, {}).then((r) => r.data.data || []));
  },
  [PlatformEnterPoint.DeptAsset]: async () => {
    const userStore = useUserStoreWithOut();
    if (!userStore.getUserInfo?.userName) {
      await userStore.getUserInfoAction();
    }
    return import('../../api/deptAsset.ts').then(({ getDeptAssetPermissionPointList }) =>
      getDeptAssetPermissionPointList({}, { subject: { type: 'user', id: userStore.getUserInfo?.userName } }).then((r) => r.data?.data?.permissions || []));
  },
  [PlatformEnterPoint.Conflux]: () => getModulePermissions('Conflux'),
};

export async function getScopePermissions(projectId?: number): Promise<PermissionInfo['scopes']> {
  const scopes: PermissionInfo['scopes'] = {};

  await Promise.all(Object.entries(scopeFetchers).map(([name, fetcher]) => {
    return fetcher(projectId).then(
      (permissions) => scopes[name] = permissions,
      () => void 0, // 忽略报错
    );
  }));
  return scopes;
}
