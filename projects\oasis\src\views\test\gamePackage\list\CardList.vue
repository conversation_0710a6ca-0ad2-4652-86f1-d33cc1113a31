<template>
  <div :class="prefixCls">
    <div v-if="!isTop || packageVersionList?.length" :class="`${prefixCls}__card-list`">
      <div v-if="!isRecycle" class="mb-3 flex items-center">
        <div class="font-bold">
          {{ isTop ? '置顶' : '安装包' }}
        </div>
        <BasicButton
          v-if="!isTop && isPackageAdmin && !mtl" v-track="'g5vezhip56'" size="small" shape="round"
          :class="`${prefixCls}__btn ml-3 relative !pr-2`" @click="handleAddVersion()"
        >
          <Icon icon="icon-park-outline:plus" class="!absolute !left-[5px] !top-[4px]" :size="14" />
          添加版本
        </BasicButton>
      </div>
      <List v-if="packageVersionList?.length" :grid="{ gutter: 16 }" :data-source="packageVersionList" :class="`${prefixCls}__card-list-container`">
        <template #renderItem="{ item }">
          <ListItem>
            <div
              :class="`${prefixCls}__card-list-item`"
              :style="{ background: colorList[item && item.color ? item.color : 0][isDark ? 2 : 0], borderColor: colorList[item && item.color ? item.color : 0][isDark ? 3 : 1] }"
              @click="handleCardClick(item)"
            >
              <div :class="`${prefixCls}__card-list-item-top`">
                <div v-if="isTop" :class="`${prefixCls}__card-list-item-top-pin`">
                  <Icon icon="mdi:pin" />
                </div>
              </div>
              <div
                v-if="selectMode" :class="`${prefixCls}__card-list-item-check`" :mode="selectMode"
                :disabled="!ifCanSelect(item) || isDifferentPlatForm(item)"
                :isDifferent="ifCanSelect(item) && isDifferentPlatForm(item)"
              >
                <Checkbox v-model:checked="item.checked" :disabled="!ifCanSelect(item) || isDifferentPlatForm(item)" />
              </div>
              <div class="relative h-[80px] w-[80px] flex-none self-center overflow-hidden rounded-2xl">
                <img :src="preprocessFilePath(item.icon)" alt="icon" class="h-full w-full">
                <div
                  v-if="item.doctorID && !selectMode && !isRecycle"
                  v-tippy="{ content: '点击查看检测报告', placement: 'right' }"
                  class="absolute bottom-0 right-0 h-[40px] w-[40px] flex cursor-pointer items-end justify-end"
                  @click.stop="() => openDoctorPage(item)"
                >
                  <div class="absolute bottom-[-50%] right-[-50%] h-full w-full rotate-[45deg] scale-115 bg-#007E0D transition-all hover:bg-#0db31d" />
                  <Icon class="pointer-events-none z-1 m-[4px] c-FO-Content-Components1" icon="gameStore-doctor|svg" />
                </div>
              </div>
              <div class="w-[280px] flex flex-col">
                <EllipsisText class="font-size-[14px] font-bold">
                  {{ item.version }}
                </EllipsisText>
                <div class="flex text-xs">
                  <div>
                    <b>{{ getPlatformIconByVal(item.platform)?.label }}</b>
                  </div>
                  <div class="ml-2">
                    <b>{{ formatKBSize(item.sizeKB) }}</b>
                  </div>
                  <div class="ml-2">
                    <b>{{ dayjs(item.CreatedAt).format('YYYY/MM/DD HH:mm') }}</b>
                  </div>
                </div>
                <CardLabel :item="item" />
                <div v-if="item.top" class="flex items-center text-xs c-FO-Content-Text2">
                  <div class="w-[40px]">
                    备注:
                  </div>
                  <EllipsisText :key="item?.ID" class="min-w-0 flex-1 !max-w-[280px]">
                    {{ item?.releaseNote || '无' }}
                  </EllipsisText>
                </div>
              </div>
              <div class="relative z-1 flex flex-col justify-center gap-[6px]">
                <div v-if="isRecycle" class="h-[32px] flex justify-center">
                  <Popconfirm title="确定恢复?" @confirm="handleRecycle(item)">
                    <BasicButton shape="round" :class="`${prefixCls}__btn-pro`" size="small" @click.stop>
                      <Icon icon="icon-park-outline:undo" size="14" class="mr-[2px]" />
                      <span class="font-bold !m-0">恢复</span>
                    </BasicButton>
                  </Popconfirm>
                </div>
                <ButtonFolded v-else-if="item.pkgFile !== 'null' && !selectMode && !oasis && !isApple && !userStore.isOutSourcing && !mtl">
                  <template #default="{ hovered }">
                    <BasicButton
                      v-track="'jo22nprtos'"
                      shape="round"
                      :class="[hovered ? `${prefixCls}__download-btn-pro` : 'bg-[#f8cc28]! c-[#000]! b-[#f8cc28]!']"
                      size="small"
                      @click.stop="() => handleDownload(item)"
                    >
                      <div class="flex items-center gap-1">
                        <Icon icon="charm:download" />
                        <span class="min-w-[3em] text-center font-bold !ml-0">下载</span>
                      </div>
                    </BasicButton>
                  </template>
                  <template #addition>
                    <BasicButton
                      v-track="'jo22nprtos'"
                      shape="round"
                      :class="`${prefixCls}__download-btn-pro`"
                      size="small"
                      @click.stop="() => handleDownloadByOasis(item)"
                    >
                      <div class="flex items-center gap-1">
                        <Icon icon="charm:download" />
                        <span class="font-bold !ml-0">通过Oasis下载</span>
                      </div>
                    </BasicButton>
                  </template>
                </ButtonFolded>
                <BasicButton
                  v-else-if="item.pkgFile !== 'null' && !selectMode && (oasis || isApple || userStore.isOutSourcing || mtl)"
                  v-track="'jo22nprtos'"
                  shape="round"
                  :class="`${prefixCls}__btn-pro`" size="small"
                  @click.stop="() => handleDownload(item)"
                >
                  <div class="flex items-center gap-1">
                    <Icon icon="charm:download" />
                    <span class="min-w-[3em] text-center font-bold !ml-0">{{ mtl ? '安装' : '下载' }}</span>
                  </div>
                </BasicButton>
                <BasicButton
                  v-if="!mtl && hasCloudPhonePermission && !selectMode && [DevicePlatform.Android, DevicePlatform.iOS].includes(item.platform) && !isRecycle"
                  shape="round"
                  :class="`${prefixCls}__btn-pro`"
                  size="small"
                  @click.stop="() => openCloudPhonePage(pkgID, item.ID)"
                >
                  <div class="flex items-center gap-1">
                    <Icon :icon="LinkCloud" />
                    <span class="min-w-[3em] text-center font-bold !ml-0">云真机</span>
                  </div>
                </BasicButton>
              </div>
              <div
                class="absolute bottom-0 right-[20px] h-[40px] w-[60px] overflow-hidden"
                :class="{
                  '!h-[36px]': ![1, 6, 9, 10].includes(item.platform),
                }"
              >
                <Icon
                  class="opacity-60" :icon="getPlatformIconByVal(item.platform)?.icon"
                  :style="{ color: getPlatformIconByVal(item.platform)?.color }" :size="60"
                />
              </div>
            </div>
          </ListItem>
        </template>
      </List>
      <Empty v-else-if="showEmpty" :image="emptyImg" description="暂无游戏包" />
    </div>
    <DetailModal @register="registerModal" @success="handleSuccess" />
    <VersionDrawer @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" setup>
import { Checkbox, Empty, List, Popconfirm } from 'ant-design-vue';
import LinkCloud from '@iconify-icons/icon-park-outline/link-cloud';
import { colorList } from './color.data';
import dayjs from 'dayjs';
import { chunk, forIn } from 'lodash-es';
import { type PropType, computed, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import DetailModal from './DetailModal.vue';
import VersionDrawer from './version/VersionDrawer.vue';
import {
  type GameLabelsListItem,
  type GamePackagesListItem,
  type GamePackagesVersionListSearchParamsModel,
  type GamePackagesVersionsListItem,
  LogSource,
} from '/@/api/page/model/testModel';
import { getGamePackagesRecycleVersionsListByPage, getGamePackagesVersionByID, getGamePackagesVersionLogSource, getGamePackagesVersionsListByPage, recoverGamePackagesRecycleVersionByID } from '/@/api/page/test';
import { useDrawer } from '/@/components/Drawer';
import Icon from '/@/components/Icon';
import { useModal } from '/@/components/Modal';
import { useDesign } from '/@/hooks/web/useDesign';
import { useMessage } from '/@/hooks/web/useMessage';
import { useUserStore } from '/@/store/modules/user';
import { openWindow } from '/@/utils';
import CardLabel from './CardLabel.vue';
import { formatKBSize } from '/@/utils/file/size';
import { platformOptions } from '/@/views/test/gamePackage/settings/settings.data';
import { getAllPaginationList } from '/@/hooks/web/usePagination';
import { useDarkModeTheme } from '/@/hooks/setting/useDarkModeTheme';
import { getTempToken } from '/@/api/sys/user';
import { isApple, isNullOrUnDef } from '/@/utils/is';
import { downloadByUrl } from '/@/utils/file/download';
import { useGlobSetting } from '/@/hooks/setting';
import { PlatformEnterPoint, preprocessFilePath } from '@hg-tech/oasis-common';
import { useIsPackageAdmin } from '../useIsPackageAdmin.ts';
import { EllipsisText } from '../../../../components/EllipsisText';
import BasicButton from '../../../../components/Button/src/BasicButton.vue';
import ButtonFolded from '../../../../components/ButtonFolded.vue';
import { usePermissionCheckPoint } from '../../../../service/permission/usePermission.ts';
import { DevicePlatform } from '../../../toolkit/settings/toolkitSettings.data.ts';
import { useCloudPhonePage } from './useCloudDevicePage.ts';
import { useCurrentProjectInfo } from '../../../../hooks/useProjects.ts';
import { useRouteQuery } from '@vueuse/router';

const props = defineProps({
  pkgID: {
    type: Number,
  },
  platforms: {
    type: Array as PropType<number[]>,
    default: () => [],
  },
  packageList: {
    type: Array as PropType<GamePackagesListItem[]>,
    default: () => [],
  },
  allLabels: {
    type: Array as PropType<GameLabelsListItem[]>,
    default: () => [],
  },
  searchParams: {
    type: Object as PropType<GamePackagesVersionListSearchParamsModel>,
    default: () => {},
  },
  isTop: {
    type: Boolean,
    default: false,
  },
  selectMode: {
    type: String,
    default: '',
  },
  selectVersionList: {
    type: Array as PropType<GamePackagesVersionsListItem[]>,
    default: () => [],
  },
  isRecycle: {
    type: Boolean,
    default: false,
  },
  showEmpty: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(['success', 'checkChange']);

const { isDark } = useDarkModeTheme();

const ListItem = List.Item;
const emptyImg = Empty.PRESENTED_IMAGE_SIMPLE;
const { prefixCls } = useDesign('game-package-card-list');

const [registerModal, { openModal }] = useModal();
const [registerDrawer, { openDrawer }] = useDrawer();
const { resolve } = useRouter();
const urlVersion = useRouteQuery('v', undefined, { transform: (v) => (!isNullOrUnDef(v) ? Number(v) : undefined) });
const urlProjectID = useRouteQuery('p', undefined, { transform: (v) => (!isNullOrUnDef(v) ? Number(v) : undefined) });
const oasis = useRouteQuery('oasis', 0, { transform: (v) => (!isNullOrUnDef(v) ? Number(v) : 0) });
const mtl = useRouteQuery('mtl', 0, { transform: (v) => (!isNullOrUnDef(v) ? Number(v) : 0) });

const userStore = useUserStore();
const { projectInfo } = useCurrentProjectInfo();
const { isPackageAdmin } = useIsPackageAdmin();
const [hasCloudPhonePermission] = usePermissionCheckPoint({
  scope: PlatformEnterPoint.DeptAsset,
  any: ['useCloudPhone'],
});
const packageVersionList = ref<GamePackagesVersionsListItem[]>([]);
const { createMessage } = useMessage();
/** 第一个选择的平台 */
const firstSelectPlatform = ref<number>();
/** 选择的数量 */
const selectedCount = computed(() => packageVersionList.value.filter((e) => e.checked).length);

/** 是否可以选择 */
function ifCanSelect(item: GamePackagesVersionsListItem) {
  return props.selectMode && (props.selectMode === 'delete' || item.doctorID);
}

/** 是否不同平台 */
function isDifferentPlatForm(item: GamePackagesVersionsListItem) {
  return !!(
    props.selectMode === 'compare'
    && firstSelectPlatform.value
    && item.platform !== firstSelectPlatform.value
  );
}

/** 获取host, 将http替换为https */
const { beOrigin } = useGlobSetting();

const curPkg = computed(() => {
  return props.packageList.find((e: GamePackagesListItem) => e.ID === props.pkgID);
});

/** 获取包体版本列表 */
async function getPackageVersionList() {
  if (!userStore.getProjectId || !props.pkgID) {
    return;
  }

  const labelList: string[] = [];
  const { labels, platform, doctor, colors } = props.searchParams;

  forIn(labels, (val, key) => {
    if (Array.isArray(val)) {
      val.forEach((e) => {
        labelList.push(JSON.stringify({ labelID: Number(key), valueID: e }));
      });
    } else if (val) {
      labelList.push(JSON.stringify({ labelID: Number(key), valueID: val }));
    }
  });

  const { list } = await getAllPaginationList((p) => getGamePackagesVersionsListByPage(
    userStore.getProjectId,
    props.pkgID!,
    {
      ...p,
      platform,
      passDoctor: doctor,
      top: !!props.isTop,

    },
    labelList,
    colors,
  ));

  if (list?.length > 0) {
    packageVersionList.value = list.map((e: GamePackagesVersionsListItem) => {
      e.checked = props.selectVersionList.some((item) => item.ID === e.ID);

      return e;
    });
    firstSelectPlatform.value = undefined;

    const findVersion = list.find((item: GamePackagesVersionsListItem) => item.ID === urlVersion.value);

    if (findVersion && urlProjectID.value && urlProjectID.value === userStore.getProjectId) {
      handleCardClick(findVersion, true);
    }
  } else {
    packageVersionList.value = [];
  }

  return packageVersionList.value;
}

/** 获取回收站版本列表 */
async function getRecycleVersionList() {
  if (!userStore.getProjectId || !props.pkgID) {
    return;
  }

  const { list } = await getAllPaginationList((p) => getGamePackagesRecycleVersionsListByPage(
    userStore.getProjectId,
    props.pkgID!,
    p,
  ));

  packageVersionList.value = list || [];

  return packageVersionList.value;
}

/** 获取平台icon */
function getPlatformIconByVal(val: number) {
  return platformOptions.find((e) => e.value === val);
}

function handleAddVersion() {
  if (props.selectMode) {
    createMessage.warning('请先退出选择模式');

    return;
  }

  openDrawer(true, {
    isUpdate: false,
    pkgID: props.pkgID,
    platforms: curPkg.value?.platforms,
  });
}

/** 处理卡片点击事件 */
function handleCardClick(item: GamePackagesVersionsListItem, needDownload = false) {
  if (mtl.value) {
    return;
  }

  if (props.selectMode) {
    if (props.selectMode === 'compare') {
      if (!item.doctorID) {
        return;
      } else if (!item.checked && selectedCount.value >= 5) {
        createMessage.warning('最多选择5个进行对比');

        return;
      } else if (firstSelectPlatform.value && item.platform !== firstSelectPlatform.value) {
        return;
      }
    }

    firstSelectPlatform.value ??= item.platform;
    item.checked = !item.checked;

    if (!selectedCount.value) {
      firstSelectPlatform.value = undefined;
    }

    emit('checkChange', item);

    return;
  }

  openModal(true, {
    detail: item,
    pkg: curPkg.value,
    needDownload,
    allLabels: props.allLabels,
    isRecycle: props.isRecycle,
  });
}

/** 打开包体检测详情页 */
function openDoctorPage(item: GamePackagesVersionsListItem) {
  if (!item.doctorID) {
    return;
  }

  const { href } = resolve({
    name: PlatformEnterPoint.GamePackageDoctor,
    params: {
      id: props.pkgID,
      doctorID: item.doctorID,
    },
    query: {
      fs: 1,
      p: userStore.getProjectId,
    },
  });

  openWindow(href);
}

async function handleDownloadByOasis(item: GamePackagesVersionsListItem) {
  openWindow(`oasisdownload://type=1&project=${userStore.getProjectId}&branch=${item.pkgID}&version=${item.ID}`, { target: '_self' });
}

const { openCloudPhonePage } = useCloudPhonePage(oasis);

async function handleDownload(item: GamePackagesVersionsListItem) {
  if (!item.pkgID || !item.ID) {
    return;
  }

  let tempToken = '';

  if (oasis.value === 0) {
    const { token } = await getTempToken();

    tempToken = token;
  }

  const fileName = item.pkgFile?.split('/').pop() || '';
  const authUrl = `${beOrigin}/download/appstore/projects/${userStore.getProjectId}/pkgs/${item.pkgID}/versions/${item.ID}/package/${fileName}?token=${tempToken}`;
  const downloadUrl
      = oasis.value === 1 || mtl.value
        ? `${beOrigin}/${item.pkgFile}`
        : oasis.value === 2
          ? `${beOrigin}/${item.pkgFile}?projectID=${userStore.getProjectId}&pkgID=${item.pkgID}&versionID=${item.ID}`
          : authUrl;

  const downList: { url: string; fileName: string }[] = [];

  downList.push({ url: downloadUrl, fileName });
  // 调用接口获取附件
  const { repkgVersion } = await getGamePackagesVersionByID(
    Number(userStore.getProjectId),
    item.pkgID,
    item.ID,
  );
  if (mtl.value) {
    window.parent.postMessage({
      type: 'download',
      data: {
        projectAlias: projectInfo.value?.alias || '',
        projectID: userStore.getProjectId,
        packageID: item.pkgID,
        versionID: item.ID,
        name: item.version,
      },
    }, '*');
    return;
  } else if (!oasis.value) {
    // 非 OASIS 内使用 AuthUrl 下载附件
    repkgVersion.attachments?.forEach((repkgVersionItem) => {
      if (repkgVersionItem.downloadLink) {
        if (props.isRecycle) {
          return;
        }

        const fileName = repkgVersionItem.downloadLink.split('/').pop();
        const authUrl = `${beOrigin}/download/appstore/projects/${userStore.getProjectId}/pkgs/${item?.pkgID}/versions/${item?.ID}/atts/${repkgVersionItem.ID}/${fileName}?token=${tempToken}`;

        downList.push({ url: authUrl, fileName: repkgVersionItem.name! });
      }
    });
  }
  chunk(downList, 10).forEach((list, i) => {
    setTimeout(
      () => {
        list.forEach((item) => {
          downloadByUrl(item);
        });
      },
      i === 0 ? 0 : 3000,
    );
  });
  getGamePackagesVersionLogSource(userStore.getProjectId, item.pkgID, item.ID, oasis.value !== 0 ? LogSource.Oasis : LogSource.Web);
  createMessage.success(oasis.value !== 0 ? '已唤起Oasis' : '已开始下载游戏包...');
}

async function handleRecycle(item: GamePackagesVersionsListItem) {
  const res = await recoverGamePackagesRecycleVersionByID(Number(userStore.getProjectId), item.pkgID!, item.ID!);

  if (res?.code !== 7) {
    handleSuccess();
  }
}

function handleSuccess() {
  emit('success', true);
}

watch(
  () => props.selectMode,
  (val) => {
    if (!val) {
      packageVersionList.value.forEach((e) => {
        e.checked = false;
      });
      firstSelectPlatform.value = undefined;
    }
  },
);

defineExpose({
  getList: props.isRecycle ? getRecycleVersionList : getPackageVersionList,
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-game-package-card-list';
.@{prefix-cls} {
  &__compare-popover {
    &-title {
      position: relative;
      padding-left: 4px;
      border-left: 3px solid @FO-Brand-Primary-Default;
      line-height: 20px;
    }

    &-list {
      margin: 16px 0;

      &-item {
        position: relative;
        margin-bottom: 8px;
        overflow: hidden;

        &-delete {
          position: absolute;
          z-index: 1;
          top: 40%;
          right: 2px;
          color: @FO-Content-Text2;
          cursor: pointer;
        }
      }

      &-empty {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        padding: 16px;
        color: @FO-Content-Text2;
        font-size: 13px;
      }
    }
  }

  &__card-list {
    padding: 16px 16px 0;

    & .ant-list .ant-list-item {
      padding: 0;
    }

    &-item {
      display: flex;
      position: relative;
      padding: 12px;
      width: 500px;
      gap: 12px;
      border: 2px solid @FO-Container-Stroke1;
      border-radius: 20px;
      background-color: @FO-Container-Fill1;
      cursor: pointer;
      user-select: none;

      &:hover {
        border-color: #dedede;
        z-index: 10;
      }

      &-top {
        position: absolute;
        top: -2px;
        left: -2px;
        width: 60px;
        height: 60px;
        overflow: hidden;
        border-radius: 20px 0 0;

        &-pin {
          display: flex;
          position: absolute;
          z-index: 1;
          top: -30px;
          left: -30px;
          align-items: flex-end;
          justify-content: center;
          width: 60px;
          height: 60px;
          padding: 2px;
          transform: rotate(-45deg);
          background-color: #f8cc28;
          color: #000;
        }
      }

      &-check {
        position: absolute;
        z-index: 2;
        top: 43px;
        right: 43px;
        transform: scale(2.5);

        &:not([disabled='true']) {
          & .ant-checkbox-inner {
            border-color: @FO-Functional-Error1-Default !important;
            background-color: @FO-Container-Fill1 !important;
          }

          & .ant-checkbox-checked > .ant-checkbox-inner {
            background-color: @FO-Functional-Error1-Default !important;
          }

          &[mode='compare'] {
            & .ant-checkbox-inner {
              border-color: @FO-Functional-Warning1-Default !important;
              background-color: @FO-Container-Fill1 !important;
            }

            & .ant-checkbox-checked > .ant-checkbox-inner {
              background-color: @FO-Functional-Warning1-Default !important;
            }
          }
        }

        &[disabled='true'] {
          &::after {
            content: '未检测';
            position: absolute;
            z-index: 1;
            top: 42%;
            left: 2px;
            color: @FO-Content-Text2;
            font-size: 4px;
            line-height: 4px;
            cursor: not-allowed;
            user-select: none;
          }
        }

        &[isDifferent='true'] {
          &::after {
            content: '不同平台';
            position: absolute;
            z-index: 1;
            top: 30%;
            left: 3px;
            color: @FO-Content-Text2;
            font-size: 5px;
            line-height: 5px;
            cursor: not-allowed;
            user-select: none;
          }
        }
      }

      &-has-after-hoverable {
        &:not(:hover) {
          &::after {
            content: '';
            position: absolute;
            z-index: 1;
            top: 0;
            right: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to right, transparent 90%, @FO-Container-Fill1);
          }
        }
      }
      &-hoverable {
        display: flex;
        position: relative;
        align-items: center;
        max-width: 300px;
        min-height: 24px;
        overflow: hidden;

        &:not([disabled='true']):hover {
          z-index: 10;
          width: auto;
          max-width: 316px;
          overflow: visible;

          & > div {
            position: absolute;
            top: -4px;
            left: -8px;
            flex-wrap: wrap;
            padding: 8px;
            border-radius: 8px;
            box-shadow: 0 0 10px 0 #00000040;
            row-gap: 4px;
          }
        }
      }

      &-label {
        display: flex;
        align-items: center;
        margin-right: 4px;
        padding: 0 4px;
        border-radius: 4px;
        color: #fff;
        font-size: 10px;
        line-height: 16px;
        white-space: nowrap;

        &[isSingle='true'] {
          border-radius: 100px;
        }
      }
      /** 菱形背景 */
      &-mark {
        position: relative;

        &-before {
          position: absolute;
          z-index: 1;
          top: 0;
          left: 0;

          &::before {
            content: ' ';
            position: absolute;
            z-index: 2;
            top: 0;
            left: 0;
            width: 0;
            height: 0;
            border-top: 8px solid @FO-Container-Fill1;
            border-right: 8px solid transparent;
            border-bottom: 8px solid @FO-Container-Fill1;
          }
        }

        &-name {
          display: flex;
          position: relative;
          align-items: center;
          margin-right: 4px;
          padding: 0 10px;
          color: #fff;
          font-size: 10px;
          line-height: 16px;
          white-space: nowrap;
        }

        &-after {
          position: absolute;
          top: 0;
          right: 0;

          &::after {
            content: ' ';
            position: absolute;
            z-index: 2;
            top: 0;
            left: -12px;
            width: 0;
            height: 0;
            border-top: 8px solid @FO-Container-Fill1;
            border-bottom: 8px solid @FO-Container-Fill1;
            border-left: 8px solid transparent;
          }
        }
      }
    }
  }
  &__download-btn {
    &-pro {
      border-color: #414141;
      background-color: #414141;
      color: #f8cc28;
      &:hover {
        border-color: #f8cc28 !important;
        background-color: #f8cc28 !important;
        color: #000 !important;
      }
      &:focus {
        border-color: #414141;
        background-color: #414141;
        color: #f8cc28;
      }
    }
  }
  &__btn {
    border-color: @forgeon-btn-normal-bg-color!important;
    background-color: @forgeon-btn-normal-bg-color !important;
    color: @forgeon-btn-normal-text-color !important;

    &-pro {
      display: inline-flex;
      align-items: center;
      border-color: #f8cc28 !important;
      background-color: #f8cc28 !important;
      color: #000 !important;
    }
  }
}

[data-theme='dark'] {
  .@{prefix-cls} {
    &__card-list-item {
      border-color: #303030;

      &:hover {
        border-color: #404040;
      }

      &-hoverable {
        &:not(:hover) {
          &::after {
            background: linear-gradient(to right, transparent 90%, #151515);
          }
        }
      }
    }
  }
}
</style>
