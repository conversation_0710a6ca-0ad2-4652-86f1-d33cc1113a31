import { createRequestService } from '@hg-tech/oasis-common';
import { useUserStoreWithOut } from '../../store/modules/user.ts';
import { handleForbidden, handleUnauthorized } from './helper.ts';

const userStore = useUserStoreWithOut();
export const authApiService = createRequestService(
  {
    authTokens: [
      {
        accessTokenKey: 'Access-Token',
        getAccessToken: () => userStore.getAccessToken,
      },
      {
        accessTokenKey: 'X-Token',
        getAccessToken: () => userStore.getToken,
        newTokenKey: 'new-token',
        setNewToken: userStore.setToken,
      },
    ],
    onUnauthorized: handleUnauthorized,
    onForbidden: handleForbidden,
  },
  {
    baseURL: import.meta.env.VITE_BASE_API_ORIGIN_PERMISSION_CENTER,
  },
);
