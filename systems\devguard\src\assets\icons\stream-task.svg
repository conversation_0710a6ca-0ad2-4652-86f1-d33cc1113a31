<?xml version="1.0" encoding="UTF-8"?>
<svg id="_图层_2" data-name="图层 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 281.58 281.58">
  <defs>
    <style>
      .cls-1 {
        fill: #333;
      }

      .cls-2 {
        fill: none;
      }

      .cls-3 {
        fill: #666;
      }
    </style>
  </defs>
  <g id="_图层_1-2" data-name="图层 1">
    <rect class="cls-2" width="281.58" height="281.58"/>
    <rect class="cls-3" x="151.52" y="44.81" width="134.1" height="33.99" rx="9.36" ry="9.36" transform="translate(416.83 -49.05) rotate(135)"/>
    <rect class="cls-3" x="194.73" y="61.33" width="80.73" height="33.99" rx="7.73" ry="7.73" transform="translate(345.95 299.95) rotate(-135)"/>
    <circle class="cls-1" cx="138.16" cy="142.55" r="61.34"/>
  </g>
</svg>